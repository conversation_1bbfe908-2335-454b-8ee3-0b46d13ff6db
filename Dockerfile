# Base image
FROM node:18-alpine AS base

# Dependencies stage
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app
# Copy only package.json since package-lock.json doesn't exist
COPY package.json ./
# Use npm install instead of npm ci
RUN npm install

# Builder stage
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Declare build argument for the API URL
ARG REACT_APP_API_URL
# Set the environment variable during the build process so npm run build can access it
ENV REACT_APP_API_URL=$REACT_APP_API_URL

RUN npm run build

# Runner stage
FROM nginx:alpine AS runner
WORKDIR /usr/share/nginx/html

# Remove default nginx static assets
RUN rm -rf ./*

# Copy built assets from builder stage
COPY --from=builder /app/build .

# Create nginx config directory if it doesn't exist
RUN mkdir -p /etc/nginx/conf.d

# Create a basic nginx config if one doesn't exist in the source
RUN echo 'server { \
    listen 80; \
    \
    location / { \
    root /usr/share/nginx/html; \
    index index.html index.htm; \
    try_files $uri $uri/ /index.html =404; \
    } \
    }' > /etc/nginx/conf.d/default.conf

# Make sure files belong to nginx user
RUN chown -R nginx:nginx /usr/share/nginx/html

# Cloud Run requires the container to listen on $PORT
ENV PORT=3000
# Update nginx config to use the environment variable
RUN sed -i.bak 's/listen\(.*\)80;/listen $PORT;/' /etc/nginx/conf.d/default.conf

# Use envsubst to replace $PORT in the Nginx configuration before starting Nginx
RUN mv /etc/nginx/conf.d/default.conf /etc/nginx/conf.d/default.conf.template
CMD envsubst '$PORT' < /etc/nginx/conf.d/default.conf.template > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'