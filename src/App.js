import React from "react";
import { Switch, Route, Redirect } from "react-router-dom";
import LoginPage from "./components/LoginPage";
import GSTNPage from "./components/GSTN";
import OnboardingPage from "./components/Onboarding";
import Dashboard from "./components/Dashboard"; // This is likely your MSME Dashboard
import Di<PERSON>lockerResponse from "./components/DigilockerResponse";
import DigilockerResponseInvoiceFinancing from "./components/DigilockerResponseInvoiceFinancing";
import InvoiceFinancingJourney from "./components/invoice-journey/InvoiceFinancingJourney";
import KycJourney from "./components/kyc-journey/kycJourney";
import { isAuthenticated } from "./api/auth"; // Keep isAuthenticated from auth.js
import Tnc from "./components/termsAndConditions";
import PrivacyPolicy from "./components/privacyPolicy";
import VerifyEmail from "./components/VerifyEmail";
import Eligibility<PERSON>hecker from "./components/EligibilityChecker";
import CreditLineContract from "./components/dashboard/CreditLineContract";
import UploadInvoicePage from "./components/dashboard/UploadInvoice";
import SupportTicketSubmissionPage from "./components/dashboard/SupportTickets";
import MainLayout from "./MainLayout";
import { UserProvider, useUser } from "./contexts/UserContext";
import BuyerRegistrationPage from "./components/BuyerRegistrationPage";
import BuyerDashboard from "./components/dashboard/BuyerDashboard";
import BuyerInvoiceVerificationPage from "./components/BuyerInvoiceVerificationPage";
import InvoiceVerificationPage from "./components/VerifyInvoice";
/**
 * Private Route component that checks if user is authenticated
 * and user type.
 * Redirects to login if not authenticated,
 * or to dashboard if an MSME user tries to access a buyer-only route.
 * Now wraps the Component with MainLayout.
 */
const PrivateRouteWithLayout = ({ component: Component, allowedUserTypes = ['msme', 'buyer'], ...rest }) => {
  const { userType } = useUser();

  return (
    <Route
      {...rest}
      render={(props) => {
        if (!isAuthenticated()) {
          return (
            <Redirect
              to={{
                pathname: "/login",
                state: { from: props.location }
              }}
            />
          );
        }

        if (!allowedUserTypes.includes(userType)) {
          console.warn(`Access Denied: User type '${userType}' not allowed for route '${props.location.pathname}'. Redirecting to dashboard.`);
          return (
            <Redirect
              to={{
                pathname: userType === 'buyer' ? "/buyer-dashboard" : "/dashboard",
                state: { from: props.location }
              }}
            />
          );
        }

        return (
          <MainLayout>
            <Component {...props} />
          </MainLayout>
        );
      }}
    />
  );
};

// New: A wrapper component to handle the initial redirect based on userType
const InitialRedirect = () => {
  const { userType, isBuyer, isMsme } = useUser();
  const authenticated = isAuthenticated();

  if (authenticated) {
    if (isBuyer) {
      return <Redirect to="/login" />;
    } else if (isMsme) {
      return <Redirect to="/login" />;
    }
    return <Redirect to="/login" />;
  }
  return <Redirect to="/login" />;
};


function App() {
  return (
    <UserProvider>
      <Switch>
        {/* Public routes that DO NOT use MainLayout */}
        <Route path="/login" component={LoginPage} />
        <Route path="/onboarding" component={OnboardingPage} />
        <Route path="/verify-email" component={VerifyEmail} />
        <Route path="/buyer-register" component={BuyerRegistrationPage} />

        {/* Public routes that MIGHT use MainLayout */}
        <Route path="/terms-and-conditions" component={Tnc} />
        <Route path="/privacy-policy" component={PrivacyPolicy} />

        {/* Protected routes that require authentication and use MainLayout */}
        {/* MSME Specific Routes */}
        <PrivateRouteWithLayout path="/documents" component={GSTNPage} allowedUserTypes={['msme']} />
        <PrivateRouteWithLayout path="/uploadInvoice" component={UploadInvoicePage} allowedUserTypes={['msme']} />
        <PrivateRouteWithLayout path="/eligibility-checker" component={EligibilityChecker} allowedUserTypes={['msme']} />
        <PrivateRouteWithLayout path="/kyc/:step?" component={KycJourney} allowedUserTypes={['msme']} />
        <PrivateRouteWithLayout path="/dashboard" component={Dashboard} allowedUserTypes={['msme']} />

        {/* Buyer Specific Routes */}
        <PrivateRouteWithLayout path="/buyer-dashboard" component={BuyerDashboard} allowedUserTypes={['buyer']} />
        <PrivateRouteWithLayout path="/buyer-invoices" component={BuyerInvoiceVerificationPage} allowedUserTypes={['buyer']} /> {/* NEW ROUTE */}
        <PrivateRouteWithLayout path="/buyer-invoice-verify/:id" component={InvoiceVerificationPage} allowedUserTypes={['buyer']} /> {/* NEW ROUTE */}
        {/* Routes accessible to both MSME and Buyer */}
        <PrivateRouteWithLayout path="/support" component={SupportTicketSubmissionPage} />
        <PrivateRouteWithLayout path="/creditLineContract/:financialInstitutionId" component={CreditLineContract} />
        <PrivateRouteWithLayout path="/digilockerResponse/" component={DigilockerResponse} />
        <PrivateRouteWithLayout path="/digilockerResponseInvoiceFinancing/" component={DigilockerResponseInvoiceFinancing} />
        <PrivateRouteWithLayout path="/invoiceContract/:step?" component={InvoiceFinancingJourney} />


        {/* Default route for authenticated users or redirect to login */}
        <Route exact path="/" component={InitialRedirect} />
        <Redirect to="/login" />
      </Switch>
    </UserProvider>
  );
}

export default App;