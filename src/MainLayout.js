import React, { useState, useEffect, createContext, useContext } from 'react';
import { useLocation } from 'react-router-dom';
import Header from './components/dashboard/Header';
import Sidebar from './components/dashboard/Sidebar';
import { useUser } from './contexts/UserContext'; // Import useUser hook

const SIDEBAR_WIDTH_DESKTOP = '250px';

// 1. Create the Context (This is now redundant as we have UserContext, but keep if MainLayout itself needs its own context for layout-specific states)
export const LayoutContext = createContext(null);

const MainLayout = ({ children }) => {
    const [currentPage, setCurrentPage] = useState('dashboard');
    const location = useLocation();
    const { userType } = useUser(); // Access userType from UserContext

    useEffect(() => {
        const path = location.pathname;
        let determinedPage = currentPage;

        if (path.startsWith('/dashboard')) {
            const dashboardSubPages = ['my-invoices', 'payments', 'settings', 'my-buyers'];
            if (path === '/dashboard' && !dashboardSubPages.includes(currentPage)) {
                determinedPage = 'dashboard';
            }
        } else if (path.startsWith('/kyc')) {
            determinedPage = 'onboarding';
        } else if (path.startsWith('/eligibility-checker')) {
            determinedPage = 'eligibility-check';
        } else if (path.startsWith('/support')) {
            determinedPage = 'support';
        } else if (path.startsWith('/buyer-dashboard')) { // New: Handle buyer dashboard
            determinedPage = 'buyer-dashboard';
        }

        if (determinedPage !== currentPage) {
            setCurrentPage(determinedPage);
        }
    }, [location.pathname, currentPage]);

    const hideSidebarAndHeader = location.pathname.includes('creditLineContract') || location.pathname.includes('invoiceContract');
    const isKycPage = location.pathname.includes('/kyc'); // <-- ADD THIS LINE

    const contextValue = {
        currentPage,
        setCurrentPage,
        hideSidebarAndHeader,
        // userType is already available via useUser hook if needed by children of MainLayout
    };

    return (
        <LayoutContext.Provider value={contextValue}>
            <div className="flex h-screen bg-gray-100 overflow-hidden">
                {!hideSidebarAndHeader && !isKycPage && ( // <-- MODIFY THIS LINE
                    <div
                        className="hidden md:flex md:flex-shrink-0 bg-[#edf5f7]"
                        style={{ width: SIDEBAR_WIDTH_DESKTOP }}
                    >
                        {/* Pass userType to Sidebar if its rendering depends on it */}
                        <Sidebar /> {/* Sidebar now consumes UserContext directly */}
                    </div>
                )}

                <div className="flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
                    {!hideSidebarAndHeader && (
                        // Header can also consume LayoutContext if needed
                        <Header />
                    )}

                    <main className="flex-1 bg-gray-100">
                        <div className="w-full h-full">
                            {children}
                        </div>
                    </main>
                </div>
            </div>
        </LayoutContext.Provider>
    );
};

export default MainLayout;

// Custom hook to consume the context easily (optional, but good practice)
export const useLayout = () => {
    const context = useContext(LayoutContext);
    if (!context) {
        throw new Error('useLayout must be used within a LayoutProvider (MainLayout)');
    }
    return context;
};