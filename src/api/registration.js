import config from '../config.json'; // Adjust path as necessary

const API_BASE_URL = config.apiUrl || ""; // Make sure apiUrl is defined in your config

export const sendBuyerInvitationEmail = async (msmeEmail, buyerEmail) => {
    const payload = {
        msmeEmail,
        buyerEmail,
    };

    try {
        const response = await fetch(`${API_BASE_URL}/ops/invoiceFinancing/send-buyer-invitation`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                // Add Authorization header if your API requires it
                // 'Authorization': `Bearer ${your_auth_token_here}`,
            },
            body: JSON.stringify(payload),
        });

        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.message || `Failed to send invitation to ${buyerEmail}`);
        }
        return result; // Should contain { success: true, message: "..." }
    } catch (error) {
        console.error(`Error sending invitation email to ${buyerEmail}:`, error);
        throw error; // Re-throw to be handled by the caller
    }
};