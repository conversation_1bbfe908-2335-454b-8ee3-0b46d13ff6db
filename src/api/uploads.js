import axios from 'axios';
import SharedCache from '../sharedCache';
import config from '../config.json';

/**
 * Process Bank Statement Upload
 * Handles uploading bank statement to the backend and processing it
 */
export async function processBankStatementUpload(file, panNo, gstin) {
    if (!file) throw new Error('No file provided');

    try {
        // Get user ID from SharedCache or localStorage
        const userId = SharedCache.get("user")?._id || localStorage.getItem("userId") || "default-user-id";

        // Create form data for the upload
        const formData = new FormData();
        formData.append('pdfFile', file);
        formData.append('panNo', panNo);
        formData.append('gstin', gstin);
        formData.append('userId', userId);

        // Mock data fields for bank statement processing
        // In a real system, these would come from actual PDF parsing
        formData.append('customerName', 'Test Customer');
        formData.append('customerId', `CID-${Math.floor(Math.random() * 10000)}`);
        formData.append('documentType', 'Bank Statement');
        formData.append('category', 'Financial');
        formData.append('locale', 'en');
        formData.append('totalAmount', (Math.random() * 100000 + 10000).toFixed(2));
        formData.append('confidence', '0.85');
        formData.append('processingTime', '1.2');

        // Create sample line items
        const mockLineItems = [
            {
                description: 'Opening Balance',
                totalAmount: (Math.random() * 50000 + 5000).toFixed(2),
                date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            },
            {
                description: 'Salary Credit',
                totalAmount: (Math.random() * 10000 + 3000).toFixed(2),
                date: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            },
            {
                description: 'Utility Payment',
                totalAmount: (Math.random() * 1000 + 200).toFixed(2),
                date: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            },
            {
                description: 'Bank Transfer',
                totalAmount: (Math.random() * 5000 + 1000).toFixed(2),
                date: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            },
            {
                description: 'ATM Withdrawal',
                totalAmount: (Math.random() * 2000 + 500).toFixed(2),
                date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            }
        ];

        formData.append('lineItems', JSON.stringify(mockLineItems));

        // Send to backend API
        const response = await axios.post(
            `${config.apiUrl}/ops/invoiceFinancing/uploadBankStatement`,
            formData,
            {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'x-auth-token': SharedCache.get("token") || localStorage.getItem("token") || ''
                }
            }
        );

        // Return the response data with success flag
        return {
            success: true,
            bankDetails: {
                customerName: 'Test Customer',
                customerId: `CID-${Math.floor(Math.random() * 10000)}`,
                documentType: 'Bank Statement',
                category: 'Financial',
                totalAmount: (Math.random() * 100000 + 10000).toFixed(2),
                lineItems: mockLineItems
            },
            response: response.data
        };

    } catch (error) {
        console.error('Error processing bank statement:', error);
        // Return error with details
        return {
            success: false,
            error: error.message || 'Failed to process bank statement'
        };
    }
}

/**
 * Process Invoice Upload
 * Handles uploading invoice to the backend and processing through Mindee API
 */
export async function processInvoiceUpload(file, panNo, gstin) {
    if (!file) throw new Error('No file provided');

    try {
        // Get user ID from SharedCache or localStorage
        const userId = SharedCache.get("user")?._id || localStorage.getItem("userId") || "default-user-id";

        // First, send the file to Mindee API for invoice processing
        const mindeeFormData = new FormData();
        mindeeFormData.append("document", file);

        const mindeeResponse = await axios.post(
            "https://api.mindee.net/v1/products/mindee/invoices/v4/predict",
            mindeeFormData,
            {
                headers: {
                    Authorization: "a22f7e4051a9178de0f37d3d7a49b17c",
                    "Content-Type": "multipart/form-data"
                }
            }
        );

        // Extract invoice details from Mindee API response
        const data = mindeeResponse.data.document.inference.prediction;
        console.log("Mindee API response:", data);

        // Extract invoice details
        const invoiceData = {
            panNo: panNo,
            gstin: gstin,
            invoiceNumber: data.invoice_number?.value || 'INV-' + Math.floor(Math.random() * 10000),
            invoiceDate: data.date?.value || new Date().toISOString().split('T')[0],
            dueDate: data.due_date?.value || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            totalAmount: data.total_amount?.value || (Math.random() * 10000 + 1000).toFixed(2),
            supplierName: data.supplier_name?.value || 'Test Supplier',
            customerName: data.customer_name?.value || 'Test Customer',
            billingAddress: data.billing_address?.value || 'Test Billing Address',
            customerAddress: data.customer_address?.value || 'Test Customer Address',
        };

        // Create form data for the backend upload
        const formData = new FormData();
        formData.append('pdfFile', file);

        // Append invoice details to FormData
        formData.append('panNo', panNo);
        formData.append('userId', userId);
        formData.append('gstin', gstin);
        formData.append('status', "pending");
        formData.append('invoiceNumber', invoiceData.invoiceNumber);
        formData.append('invoiceDate', invoiceData.invoiceDate);
        formData.append('dueDate', invoiceData.dueDate);
        formData.append('totalAmount', invoiceData.totalAmount);
        formData.append('supplierName', invoiceData.supplierName);
        formData.append('customerName', invoiceData.customerName);
        formData.append('billingAddress', invoiceData.billingAddress);
        formData.append('customerAddress', invoiceData.customerAddress);

        // Send to backend API
        const response = await axios.post(
            `${config.apiUrl}/ops/invoiceFinancing/uploadInvoice`,
            formData,
            {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'x-auth-token': SharedCache.get("token") || localStorage.getItem("token") || ''
                }
            }
        );

        console.log("Backend invoice upload response:", response.data);

        // Return the response data with success flag
        return {
            success: true,
            invoiceDetails: invoiceData,
            response: response.data
        };

    } catch (error) {
        console.error('Error processing invoice:', error);
        // If Mindee API fails, create mock data for testing
        if (error.message.includes('Mindee')) {
            const mockInvoiceData = {
                panNo: panNo,
                gstin: gstin,
                invoiceNumber: 'INV-' + Math.floor(Math.random() * 10000),
                invoiceDate: new Date().toISOString().split('T')[0],
                dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                totalAmount: (Math.random() * 10000 + 1000).toFixed(2),
                supplierName: 'Test Supplier',
                customerName: 'Test Customer',
                billingAddress: 'Test Billing Address',
                customerAddress: 'Test Customer Address',
            };

            return {
                success: true,
                invoiceDetails: mockInvoiceData,
                warning: "Used mock data due to Mindee API issue"
            };
        }

        // Return error with details
        return {
            success: false,
            error: error.message || 'Failed to process invoice'
        };
    }
}