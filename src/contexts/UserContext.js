// src/contexts/UserContext.js
import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';
import SharedCache from '../sharedCache';

export const UserContext = createContext(null);

export const UserProvider = ({ children }) => {
    // Initialize userType from SharedCache, defaulting to 'msme'
    const [userType, setUserTypeState] = useState(() => SharedCache.get('userType') || 'msme');

    // ADD THESE TWO STATE VARIABLES
    const [msmeProfile, setMsmeProfile] = useState(() => SharedCache.get('msmeUser'));
    const [buyerProfile, setBuyerProfile] = useState(() => SharedCache.get('buyerFullData'));

    // Effect to ensure UserContext's userType state is synchronized if SharedCache.userType
    // is changed by an external mechanism
    useEffect(() => {
        const cachedType = SharedCache.get('userType');
        if (cachedType && cachedType !== userType) {
            console.log(`UserContext Effect: Syncing userType state from SharedCache. Was: ${userType}, Now: ${cachedType}`);
            setUserTypeState(cachedType);
        }
    }, [userType]);

    // ADD THIS NEW useEffect FOR PROFILE SYNCHRONIZATION
    // This effect ensures that the UserProvider's internal state
    // (msmeProfile, buyerProfile) is kept in sync with SharedCache.
    // It will trigger a re-render of UserProvider when these values change in cache,
    // which then re-calculates isDualRoleUser.
    useEffect(() => {
        const currentMsme = SharedCache.get('msmeUser');
        const currentBuyer = SharedCache.get('buyerFullData');

        // Use a deep comparison for objects to avoid unnecessary re-renders
        // or ensure objects are only updated if their content truly changes.
        // A simple JSON.stringify is often sufficient for basic objects.
        if (JSON.stringify(currentMsme) !== JSON.stringify(msmeProfile)) {
            setMsmeProfile(currentMsme);
        }
        if (JSON.stringify(currentBuyer) !== JSON.stringify(buyerProfile)) {
            setBuyerProfile(currentBuyer);
        }
    }, [msmeProfile, buyerProfile]); // Dependency array: re-run if these internal states change


    const setUserType = useCallback((type) => {
        console.log(`UserContext: setUserType CALLED with type: ${type}`);

        setUserTypeState(type); // Update React local state
        SharedCache.set('userType', type); // Update SharedCache (and localStorage)

        // Set the active "user" object in SharedCache based on the new type
        if (type === 'buyer') {
            const buyerData = SharedCache.get('buyerFullData');
            if (buyerData) {
                SharedCache.set('user', buyerData);
                // ALSO UPDATE THE LOCAL REACT STATE OF PROFILES HERE
                setBuyerProfile(buyerData); // Crucial for immediate reactivity
                console.log("UserContext: Active user in SharedCache SET to BUYER profile.", buyerData);
            } else {
                console.warn("UserContext: Switching to Buyer, but 'buyerFullData' is missing in SharedCache. Active 'user' may be incorrect or unchanged.");
            }
        } else { // type === 'msme' or any other non-buyer type considered MSME-like
            const msmeData = SharedCache.get('msmeUser');
            if (msmeData) {
                SharedCache.set('user', msmeData);
                // ALSO UPDATE THE LOCAL REACT STATE OF PROFILES HERE
                setMsmeProfile(msmeData); // Crucial for immediate reactivity
                console.log("UserContext: Active user in SharedCache SET to MSME profile.", msmeData);
            } else {
                console.warn("UserContext: Switching to MSME, but 'msmeUser' is missing in SharedCache. Active 'user' may be incorrect or unchanged.");
            }
        }
        console.log("UserContext: After setUserType, SharedCache.get('user') is:", SharedCache.get("user"));
        console.log("UserContext: After setUserType, SharedCache.get('userType') is:", SharedCache.get("userType"));
        console.log("UserContext: After setUserType, SharedCache.get('msmeUser') is:", SharedCache.get("msmeUser"));
        console.log("UserContext: After setUserType, SharedCache.get('buyerFullData') is:", SharedCache.get("buyerFullData"));

    }, []); // No explicit dependencies needed for `setUserType` if it always reads fresh from SharedCache and uses stable setters

    const toggleUserType = useCallback(() => {
        const currentActiveUserType = SharedCache.get('userType'); // Get the definitive current type
        const newType = currentActiveUserType === 'msme' ? 'buyer' : 'msme';
        console.log(`UserContext: toggleUserType from ${currentActiveUserType} to ${newType}`);
        setUserType(newType); // This function will handle all necessary updates
    }, [setUserType]);

    // --- Start Critical Section for isDualRoleUser Calculation ---
    // USE THE NEW INTERNAL STATE VARIABLES FOR CALCULATION
    // This is the key: these now react to changes picked up by the useEffect
    const isDualRoleUser = !!(
        msmeProfile &&
        msmeProfile.email &&
        buyerProfile &&
        buyerProfile.email &&
        msmeProfile.email === buyerProfile.email
    );

    // **** ENHANCED LOGGING for debugging isDualRoleUser ****
    // This will log every time UserProvider renders
    console.log(
        `UserProvider RENDER (React userType state: ${userType}):
        msmeProfile from SharedCache: ${msmeProfile ? 'Exists' : 'Null'} (email: ${msmeProfile?.email}),
        buyerProfile from SharedCache: ${buyerProfile ? 'Exists' : 'Null'} (email: ${buyerProfile?.email})`
    );

    // Log the calculated value of isDualRoleUser
    console.log(`UserProvider RENDER: calculated isDualRoleUser = ${isDualRoleUser}`);
    // --- End Critical Section ---

    const contextValue = {
        userType,
        setUserType,
        toggleUserType,
        isBuyer: userType === 'buyer',
        isMsme: userType === 'msme',
        isDualRoleUser,
    };

    return (
        <UserContext.Provider value={contextValue}>
            {children}
        </UserContext.Provider>
    );
};

export const useUser = () => {
    const context = useContext(UserContext);
    if (!context) {
        throw new Error('useUser must be used within a UserProvider');
    }
    return context;
};