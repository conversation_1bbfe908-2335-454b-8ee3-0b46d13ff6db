import React, { useState } from 'react';
import { useParams, useHistory } from 'react-router-dom';
import Header from '../dashboard/Header';
import Footer from '../dashboard/Footer';
import InvoiceOffer from './steps/InvoiceOffer';
import PersonalDetails from './steps/PersonalDetails';
import ShopDetails from './steps/ShopDetails';
import AdditionalDetails from './steps/AdditionalDetails';
import ReviewDetails from './steps/ReviewDetails';

const InvoiceFinancingJourney = () => {
  const { step = 'invoice-offer' } = useParams();
  const history = useHistory();
  const [formData, setFormData] = useState({});

  const steps = [
    { id: 'invoice-offer', label: 'Invoice Offer' },
    // { id: 'personal-details', label: 'Personal Details' },
    // { id: 'shop-details', label: 'Shop Details' },
    // { id: 'additional-details', label: 'Additional Details' },
    // { id: 'review', label: 'Review' }
  ];

  const currentStepIndex = steps.findIndex(s => s.id === step);

  const handleNext = (stepData) => {
    setFormData(prev => ({ ...prev, ...stepData }));
    const nextStep = steps[currentStepIndex + 1];
    if (nextStep) {
      history.push(`/invoice-financing/${nextStep.id}`);
    }
  };

  const handleBack = () => {
    const prevStep = steps[currentStepIndex - 1];
    if (prevStep) {
      history.push(`/invoice-financing/${prevStep.id}`);
    }
  };

  const renderStep = () => {
    switch (step) {
      case 'invoice-offer':
        return <InvoiceOffer onNext={handleNext} data={formData} />;
      // case 'personal-details':
      //   return <PersonalDetails onNext={handleNext} onBack={handleBack} data={formData} />;
      // case 'shop-details':
      //   return <ShopDetails onNext={handleNext} onBack={handleBack} data={formData} />;
      // case 'additional-details':
      //   return <AdditionalDetails onNext={handleNext} onBack={handleBack} data={formData} />;
      // case 'review':
      //   return <ReviewDetails onNext={handleNext} onBack={handleBack} data={formData} />;
      // default:
      //   return <InvoiceOffer onNext={handleNext} data={formData} />;
    }
  };

  return (
    <div className="min-h-screen flex flex-col">

      {/* Step Progress Bar */}
      {/* <div className="bg-white shadow px-4 py-3">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center">
            {steps.map((s, index) => (
              <div key={s.id} className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-md ${index <= currentStepIndex ? 'bg-[#004141] text-white' : 'bg-gray-200'
                  }`}>
                  {index + 1}
                </div>
                <span className={`ml-2 ${index <= currentStepIndex ? 'text-[#004141]' : 'text-gray-500'
                  }`}>
                  {s.label}
                </span>
                {index < steps.length - 1 && (
                  <div className={`h-1 w-12 mx-2 ${index < currentStepIndex ? 'bg-[#004141]' : 'bg-gray-200'
                    }`} />
                )}
              </div>
            ))}
          </div>
        </div>
      </div> */}

      {/* Main Content */}
      <main className="">
        <div className="">
          {renderStep()}
        </div>
      </main>
    </div>
  );
};

export default InvoiceFinancingJourney;