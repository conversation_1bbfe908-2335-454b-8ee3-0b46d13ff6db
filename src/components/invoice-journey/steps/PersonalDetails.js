import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';

const PersonalDetails = ({ onNext, onBack }) => {
  const history = useHistory();
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);

  // Hardcoded form data matching our Global Oils Factory context
  const formData = {
    firstName: '<PERSON><PERSON>',
    middleName: '<PERSON>',
    lastName: 'Agarwal',
    addressLine1: '42, Industrial Area Phase I',
    addressLine2: 'Sector 5',
    addressLine3: '',
    city: 'Mumbai',
    state: 'Maharashtra',
    pincode: '400072',
    mobile: '9876543210',
    dob: '1980-05-15',
    panNumber: '**********',
    aadhaarNumber: '**************',
    gender: 'M',
    email: '<EMAIL>'
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Proceed to next step
      onNext(formData);
      history.push('/invoice-financing/shop-details');
    } catch (error) {
      setError('An error occurred. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h2 className="text-2xl font-bold mb-6">Personal Details</h2>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Personal Information */}
        <div className="bg-blue-50 p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-4">Basic Information</h3>
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">First Name</label>
              <input
                type="text"
                value={formData.firstName}
                readOnly
                className="w-full p-2 border rounded-md bg-gray-100"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Middle Name</label>
              <input
                type="text"
                value={formData.middleName}
                readOnly
                className="w-full p-2 border rounded-md bg-gray-100"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Last Name</label>
              <input
                type="text"
                value={formData.lastName}
                readOnly
                className="w-full p-2 border rounded-md bg-gray-100"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Date of Birth</label>
              <input
                type="date"
                value={formData.dob}
                readOnly
                className="w-full p-2 border rounded-md bg-gray-100"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Gender</label>
              <input
                type="text"
                value={formData.gender === 'M' ? 'Male' : formData.gender === 'F' ? 'Female' : formData.gender}
                readOnly
                className="w-full p-2 border rounded-md bg-gray-100"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Mobile Number</label>
              <input
                type="text"
                value={formData.mobile}
                readOnly
                className="w-full p-2 border rounded-md bg-gray-100"
              />
            </div>

            <div className="col-span-2">
              <label className="block text-sm font-medium mb-1">Email</label>
              <input
                type="email"
                value={formData.email}
                readOnly
                className="w-full p-2 border rounded-md bg-gray-100"
              />
            </div>
          </div>
        </div>

        {/* KYC Information */}
        <div className="bg-green-50 p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-4">KYC Information</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">PAN Number</label>
              <input
                type="text"
                value={formData.panNumber}
                readOnly
                className="w-full p-2 border rounded-md bg-gray-100"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Aadhaar Number</label>
              <input
                type="text"
                value={formData.aadhaarNumber}
                readOnly
                className="w-full p-2 border rounded-md bg-gray-100"
              />
            </div>
          </div>
        </div>

        {/* Address Information */}
        <div className="bg-gray-50 p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-4">Current Address</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Address Line 1</label>
              <input
                type="text"
                value={formData.addressLine1}
                readOnly
                className="w-full p-2 border rounded-md bg-gray-100"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Address Line 2</label>
              <input
                type="text"
                value={formData.addressLine2}
                readOnly
                className="w-full p-2 border rounded-md bg-gray-100"
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">City</label>
                <input
                  type="text"
                  value={formData.city}
                  readOnly
                  className="w-full p-2 border rounded-md bg-gray-100"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">State</label>
                <input
                  type="text"
                  value={formData.state}
                  readOnly
                  className="w-full p-2 border rounded-md bg-gray-100"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Pincode</label>
                <input
                  type="text"
                  value={formData.pincode}
                  readOnly
                  className="w-full p-2 border rounded-md bg-gray-100"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Verification Status */}
        <div className="bg-blue-50 p-4 rounded-md">
          <div className="flex items-center">
            <svg className="w-6 h-6 text-[#208039] mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
            </svg>
            <span className="text-sm text-gray-700">All details have been verified through DigiLocker</span>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 p-4 rounded-md text-red-500 text-sm">
            {error}
          </div>
        )}

        <div className="flex justify-between pt-6">
          <button
            type="button"
            onClick={() => history.push('/invoice-financing/invoice-offer')}
            className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Back
          </button>
          <button
            type="submit"
            disabled={submitting}
            className={`px-6 py-2 ${submitting
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-[#004141]  '
              } text-white rounded-md`}
          >
            {submitting ? 'Processing...' : 'Next'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default PersonalDetails;