import React from 'react';
import { useHistory } from 'react-router-dom';

const AdditionalDetails = ({ onNext, onBack }) => {
  const history = useHistory();

  // Hardcoded data matching our business owner's profile
  const formData = {
    hasWhatsapp: 'Yes',
    gender: 'Male',
    maritalStatus: 'Married',
    hasChildren: 'Yes',
    homeOwnership: 'Own',
    hasVehicle: 'Yes',
    monthlyExpenses: '250000',
    monthlyIncome: '1500000',
    sectorExperience: 'Private',
    recentPurchases: 'Yes',
    education: 'Post Graduation',
    referralName: ''
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onNext(formData);
    history.push('/invoice-financing/review');
  };

  const formatCurrency = (amount) => {
    return `QAR ${Number(amount).toLocaleString('en-IN')}`;
  };

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Additional Details</h2>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Personal Information */}
        <div className="bg-blue-50 p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-4">Personal Information</h3>
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                WhatsApp Available
              </label>
              <input
                type="text"
                value={formData.hasWhatsapp}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Gender
              </label>
              <input
                type="text"
                value={formData.gender}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Marital Status
              </label>
              <input
                type="text"
                value={formData.maritalStatus}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
            </div>
          </div>
        </div>

        {/* Family & Assets */}
        <div className="bg-gray-50 p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-4">Family & Assets</h3>
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Children
              </label>
              <input
                type="text"
                value={formData.hasChildren}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Home Ownership
              </label>
              <input
                type="text"
                value={formData.homeOwnership}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Vehicle Ownership
              </label>
              <input
                type="text"
                value={formData.hasVehicle}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
            </div>
          </div>
        </div>

        {/* Financial Information */}
        <div className="bg-green-50 p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-4">Financial Information</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Monthly Expenses
              </label>
              <input
                type="text"
                value={formatCurrency(formData.monthlyExpenses)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Monthly Income
              </label>
              <input
                type="text"
                value={formatCurrency(formData.monthlyIncome)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
            </div>
          </div>
        </div>

        {/* Educational & Other Details */}
        <div className="bg-gray-50 p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-4">Educational & Other Details</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Recent Major Purchases
              </label>
              <input
                type="text"
                value={formData.recentPurchases}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Education Level
              </label>
              <input
                type="text"
                value={formData.education}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
            </div>
          </div>
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between pt-6">
          <button
            type="button"
            onClick={() => history.push('/invoice-financing/shop-details')}
            className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Back
          </button>
          <button
            type="submit"
            className="px-6 py-2 bg-[#004141] text-white rounded-md  "
          >
            Next
          </button>
        </div>
      </form>
    </div>
  );
};

export default AdditionalDetails;