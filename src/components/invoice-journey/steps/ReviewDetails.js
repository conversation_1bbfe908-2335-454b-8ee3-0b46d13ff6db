import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';

const ReviewDetails = ({ onNext, onBack }) => {
  const history = useHistory();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [agreed, setAgreed] = useState(false);
  const [error, setError] = useState('');

  // Hardcoded data combining all previous steps
  const allDetails = {
    invoice: {
      invoiceNumber: '7295',
      totalAmount: '87000',
      invoiceDate: '2024-12-16',
      dueDate: '2025-01-15',
      supplierName: 'GLOBAL OILS FACTORY',
      customerName: 'BIG TRADERS',
      description: 'Raw Material Supply',
      reference: 'PO-2024-001'
    },
    personal: {
      firstName: 'Rajesh',
      middleName: 'Kumar',
      lastName: 'Agarwal',
      addressLine1: '42, Industrial Area Phase I',
      addressLine2: 'Sector 5',
      city: 'Mumbai',
      state: 'Maharashtra',
      pincode: '400072',
      mobile: '9876543210',
      dob: '1980-05-15',
      panNumber: '**********',
      aadhaarNumber: '**************',
      email: '<EMAIL>'
    },
    business: {
      businessName: 'Global Oils Factory',
      type: 'Manufacturing Unit',
      sector: 'Food & Beverages',
      ownership: 'Owned-Self',
      gstNumber: '27**********1ZV',
      udyamNumber: 'UDYAM-MH-18-0087654',
      yearEstablished: '2015',
      monthlyTurnover: 'QAR 15,00,000'
    },
    additional: {
      monthlyIncome: 'QAR 15,00,000',
      monthlyExpenses: 'QAR 2,50,000',
      education: 'Post Graduation',
      maritalStatus: 'Married',
      homeOwnership: 'Own',
      vehicleOwnership: 'Yes'
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!agreed) {
      setError('Please agree to the terms and conditions');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Navigate back to My Invoices with updated status
      history.push('/dashboard/my-invoices', {
        success: true,
        status: 'APPLIED_FOR_DISCOUNTING'
      });
    } catch (error) {
      setError('Failed to submit application. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderSection = (title, details) => (
    <div className="bg-gray-50 p-4 rounded-md">
      <h3 className="text-lg font-semibold mb-4">{title}</h3>
      <div className="grid grid-cols-2 gap-4">
        {Object.entries(details).map(([key, value]) => (
          <div key={key} className="space-y-1">
            <p className="text-sm text-gray-600">{key}</p>
            <p className="font-medium">{value}</p>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Review Your Application</h2>

      <div className="space-y-6">
        {/* Invoice Details */}
        <div className="bg-blue-50 p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-4">Invoice Details</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600">Invoice Number</p>
              <p className="font-medium">{allDetails.invoice.invoiceNumber}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Amount</p>
              <p className="font-medium">QAR {Number(allDetails.invoice.totalAmount).toLocaleString('en-IN')}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Invoice Date</p>
              <p className="font-medium">{allDetails.invoice.invoiceDate}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Due Date</p>
              <p className="font-medium">{allDetails.invoice.dueDate}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Supplier Name</p>
              <p className="font-medium">{allDetails.invoice.supplierName}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Customer Name</p>
              <p className="font-medium">{allDetails.invoice.customerName}</p>
            </div>
          </div>
        </div>

        {/* Personal Details */}
        <div className="bg-green-50 p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-4">Personal Details</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600">Full Name</p>
              <p className="font-medium">
                {`${allDetails.personal.firstName} ${allDetails.personal.middleName} ${allDetails.personal.lastName}`}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Mobile</p>
              <p className="font-medium">{allDetails.personal.mobile}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Email</p>
              <p className="font-medium">{allDetails.personal.email}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Date of Birth</p>
              <p className="font-medium">{allDetails.personal.dob}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">PAN Number</p>
              <p className="font-medium">{allDetails.personal.panNumber}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Aadhaar Number</p>
              <p className="font-medium">{allDetails.personal.aadhaarNumber}</p>
            </div>
          </div>
        </div>

        {/* Business Details */}
        <div className="bg-gray-50 p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-4">Business Details</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600">Business Name</p>
              <p className="font-medium">{allDetails.business.businessName}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Business Type</p>
              <p className="font-medium">{allDetails.business.type}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Sector</p>
              <p className="font-medium">{allDetails.business.sector}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">GST Number</p>
              <p className="font-medium">{allDetails.business.gstNumber}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Udyam Number</p>
              <p className="font-medium">{allDetails.business.udyamNumber}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Monthly Turnover</p>
              <p className="font-medium">{allDetails.business.monthlyTurnover}</p>
            </div>
          </div>
        </div>

        {/* Financial Information */}
        <div className="bg-yellow-50 p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-4">Financial Information</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600">Monthly Income</p>
              <p className="font-medium">{allDetails.additional.monthlyIncome}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Monthly Expenses</p>
              <p className="font-medium">{allDetails.additional.monthlyExpenses}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Education</p>
              <p className="font-medium">{allDetails.additional.education}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Home Ownership</p>
              <p className="font-medium">{allDetails.additional.homeOwnership}</p>
            </div>
          </div>
        </div>

        {/* Terms and Conditions */}
        <div className="border-t pt-6">
          <div className="flex items-start space-x-2">
            <input
              type="checkbox"
              id="terms"
              checked={agreed}
              onChange={(e) => setAgreed(e.target.checked)}
              className="mt-1"
            />
            <label htmlFor="terms" className="text-sm text-gray-600">
              I hereby declare that all the information provided above is true and accurate to the best of my knowledge.
              I understand that providing false information may result in the rejection of my application and other
              legal consequences.
            </label>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 p-4 rounded-md text-red-500 text-sm">
            {error}
          </div>
        )}

        <div className="flex justify-between pt-6">
          <button
            type="button"
            onClick={() => history.push('/invoice-financing/additional-details')}
            disabled={isSubmitting}
            className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Back
          </button>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting || !agreed}
            className={`px-6 py-2 rounded-md ${isSubmitting || !agreed
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-[#004141]  '
              } text-white flex items-center`}
          >
            {isSubmitting ? (
              <>
                <svg className="animate-spin h-5 w-5 mr-2" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z" />
                </svg>
                Processing...
              </>
            ) : (
              'Submit Application'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReviewDetails;