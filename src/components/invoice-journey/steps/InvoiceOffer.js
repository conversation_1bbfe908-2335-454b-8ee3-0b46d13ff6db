import React, { useState, useEffect } from 'react';
import { useHistory, useLocation } from 'react-router-dom';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Button } from 'react-bootstrap';
import axios from 'axios';
import config from '../../../config.json';
import SharedCache from '../../../sharedCache';
import { getKycInfo } from '../../../api/kyc';
import { fetchInvoiceById } from '../../../api/auth';
import Modal from 'react-modal'; // For document/consent modals
import LoadingModal from '../../Reusable/Loading';
import Header from '../../dashboard/Header';

const InvoiceOffer = () => {
  const [agreed, setAgreed] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isConsentModalOpen, setIsConsentModalOpen] = useState(false);
  const [signature, setSignature] = useState('');
  const [additionalComments, setAdditionalComments] = useState('');
  const [error, setError] = useState('');
  const [invoiceDetails, setInvoiceDetails] = useState(null);
  const [acceptedOffer, setAcceptedOffer] = useState(null);
  const [pdfUrl, setPdfUrl] = useState('');
  const [kycInfo, setKycInfo] = useState({});
  const [contractData, setContractData] = useState({
    otp: '123456',
  });
  const [showDocConsent, setShowDocConsent] = useState(false);
  const [docConsent, setDocConsent] = useState(false);
  const [disbursalConsent, setDisbursalConsent] = useState(false);
  const [contractErrors, setContractErrors] = useState({});
  const [showContractModal, setShowContractModal] = useState(false);
  const [contractGenerated, setContractGenerated] = useState(false);
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [staticData, setStaticData] = useState(null);
  const [creditLineDetails, setCreditLineDetails] = useState(null);
  const [otpCooldown, setOtpCooldown] = useState(0);
  const [isOtpResendDisabled, setIsOtpResendDisabled] = useState(false);
  const history = useHistory();
  const location = useLocation();
  const user = SharedCache.get('user');
  const token = SharedCache.get('token');

  // Navigation handlers for Terms and Privacy Policy
  const handleTermsClick = () => {
    history.push('/terms-and-conditions');
  };

  const handlePrivacyClick = () => {
    history.push('/privacy-policy');
  };

  const closeConsentModal = () => setIsConsentModalOpen(false);
  const openConsentModal = () => setIsConsentModalOpen(true);

  useEffect(() => {
    let timerId;
    if (otpCooldown > 0) {
      setIsOtpResendDisabled(true); // Ensure disabled while counting down
      timerId = setInterval(() => {
        setOtpCooldown((prevCooldown) => {
          if (prevCooldown <= 1) {
            clearInterval(timerId); // Clear interval when reaching 0
            setIsOtpResendDisabled(false); // Enable button
            return 0;
          }
          return prevCooldown - 1;
        });
      }, 1000);
    } else {
      setIsOtpResendDisabled(false); // Ensure button is enabled if cooldown is 0 initially
    }

    // Cleanup function
    return () => {
      if (timerId) {
        clearInterval(timerId);
      }
    };
  }, [otpCooldown]);

  const handleResendOtp = async () => {
    if (isOtpResendDisabled) return; // Do nothing if button is disabled

    console.log("Resending OTP logic triggered...");
    // ---!!! IMPORTANT: Add your API call to resend OTP here !!!---
    // Example placeholder:
    // try {
    //   setIsLoading(true); // Optional: show loading state
    //   // Replace with your actual API endpoint and payload
    //   await axios.post(`${config.apiUrl}/auth/resend-otp`, { identifier: user.mobileNo || user.email });
    //   console.log("OTP Resend API call successful");
    //   setOtpCooldown(30); // Start cooldown only on successful API call
    // } catch (apiError) {
    //   console.error("Failed to resend OTP:", apiError);
    //   setError("Failed to resend OTP. Please try again."); // Show error to user
    // } finally {
    //   setIsLoading(false); // Optional: hide loading state
    // }
    // --- End of Placeholder ---

    // For testing UI without API call, uncomment the line below:
    setOtpCooldown(30); // Start 30-second cooldown
  };

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const invoiceId = searchParams.get('id');

    const fetchInvoice = async () => {
      setIsLoading(true);
      try {
        const data = await fetchInvoiceById(invoiceId);
        console.log("DATA HERE", data)
        setInvoiceDetails(data._doc);
        console.log(invoiceDetails, "oadoakdaokdoakd")
        console.log('INVOICE DATA', data);
      } catch (err) {
        setError('Failed to load invoice details.');
        console.error('Error fetching invoice:', err);
      } finally {
        setIsLoading(false);
      }
    };

    const fetchAcceptedOffer = async () => {
      setIsLoading(true);
      try {
        const response = await axios.get(
          `${config.apiUrl}/ops/invoiceFinancing/offerByInvoice/${invoiceId}`,
          {
            headers: {
              'x-auth-token': token,
            },
          }
        );
        setAcceptedOffer(response.data.offer);
        console.log('OFFER DATA', response.data);
        if (response.data.offer?.invoiceContract?.signedUrl) {
          setPdfUrl(response.data.offer?.invoiceContract?.signedUrl);
          setContractGenerated(true);
        }
      } catch (err) {
        setError('Failed to load accepted offer.');
        console.error('Error fetching accepted offer:', err);
      } finally {
        setIsLoading(false);
      }
    };

    const fetchKycInfo = async () => {
      setIsLoading(true);
      try {
        const response = await getKycInfo(user._id || user.id);
        setKycInfo(response);
        console.log('ALL USER DATA', response);
      } catch (err) {
        setError('Failed to load user info.');
        console.error('Error fetching user info:', err);
      } finally {
        setIsLoading(false);
      }
    };

    const fetchCreditLine = async () => {
      setIsLoading(true);
      try {
        const response = await axios.get(
          `${config.apiUrl}/ops/invoiceFinancing/fetchCreditLine/${user._id ||
          user.id}`,
          {
            headers: {
              'x-auth-token': token,
            },
          }
        );
        setCreditLineDetails(response.data);
        console.log('CREDIT LINE DATA', response.data);
      } catch (err) {
        // Handle 404 gracefully (credit line might not exist yet)
        if (err.response && err.response.status === 404) {
          setCreditLineDetails(null);
          console.log('Credit line not found for user.');
        } else {
          setError('Failed to load credit line.');
          console.error('Error fetching credit line:', err);
        }
      } finally {
        setIsLoading(false);
      }
    };

    if (invoiceId) {
      fetchInvoice();
    } else {
      setError('Invoice ID not provided.');
    }

    fetchKycInfo();
    fetchAcceptedOffer();
    fetchCreditLine();
  }, [location.search, user._id || user.id, token]);

  useEffect(() => {
    // Calculate and log static data when invoiceDetails, acceptedOffer, and creditLineDetails are available
    // Uses Compound Interest (PMT) for EMI calculation and schedule generation, inspired by reference logic.
    if (invoiceDetails && acceptedOffer && creditLineDetails && kycInfo) {
      try {
        // --- 1. Extract and Parse Input Data ---
        const amount = parseFloat(invoiceDetails.totalAmount); // Original Invoice Amount
        const discountedPercentage = parseFloat(acceptedOffer.invoiceDiscountingPercentage);
        const tenureDays = parseInt(acceptedOffer.tenureDays, 10);
        const interestRate = parseFloat(creditLineDetails.interestRate); // Annual Percentage Rate (APR) % (e.g., 3 for 3%)
        const invoiceDate = new Date(invoiceDetails.invoiceDate);
        const dateOfSigning = new Date(); // Use current date as signing/calculation start date
        const emiRepaymentFrequency = acceptedOffer.emiRepaymentFrequency || 'MONTHLY'; // Default if missing
        const principalAmount = parseFloat((amount * (discountedPercentage / 100)).toFixed(2)); // This is the actual loan principal

        // --- 2. Calculate Processing Charges ---
        const feeValue = acceptedOffer.processingFee ? parseFloat(acceptedOffer.processingFee) : 0;
        const processingCharges =
          acceptedOffer.processingFeeType === 'percentage'
            ? (principalAmount * (feeValue / 100)).toFixed(2)
            : feeValue.toFixed(2);

        console.log('--- Calculation Inputs ---', {
          totalAmount: amount,
          discountedPercentage: discountedPercentage,
          discountedAmount_Principal: principalAmount,
          tenureDays: tenureDays,
          processingFeeType: acceptedOffer.processingFeeType,
          processingFeeValue: feeValue,
          annualInterestRatePercent: interestRate,
          invoiceDate: invoiceDate.toISOString().split('T')[0],
          dateOfSigning: dateOfSigning.toISOString().split('T')[0],
          emiRepaymentFrequency: emiRepaymentFrequency,
        });

        // --- 3. Determine EMI Periodicity (Interval in Days) and Count ---
        let emiIntervalDays = 30; // Default to monthly approximation
        switch (emiRepaymentFrequency.toUpperCase()) {
          case 'WEEKLY':
            emiIntervalDays = 7;
            break;
          case 'DAILY':
            emiIntervalDays = 1;
            break;
          case 'MONTHLY':
          default:
            emiIntervalDays = 30; // Keep simple 30-day approx for monthly consistency
            break;
        }
        const emiCount = (tenureDays > 0 && emiIntervalDays > 0) ? Math.ceil(tenureDays / emiIntervalDays) : 0;

        if (emiCount <= 0) {
          throw new Error("Cannot calculate EMI schedule with zero or invalid tenure/frequency.");
        }

        // --- 4. Calculate Periodic Interest Rate (for Compound Interest) ---
        const annualRateDecimal = interestRate / 100; // Annual Percentage Rate (APR)
        const dailyInterestRate = annualRateDecimal / 365;
        const periodicInterestRate = dailyInterestRate * emiIntervalDays;

        // --- 5. Calculate Standard EMI using Compound Interest Formula (PMT) ---
        let calculatedEmi = 0;
        if (principalAmount > 0 && emiCount > 0) {
          if (periodicInterestRate > 0 && interestRate > 0) {
            const ratePowerN = Math.pow(1 + periodicInterestRate, emiCount);
            calculatedEmi = principalAmount * (periodicInterestRate * ratePowerN) / (ratePowerN - 1);
          } else {
            calculatedEmi = principalAmount / emiCount;
          }
        }
        const standardEmiAmount = parseFloat(calculatedEmi.toFixed(2));

        // --- 6. Initialize Output Data Structure ---
        const calculatedData = {
          amount: amount.toFixed(2),
          discountedPercentage: discountedPercentage.toFixed(2),
          discountedAmount: principalAmount.toFixed(2),
          processingCharges: processingCharges,
          tenure: tenureDays,
          emiAmount: standardEmiAmount.toFixed(2),
          emiCount: emiCount,
          borrowingPartner: invoiceDetails.supplierName || 'N/A',
          disbursalType: 'Partner',
          totalInterest: '0.00',
          totalPayableAmount: '0.00',
          interestRate: interestRate.toFixed(2),
          emiSchedule: [], // Initialize as empty
          invoiceDate: invoiceDate.toISOString().split('T')[0],
          dateOfSigning: dateOfSigning.toISOString().split('T')[0],
          borrowerName: `${kycInfo?.user?.firstName || ''} ${kycInfo?.user?.middleName || ''} ${kycInfo?.user?.lastName || ''}`.trim() || 'N/A',
          borrowerAddress: `${kycInfo?.user?.kyc?.addressLine1 || ''} ${kycInfo?.user?.kyc?.addressLine2 || ''}`.trim() || 'N/A',
          mobileNo: kycInfo?.user?.mobileNo || '',
          email: kycInfo?.user?.email || '',
        };

        // --- 7. Populate EMI Schedule including Due Dates ---
        let remainingPrincipal = principalAmount;
        let accumulatedInterest = 0;

        for (let i = 1; i <= emiCount; i++) {
          const interestForPeriod = remainingPrincipal * periodicInterestRate;

          let paymentAmountForThisPeriod;
          let principalRecovered;

          if (i < emiCount) {
            paymentAmountForThisPeriod = standardEmiAmount;
            principalRecovered = paymentAmountForThisPeriod - interestForPeriod;

            if (principalRecovered < 0) principalRecovered = 0;
            if (principalRecovered > remainingPrincipal) principalRecovered = remainingPrincipal;

          } else { // Final Installment
            principalRecovered = remainingPrincipal;
            paymentAmountForThisPeriod = principalRecovered + interestForPeriod;
          }

          remainingPrincipal -= principalRecovered;
          accumulatedInterest += interestForPeriod;

          // --- Calculate Due Date for this EMI ---
          // Create a new date object based on the signing date for each calculation
          const emiDueDate = new Date(dateOfSigning);
          // Add the total number of days for *this* installment's due date
          // (i * interval, e.g., 1*30, 2*30, 3*30)
          emiDueDate.setDate(dateOfSigning.getDate() + i * emiIntervalDays);
          const formattedDueDate = emiDueDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
          // --- End Date Calculation ---

          calculatedData.emiSchedule.push({
            emiNumber: i,
            emiDueDate: formattedDueDate, // <-- ADDED DUE DATE
            rePaymentAmount: paymentAmountForThisPeriod.toFixed(2),
            interestAmount: interestForPeriod.toFixed(2),
            principalRecovered: principalRecovered.toFixed(2),
            principalOutstanding: (i === emiCount ? 0 : remainingPrincipal).toFixed(2),
          });
        }

        // --- 8. Finalize Total Interest and Total Payable Amount ---
        calculatedData.totalInterest = accumulatedInterest.toFixed(2);
        calculatedData.totalPayableAmount = (principalAmount + accumulatedInterest).toFixed(2);

        // --- 9. Log Results and Set State ---
        console.log('--- STATIC DATA (Compound Interest Method with Dates) ---', calculatedData);
        setStaticData(calculatedData);

      } catch (error) {
        console.error('Error calculating static data:', error);
        setStaticData(null);
      }
    } else {
      setStaticData(null);
    }
  }, [invoiceDetails, acceptedOffer, creditLineDetails, kycInfo]); // Dependencies
  useEffect(() => {
    setIsButtonDisabled(
      !docConsent ||
      !disbursalConsent ||
      !signature.trim() ||
      !agreed
    );
  }, [docConsent, disbursalConsent, signature, agreed]);

  const handleDocConsent = () => {
    setDocConsent(!docConsent);
  };

  const handleDisbursalConsent = () => {
    setDisbursalConsent(!disbursalConsent);
  };

  const handleShowContract = async (e) => {
    e.preventDefault();

    const newErrors = {};

    if (!docConsent) newErrors.docConsent = 'You must agree to the document consent';
    if (!disbursalConsent) newErrors.disbursalConsent = 'You must agree to the disbursal consent';
    if (!signature.trim()) newErrors.signature = 'Please provide your signature';
    if (!agreed) newErrors.agreed = 'Please agree to the terms and conditions';

    if (Object.keys(newErrors).length > 0) {
      setContractErrors(newErrors);
      return;
    }

    if (contractGenerated) {
      setShowContractModal(true);
      return;
    }

    setIsLoading(true);
    setError('');
    setContractErrors({});

    try {
      const userId = user._id || user.id || '';
      const response = await axios.post(
        `${config.apiUrl}/ops/invoiceFinancing/generateLoanContract`,
        {
          userId: userId,
          invoiceId: invoiceDetails?._id || '',
          lenderName: 'Dummy Tech',
          borrowerName: staticData?.borrowerName || `${user?.firstName || ''} ${user?.middleName || ''} ${user?.lastName || ''}`,
          lenderAddress: 'Dummy Lender Address',
          borrowerAddress: staticData?.borrowerAddress || `${kycInfo?.user?.kyc?.addressLine1 || ''} ${kycInfo?.user?.kyc?.addressLine2 || ''}`,
          invoiceNumber: invoiceDetails?.invoiceNumber || '',
          invoiceDate: staticData?.invoiceDate || invoiceDetails?.invoiceDate || new Date().toISOString().split('T')[0],
          totalAmount: invoiceDetails?.totalAmount || 0,
          discountedAmount: staticData?.discountedAmount || 0,
          processingFee: staticData?.processingCharges || 0,
          tenure: staticData?.tenure || 0,
          emiAmount: staticData?.emiAmount || 0,
          emiCount: staticData?.emiCount || 0,
          dateOfSigning: staticData?.dateOfSigning || new Date().toISOString().split('T')[0],
          additionalComments: additionalComments,
          signature: signature,
          otp: contractData.otp,
          interestRate: staticData?.interestRate || 0,
          totalInterest: staticData?.totalInterest || 0,
          totalPayableAmount: staticData?.totalPayableAmount || 0,
          emiSchedule: staticData?.emiSchedule || [],
          emiRepaymentFrequency: acceptedOffer?.emiRepaymentFrequency || 'MONTHLY',
          name: staticData?.borrowerName || `${user?.firstName || ''} ${user?.middleName || ''} ${user?.lastName || ''}`,
          phone: staticData?.mobileNo || '',
          emailId: staticData?.email || '',

        },
        { headers: { 'x-auth-token': token } }
      );

      setPdfUrl(response.data.signedUrl);
      setShowContractModal(true);
      setContractGenerated(true);
    } catch (apiError) {
      setError('Failed to generate loan contract.');
      console.log('Error generating loan contract:', apiError);
    } finally {
      setIsLoading(false);
    }
  };

  const handleComplete = async () => {
    setShowContractModal(false);
    setIsLoading(true)
    // --- Frontend Pre-checks (Recommended) ---
    if (!acceptedOffer || !acceptedOffer._id) {
      console.error("handleComplete Error: Missing acceptedOffer details (_id).");
      alert("Cannot proceed: Offer details missing.");
      return;
    }
    if (!pdfUrl) {
      console.error("handleComplete Error: Missing pdfUrl.");
      alert("Cannot proceed: PDF URL missing.");
      return;
    }
    if (!invoiceDetails || !invoiceDetails._id) {
      console.error("handleComplete Error: Missing invoiceDetails details (_id).");
      alert("Cannot proceed: Invoice details missing.");
      return;
    }
    // --- End Pre-checks ---

    try {
      // --- Payload remains the same ---
      const contractAcceptancePayload = {
        status: 'LOAN_CONTRACT_ACCEPTED',
        invoiceContract: {
          filePath: '', // Or relevant path
          signedUrl: pdfUrl, // The generated PDF URL
          uploadedOn: new Date(), // Current time
          expiresOn: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Example: 7 days expiry
          mimeType: 'application/pdf',
        },
      };
      console.log("Sending Payload to NEW /acceptContract route:", JSON.stringify(contractAcceptancePayload, null, 2));

      // --- Call the NEW backend route ---
      await axios.put(
        // *** IMPORTANT: Use the new URL path ***
        `${config.apiUrl}/ops/invoiceFinancing/offers/${acceptedOffer._id}/acceptContract`,
        contractAcceptancePayload,
        // *** NO Authentication Header needed for this specific route ***
      );
      console.log("Offer contract acceptance update successful via new route.");

      // --- Update Invoice Status (Second API call) ---
      // Assuming this route might also not need auth now? Adjust if needed.
      const invoiceUpdatePayload = {
        status: 'READY_FOR_DISBURSAL',
        appliedForDiscountingAt: new Date(),
      };
      console.log("Sending Payload to updateInvoice route:", JSON.stringify(invoiceUpdatePayload, null, 2));

      await axios.put(
        `${config.apiUrl}/ops/invoiceFinancing/updateInvoice/${invoiceDetails._id}`,
        invoiceUpdatePayload
        // Remove auth header here too if this route was also modified to remove auth
        // { headers: { 'x-auth-token': token } }
      );
      console.log("Invoice status update successful.");


      history.push('/dashboard'); // Navigate on success

    } catch (error) {
      // Log and display error details
      const errorMsg = error.response ? JSON.stringify(error.response.data) : error.message;
      const errorStatus = error.response ? error.response.status : 'N/A';
      console.error(`Error during handleComplete (API Status: ${errorStatus}):`, errorMsg, error);
      alert(`Operation failed. Status: ${errorStatus}, Message: ${error.response?.data?.message || error.message}`);
    } finally {
      setIsLoading(false)
    }
  };

  const handleCloseModal = () => {
    setShowContractModal(false);
  };

  if (isLoading || !invoiceDetails) {
    return (<LoadingModal />);
  }

  if (error) {
    return <div className="text-red-500">{error}</div>;
  }

  const docConsentLabel = (
    <React.Fragment>
      I agree to the <button type="button" onClick={openConsentModal} className="text-indigo-600 underline hover:text-indigo-800">Financial Data & Credit Bureau Report Consent</button>.

    </React.Fragment>
  );

  const disbursalConsentLabel = (
    <React.Fragment>
      I give consent to disburse invoice discounting amount to{' '}
      <b>{staticData?.borrowingPartner}</b> account.
    </React.Fragment>
  );

  const renderContractModal = () => {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
        <div className="bg-white p-6 rounded-md shadow-lg w-[80vw] h-[80vh] flex flex-col">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Loan Contract</h2>
            <button
              onClick={handleCloseModal}
              className="text-gray-600 hover:text-gray-800"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
          <div className="flex-grow overflow-auto">
            {pdfUrl && (
              <iframe
                src={pdfUrl}
                className="w-full h-full"
                title="Loan Contract PDF"
              />
            )}
          </div>
          <div className="ml-auto px-6 mt-4 py-2 bg-[#208039] text-white rounded-md hover:bg-[#208039]">
            <Button variant="primary" onClick={handleComplete}>
              Complete
            </Button>
          </div>
        </div>
      </div>
    );
  };

return (
  <> {/* 1. Use a React Fragment to wrap multiple elements */}
    <Header /> {/* 2. Header is now outside the constraining div, it will take full width */}

    {/* 3. This div now only contains the main page content, centered and with spacing */}
    <div className="max-w-4xl mx-auto bg-white my-6 rounded-lg space-y-6 px-4 sm:px-6 lg:px-8 py-6"> {/* Added py-6 for vertical padding (includes pt-6 for space below header) and px for horizontal padding */}
      
      <h2 className="text-2xl font-bold">E-Sign Invoice Discounting Contract</h2>

      <div className="bg-blue-50 p-4 rounded-md"> {/* mb-6 can be removed if space-y-6 on parent handles it sufficiently */}
        <h3 className="text-lg font-semibold mb-2">Invoice Details</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-base text-gray-600">Invoice Number</p>
            <p className="font-medium">{invoiceDetails.invoiceNumber}</p>
          </div>
          <div>
            <p className="text-base text-gray-600">Amount</p>
            <p className="font-medium">
              QAR {Number(invoiceDetails.totalAmount).toLocaleString('en-IN')}
            </p>
          </div>
          <div>
            <p className="text-base text-gray-600">Invoice Date</p>
            <p className="font-medium">{invoiceDetails.invoiceDate}</p>
          </div>
          <div>
            <p className="text-base text-gray-600">Due Date</p>
            <p className="font-medium">{invoiceDetails.dueDate}</p>
          </div>
          <div>
            <p className="text-base text-gray-600">Supplier Name</p>
            <p className="font-medium">{invoiceDetails.supplierName}</p>
          </div>
          <div>
            <p className="text-base text-gray-600">Customer Name</p>
            <p className="font-medium">{invoiceDetails.customerName}</p>
          </div>
          <div>
            <p className="text-base text-gray-600">Billing Address</p>
            <p className="font-medium">{invoiceDetails.billingAddress}</p>
          </div>
          <div>
            <p className="text-base text-gray-600">Customer Address</p>
            <p className="font-medium">{invoiceDetails.customerAddress}</p>
          </div>
          <div>
            <p className="text-base text-gray-600">Status</p>
            <p className="font-medium">{invoiceDetails.status}</p>
          </div>
          <div>
            <p className="text-base text-gray-600">Invoice PDF</p>
            <a
              href={invoiceDetails.signedUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
            >
              View Invoice PDF
            </a>
          </div>
        </div>
      </div>

      <div className="bg-gray-50 p-4 rounded-md">
        <h4 className="text-lg font-semibold mb-3">Invoice Discounting Details</h4>
        <div className="grid grid-cols-3 gap-4">
          <div>
            <p className="text-base text-gray-600">Discounted Percentage</p>
            <p className="font-medium">{staticData?.discountedPercentage}%</p>
          </div>
          <div>
            <p className="text-base text-gray-600">Discounted Amount</p>
            <p className="font-medium">
              QAR {staticData?.discountedAmount.toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-base text-gray-600">Processing Fee</p>
            <p className="font-medium">
              QAR {staticData?.processingCharges.toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-base text-gray-600">Tenure (Days)</p>
            <p className="font-medium">{staticData?.tenure} days</p>
          </div>
          <div>
            <p className="text-base text-gray-600">Estimated EMI</p>
            <p className="font-medium">
              QAR {staticData?.emiAmount.toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-base text-gray-600">Total Amount Payable</p>
            <p className="font-medium">
              QAR {staticData?.totalPayableAmount ? Number(staticData.totalPayableAmount).toLocaleString('en-IN') : 'N/A'}
            </p>
          </div>
          <div>
            <p className="text-base text-gray-600">Service Fee %</p>
            <p className="font-medium">
              {creditLineDetails?.interestRate ? `${creditLineDetails.interestRate}%` : 'N/A'}
            </p>
          </div>
          <div>
            <p className="text-base text-gray-600">Number of Installments</p>
            <p className="font-medium">{staticData?.emiCount}</p>
          </div>
        </div>
      </div>

      <div className="bg-blue-50 p-4 rounded-md">
        <p className="text-base text-gray-700 mb-4">
          <strong>Note:</strong> The disbursed amount will be transferred to the following bank account:
        </p>
        <div className="grid grid-cols-2 gap-3">
          <div>
            <p className="text-base text-gray-600">Bank Account Number</p>
            <p className="font-medium">
              {user?.kyc?.incomeDetails?.accountNumber ?? 'N/A'}
            </p>
          </div>
          <div>
            <p className="text-base text-gray-600">Bank IBAN Number</p>
            <p className="font-medium">{user?.kyc?.incomeDetails?.ifscCode ?? 'N/A'}</p>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Verification and Signing</h3>

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="docConsent"
              checked={docConsent}
              onChange={handleDocConsent}
              className="h-4 w-4 text-blue-600 rounded-md"
            />
            <label htmlFor="docConsent" className="text-base">
              {docConsentLabel}
            </label>
          </div>
          {contractErrors.docConsent && (
            <p className="text-red-500 text-base">{contractErrors.docConsent}</p>
          )}

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="disbursalConsent"
              checked={disbursalConsent}
              onChange={handleDisbursalConsent}
              className="h-4 w-4 text-blue-600 rounded-md"
            />
            <label htmlFor="disbursalConsent" className="text-base">
              {disbursalConsentLabel}
            </label>
          </div>
          {contractErrors.disbursalConsent && (
            <p className="text-red-500 text-base">{contractErrors.disbursalConsent}</p>
          )}
        </div>

        <div className="space-y-2">
          <label className="block">Additional Comments (Optional)</label>
          <textarea
            value={additionalComments}
            onChange={(e) => setAdditionalComments(e.target.value)}
            className="w-full h-32 p-2 border rounded-md resize-none"
            placeholder="Any additional information you'd like to share"
          />
        </div>

        <div className="space-y-2">
          <label className="block">
            <span className="text-red-500">*</span> Digital Signature (Full Name)
          </label>
          <input
            type="text"
            value={signature}
            onChange={(e) => setSignature(e.target.value)}
            className="w-full p-2 border rounded-md"
            placeholder="Type your full name as signature"
          />
          {contractErrors.signature && (
            <p className="text-red-500 text-base">{contractErrors.signature}</p>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="terms"
            checked={agreed}
            onChange={(e) => setAgreed(e.target.checked)}
            className="h-4 w-4 text-blue-600 rounded-md"
          />
          <label htmlFor="terms" className="text-base">
            I agree to the <span className="text-blue-600 hover:underline cursor-pointer" onClick={handleTermsClick}>Terms and Conditions</span> and <span className="text-blue-600 hover:underline cursor-pointer" onClick={handlePrivacyClick}>Privacy Policy</span>
          </label>
          {contractErrors.agreed && (
            <p className="text-red-500 text-base">{contractErrors.agreed}</p>
          )}
        </div>

        {error && (
          <div className="text-red-500 text-base bg-red-50 p-3 rounded-md">{error}</div>
        )}

        {!showContractModal && (
          <div className="flex justify-end space-x-4">
            <button
              onClick={handleShowContract}
              disabled={isButtonDisabled || isLoading}
              className={`px-6 py-2 rounded-md ${!isButtonDisabled && !isLoading
                  ? 'bg-[#004141] text-white'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
            >
              {isLoading ? (
                <span className="flex items-center">
                  <svg
                    className="animate-spin h-5 w-5 mr-2"
                    viewBox="0 0 24 24"
                  >
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z" />
                  </svg>
                  Processing...
                </span>
              ) : (
                'View Contract'
              )}
            </button>
          </div>
        )}
        <Modal isOpen={isConsentModalOpen} onRequestClose={closeConsentModal} contentLabel="Financial Data Consent" style={{ overlay: { backgroundColor: 'rgba(0, 0, 0, 0.75)', zIndex: 1050 }, content: { top: '50%', left: '50%', transform: 'translate(-50%, -50%)', width: '90%', maxWidth: '700px', maxHeight: '80vh', padding: '0', border: 'none', borderRadius: '8px', overflow: 'hidden', display: 'flex', flexDirection: 'column' } }} ariaHideApp={false}>
          <div className="flex justify-between items-center p-4 bg-gray-100 border-b rounded-t-lg flex-shrink-0"><h2 className="text-lg font-semibold">Financial Data & Credit Bureau Report Consent</h2><button onClick={closeConsentModal} className="text-gray-500 hover:text-gray-800"><XMarkIcon className="h-6 w-6" /></button></div>
          <div className="p-6 prose max-w-none overflow-y-auto"> <p>
            Consent Form – Credit Report
            First: By signing this consent I hereby agree to the following:

            1. Madad Financial Technologies, its partners and lenders will inquire about my credit history including returned checks. The inquiry may be made at any stage of my relationship with the Madad which could range from me requesting ( but not yet granted) any service from Madad Financial Technologies, to me being the client of Madad Financial Technologies by way of established, approved relationship.

            2. Madad Financial Technologies, its partners and lenders will review all facilities granted to me, including the regularity of payment, and that Madad Financial Technologies employees need to review and discuss this information to perform their work in relation to the credit history, and that such credit history may affect Madad Financial Technologies’ decision to grant the required facilities, either positively or negatively, and that the credit report request will appear in the credit history and that may affect the decision of any other authority with access to my credit information .

            3. I acknowledge that Madad Financial Technologies, its partners and lenders are required to provide my credit information to Qatar Credit Information Center and vice versa, and that this includes providing information about any defaults for any reason.

            4. Madad Financial Technologies, its partners and lenders may request a credit history report more than once as and when needed according to work requirements.

            5. Madad Financial Technologies, its partners and lenders has the right to request my credit report at any time should any facility be granted and as long as the facility is not fully repaid.

            6. Madad Financial Technologies, its partners and lenders and its employees are exempted from any liability related to the inquiry of my credit history.

            7. I undertake to inform Madad Financial Technologies, its partners and lenders of any significant financial obligation on the credit history not mentioned in the Qatar Credit Center Report.

            Second: I also acknowledge and agree to the following:

            1. I understand that all of the above applies to me, whether an individual, institution or company, and that my signature below represents me personally, or the entity I legally own, represent or authorized on their behalf, (whether the request for the credit history report is for a real person or legal entity).

          </p> <p> I confirm that I have read and understood the above and agree to the terms and conditions outlined herein. </p> </div> <div className="p-4 bg-gray-50 border-t flex justify-end rounded-b-lg flex-shrink-0"><button onClick={closeConsentModal} className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm font-medium">Close</button></div>
        </Modal>

        {showContractModal && renderContractModal()}
      </div>
    </div>
  </>
);
};

export default InvoiceOffer;