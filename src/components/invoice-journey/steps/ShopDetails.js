import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';

const ShopDetails = ({ onNext, onBack }) => {
  const history = useHistory();
  const [isVerifying, setIsVerifying] = useState(false);

  // Hardcoded data matching Global Oils Factory context
  const formData = {
    shopName: 'Global Oils Factory',
    shopType: 'Manufacturing Unit',
    shopOwnershipType: 'Owned-Self',
    shopSector: 'Food & Beverages',
    natureOfBusiness: 'Manufacturer',
    shopAddress: '42, Industrial Area Phase I, Sector 5',
    pincode: '400072',
    state: 'Maharashtra',
    city: 'Mumbai',
    udyamNumber: 'UDYAM-MH-18-0087654',
    gstNumber: '27AAPFU0939F1ZV',
    yearEstablished: '2015',
    monthlyTurnover: 'QAR 15,00,000'
  };

  const handleVerify = async () => {
    setIsVerifying(true);
    // Simulate verification delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsVerifying(false);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onNext(formData);
    history.push('/invoice-financing/additional-details');
  };

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Business Details</h2>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Business Information */}
        <div className="bg-blue-50 p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-4">Basic Information</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Business Name
              </label>
              <input
                type="text"
                value={formData.shopName}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Type of Business
              </label>
              <input
                type="text"
                value={formData.shopType}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Ownership Type
              </label>
              <input
                type="text"
                value={formData.shopOwnershipType}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Business Sector
              </label>
              <input
                type="text"
                value={formData.shopSector}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
            </div>
          </div>
        </div>

        {/* Additional Business Information */}
        <div className="bg-gray-50 p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-4">Additional Information</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Nature of Business
              </label>
              <input
                type="text"
                value={formData.natureOfBusiness}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Year Established
              </label>
              <input
                type="text"
                value={formData.yearEstablished}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Monthly Turnover
              </label>
              <input
                type="text"
                value={formData.monthlyTurnover}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                GST Number
              </label>
              <input
                type="text"
                value={formData.gstNumber}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
            </div>
          </div>
        </div>

        {/* Address Information */}
        <div className="bg-gray-50 p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-4">Business Address</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Address
              </label>
              <input
                type="text"
                value={formData.shopAddress}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  City
                </label>
                <input
                  type="text"
                  value={formData.city}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                  readOnly
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  State
                </label>
                <input
                  type="text"
                  value={formData.state}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                  readOnly
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Pincode
                </label>
                <input
                  type="text"
                  value={formData.pincode}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                  readOnly
                />
              </div>
            </div>
          </div>
        </div>

        {/* Udyam Verification */}
        <div className="bg-green-50 p-4 rounded-md">
          <h3 className="text-lg font-semibold mb-4">Registration Details</h3>
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Udyam Registration
            </label>
            <div className="flex space-x-2">
              <input
                type="text"
                value={formData.udyamNumber}
                className="flex-1 border border-gray-300 rounded-md shadow-lg p-2 bg-gray-100"
                readOnly
              />
              <button
                type="button"
                onClick={handleVerify}
                className={`px-4 py-2 rounded-md ${isVerifying
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-[#208039] hover:bg-[#208039] text-white'
                  }`}
                disabled={isVerifying}
              >
                {isVerifying ? 'Verifying...' : 'Verified ✓'}
              </button>
            </div>
          </div>
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between pt-6">
          <button
            type="button"
            onClick={() => history.push('/invoice-financing/personal-details')}
            className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Back
          </button>
          <button
            type="submit"
            className="px-6 py-2 bg-[#004141] text-white rounded-md  "
          >
            Next
          </button>
        </div>
      </form>
    </div>
  );
};

export default ShopDetails;