import React, { useEffect, useState, useContext } from 'react';
import { LayoutContext } from '../MainLayout'; // Adjust path to MainLayout.js
import DashboardOverview from './dashboard/DashboardOverview';
import InvoiceForm from './dashboard/InvoiceForm';
import MyInvoices from './dashboard/MyInvoices';
import LoanOffersPage from './dashboard/LoanOffersPage'; // Ensure this component exists
import SharedCache from '../sharedCache'; // Ensure this utility exists and works
import Profile from './dashboard/Profile';
import MerchantPaymentsDashboard from './dashboard/Payments';
import ActivationJourney from './dashboard/ActivationJourney';
import axios from 'axios';
import config from '../config'; // Ensure config file exists with apiUrl
import MyBuyersPage from './dashboard/MyBuyers';

import {
  ClockIcon,
  ExclamationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  InformationCircleIcon,
  GiftIcon
} from '@heroicons/react/24/outline';

import briefcaseMoney from '../images/briefcase_money.png'; // Adjust path
import LoadingModal from './Reusable/Loading';
import { fetchUserById } from '../api/auth';

const LoadingIndicator = ({ fileName }) => {
  return (
    <LoadingModal
      message={fileName ? `Loading ${fileName}...` : "Please wait a moment while we load your journey data"}
    />
  );
};

const Dashboard = () => {
  const { currentPage, setCurrentPage: setMainLayoutPage } = useContext(LayoutContext);

  const [showForm, setShowForm] = useState(false);
  const [showActivationJourneyContent, setShowActivationJourneyContent] = useState(true);
  const [applicationStatus, setApplicationStatus] = useState({ message: '', label: '', color: '', icon: null });
  const [loading, setLoading] = useState(true);
  const [userId, setUserId] = useState(() => (SharedCache.get('user') || {})._id || "");
  const [creditLineIsActive, setCreditLineIsActive] = useState(false);
  const [creditLine, setCreditLine] = useState({});
  const [offers, setOffers] = useState({});
  // Inside the Dashboard component, near other useState hooks:
  useEffect(() => {
    const handleCacheChange = () => {
      const cachedUser = SharedCache.get('user') || {};
      if (cachedUser._id && cachedUser._id !== userId) {
        setUserId(cachedUser._id);
      }
    };
    handleCacheChange(); // Check on mount
    // If SharedCache emits events:
    // SharedCache.on('change', handleCacheChange);
    // return () => SharedCache.off('change', handleCacheChange);
  }, []); // userId dependency removed to avoid potential loops if SharedCache is synchronous

  useEffect(() => {
    // This guard prevents the function from running before userId is available
    if (!userId) {
      const userFromCache = SharedCache.get('user');
      if (userFromCache?._id || userFromCache?.id) {
        setUserId(userFromCache._id || userFromCache.id);
      } else {
        setLoading(false);
        console.error("Dashboard: No user ID found, redirecting to login.");
        // history.push('/login'); // Use this if history is available
        window.location.href = '/login';
      }
      return; // Exit effect until userId state is updated
    }

    const fetchDataAndUpdateStatus = async () => {
      setLoading(true);
      try {
        // Use the new centralized function to get user details
        const userResponse = await fetchUserById(userId);

        if (userResponse.status !== 200) {
          throw new Error(userResponse.data?.message || "Failed to fetch user data");
        }

        const currentUserDetails = userResponse.data;
        SharedCache.set('user', currentUserDetails);

        let creditLineStatus = 'INACTIVE';
        try {
          const creditLineResponse = await axios.get(
            `${config.apiUrl}/ops/invoiceFinancing/fetchCreditLine/${userId}`
          );
          if (creditLineResponse.data?.creditLineStatus) {
            creditLineStatus = creditLineResponse.data.creditLineStatus;
            setCreditLine(creditLineResponse.data);
          }
        } catch (creditLineError) {
          console.error("Failed to fetch credit line status:", creditLineError);
        }

        setCreditLineIsActive(creditLineStatus === 'ACTIVE');
        setShowActivationJourneyContent(creditLineStatus !== 'ACTIVE');

        // Your existing logic for setting application status based on KYC and credit line
        const verificationStatus = currentUserDetails?.kyc?.verificationStatus;
        switch (verificationStatus) {
          case "INITIATED":
          case "REINITIATED":
            setApplicationStatus({ message: "Your application has been initiated. Please complete the KYC process.", label: "Initiated", color: "bg-blue-100 text-blue-700", icon: ExclamationCircleIcon });
            // Redirecting to KYC should be handled by the component that renders based on this status
            break;
          case "UNDER_REVIEW":
            setApplicationStatus({ message: "Your application is under review. We'll update you soon.", label: "Under Review", color: "bg-yellow-100 text-yellow-700", icon: ClockIcon });
            break;
          case "APPROVED":
            setApplicationStatus({ message: "Your KYC is approved. Complete the next steps to activate your credit line.", label: "KYC Approved", color: "bg-green-100 text-green-700", icon: CheckCircleIcon });
            break;
          // Add other cases like REJECTED, INFO_NEEDED etc.
          default:
            setApplicationStatus({ message: "Welcome to your dashboard. Complete the steps to get started.", label: "Welcome", color: "bg-gray-100 text-gray-700", icon: InformationCircleIcon });
        }

      } catch (error) {
        console.error("Failed to fetch user data and related statuses:", error);
        setApplicationStatus({ message: "Could not load dashboard data. Please try again later.", label: "Error", color: "bg-red-100 text-red-700", icon: XCircleIcon });
      } finally {
        setLoading(false);
      }
    };

    fetchDataAndUpdateStatus();
  }, [userId]);
  const StatusIconToRender = applicationStatus.icon; // Changed variable name

  if (loading) {
    return <LoadingIndicator fileName={userId ? "dashboard data" : "user session"} />;
  }

  const renderDynamicContent = () => {
    if (showForm) { // Show InvoiceForm if requested, potentially on top of other content or replacing it
      return <InvoiceForm userId={userId} onClose={() => setShowForm(false)} />;
    }

    switch (currentPage) { // currentPage from LayoutContext
      case 'my-buyers':
        return <MyBuyersPage />;
      case 'my-invoices':
        return <MyInvoices />;
      case 'payments':
        return <MerchantPaymentsDashboard />;
      case 'settings':
        return <Profile />;
      case 'lender-offers': // Example if you add this page
        return <LoanOffersPage />;
      case 'dashboard':
      default:
        // Always render ActivationJourney if showActivationJourneyContent is true
        if (showActivationJourneyContent) {
          return <ActivationJourney />;
        }
        return <DashboardOverview setCurrentPage={setMainLayoutPage} setShowInvoiceForm={setShowForm} />;
    }
  };

  return (
    <div className="flex flex-col h-full"> {/* Fills the main content area of MainLayout */}
      {!creditLineIsActive && applicationStatus.message && (
        <div className={`p-3 flex items-center justify-center text-sm text-center sticky top-0 z-10 ${applicationStatus.color || 'bg-gray-100 text-gray-700'}`}>
          {StatusIconToRender && <StatusIconToRender className="h-5 w-5 mr-2 flex-shrink-0" />}
          <span className="truncate">{applicationStatus.message}</span>
        </div>
      )}

      <div className="flex-1 overflow-y-auto p-4 md:p-6"> {/* Content area with padding and scroll */}
        {/*
          Removed the conditional rendering for the left column (credit line component)
          Now, renderDynamicContent will always take full width.
        */}
        {renderDynamicContent()}
      </div>
    </div>
  );
};

export default Dashboard;