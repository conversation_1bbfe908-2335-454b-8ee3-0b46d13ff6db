import React, { useState, useEffect } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { ArrowLeftIcon, ArrowDownOnSquareIcon, LinkIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import axios from 'axios';
import config from "../config.json"
import { parseISO } from 'date-fns';
import LoadingModal from './Reusable/Loading';

// Status styles matching the original component (keeping these global or in a separate file)
const STATUS_STYLES = {
    'VERIFICATION_PENDING_ANCHOR': { bg: 'bg-yellow-400', text: 'text-yellow-900', border: 'border-yellow-500' },
    'VERIFIED_ANCHOR': { bg: 'bg-green-500', text: 'text-white', border: 'border-green-600' },
    'REJECTED_ANCHOR': { bg: 'bg-red-500', text: 'text-white', border: 'border-red-600' },
    'MORE_INFO_NEEDED_ANCHOR': { bg: 'bg-orange-400', text: 'text-orange-900', border: 'border-orange-500' },
    'DEFAULT': { bg: 'bg-gray-400', text: 'text-gray-900', border: 'border-gray-500' }
};

const STATUS_DISPLAY_NAMES = {
    'VERIFICATION_PENDING_ANCHOR': 'Pending Verification',
    'VERIFIED_ANCHOR': 'Verified Buyer',
    'REJECTED_ANCHOR': 'Rejected by You',
    'MORE_INFO_NEEDED_ANCHOR': 'More Info Requested',
    'UNKNOWN': 'Unknown Status'
};

const BUYER_STATUS_OPTIONS = {
    APPROVE: 'VERIFIED_ANCHOR',
    REJECT: 'REJECTED_ANCHOR',
    MORE_INFO: 'MORE_INFO_NEEDED_ANCHOR',
};

// --- Helper function to safely parse date strings ---
const safeParseDate = (dateString) => {
    if (!dateString) return null;
    try {
        const parsed = parseISO(dateString);
        if (isNaN(parsed.getTime())) return null;
        return parsed;
    } catch (e) {
        console.error("Error parsing date:", dateString, e);
        return null;
    }
};

const StatusBadge = ({ status, displayText, baseClasses, colorClass }) => (
    <span className={`${baseClasses} ${colorClass}`}>
        {displayText}
    </span>
);

const InvoiceVerificationPage = () => {
    const { id } = useParams();
    const history = useHistory();

    const [invoice, setInvoice] = useState(null);
    const [verificationComments, setVerificationComments] = useState('');
    const [isLoading, setIsLoading] = useState(true);
    const [reasonForDisablement, setReasonForDisablement] = useState('');
    const fetchInvoiceDetails = async () => {
        if (!id) {
            toast.error("Invoice ID missing from URL.");
            history.push('/buyer-invoices');
            setIsLoading(false);
            return;
        }

        try {
            const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/fetchInvoice/${id}`);
            if (response.data && response.data._doc) {
                const fetchedInvoice = response.data._doc;
                setInvoice(fetchedInvoice);

                if (fetchedInvoice.verificationComments) {
                    setVerificationComments(fetchedInvoice.verificationComments);
                }

                // Define the statuses where buyer actions are allowed
                const buyerModifiableStatuses = [
                    'VERIFICATION_PENDING_ANCHOR',
                    'VERIFIED_ANCHOR',
                    'MORE_INFO_NEEDED_ANCHOR',
                    'REJECTED_ANCHOR'
                ];

                // Set the disablement reason if the current status is NOT in the allowed list
                if (!buyerModifiableStatuses.includes(fetchedInvoice.status)) {
                    setReasonForDisablement("This invoice has been picked up for discounting by buyer, so its status cannot be changed.");
                } else {
                    setReasonForDisablement(""); // Clear the reason if status is modifiable
                }

            } else {
                toast.error("Invoice details not found.");
                history.push('/buyer-invoices');
            }
        } catch (error) {
            console.error("Error fetching invoice details:", error.response?.data || error.message);
            toast.error(`Failed to load invoice details: ${error.response?.data?.message || 'Network error'}`);
            history.push('/buyer-invoices');
        } finally {
            setIsLoading(false);
        }
    };
    useEffect(() => {
        fetchInvoiceDetails();
    }, [id, history]);

    if (isLoading) {
        return (<LoadingModal />);
    }

    if (!invoice) {
        return (
            <div className="min-h-screen bg-[#F7F8FA] flex items-center justify-center text-gray-500">
                Invoice details could not be loaded. Please try again.
            </div>
        );
    }

    const getStatusStyle = (status) => {
        const style = STATUS_STYLES[status] || STATUS_STYLES['DEFAULT'];
        return `${style.bg} ${style.text} ${style.border}`;
    };

    const getStatusDisplay = (status) => {
        return STATUS_DISPLAY_NAMES[status] || status?.replace(/_/g, ' ') || STATUS_DISPLAY_NAMES['UNKNOWN'];
    };

    const formatAmount = (amount) => {
        if (amount === null || amount === undefined || amount === '') return 'N/A';
        const numAmount = Number(amount);
        if (isNaN(numAmount)) return 'Invalid Amount';
        return `QAR ${numAmount.toLocaleString('en-QA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    };

    const handleStatusUpdate = async (newStatus) => {
        // First, check if the invoice's current status allows ANY buyer action
        const buyerModifiableStatuses = [
            'VERIFICATION_PENDING_ANCHOR',
            'VERIFIED_ANCHOR',
            'MORE_INFO_NEEDED_ANCHOR',
            'REJECTED_ANCHOR'
        ];
        if (!buyerModifiableStatuses.includes(invoice.status)) {
            toast.error(reasonForDisablement || "Status cannot be changed for this invoice.");
            return;
        }

        if ((newStatus === BUYER_STATUS_OPTIONS.REJECT || newStatus === BUYER_STATUS_OPTIONS.MORE_INFO) && !verificationComments.trim()) {
            toast.warn("Comments are required for rejection or requesting more information.");
            return;
        }

        setIsLoading(true);
        try {
            await axios.put(
                `${config.apiUrl}/ops/invoiceFinancing/updateInvoice/${invoice._id}`,
                { status: newStatus, verificationComments: verificationComments }
            );
            toast.success(`Invoice ${newStatus === BUYER_STATUS_OPTIONS.APPROVE ? 'verified' : newStatus === BUYER_STATUS_OPTIONS.REJECT ? 'rejected' : 'status updated'}!`);
            // Re-fetch the invoice to ensure the UI reflects the new status and comments
            setTimeout(fetchInvoiceDetails, 500);
        } catch (error) {
            toast.error(`Update Failed: ${error.response?.data?.message || 'Unknown error'}`);
        } finally {
            setIsLoading(false);
        }
    };

    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        const date = safeParseDate(dateString);
        if (!date) return 'Invalid Date';
        return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD format
    };

    const getFilenameFromPath = (path) => {
        if (!path) return 'document';
        try {
            const url = new URL(path);
            return decodeURIComponent(url.pathname.split('/').pop() || 'document');
        } catch (e) {
            return decodeURIComponent(path.split(/[/\\]/).pop() || 'document');
        }
    };

    const renderDetailItem = (label, value, isAmount = false) => (
        <div className="mb-3">
            <span className="text-xs text-gray-500 block">{label}</span>
            <span className={`text-sm font-medium text-gray-800 block break-words ${isAmount ? 'text-lg font-bold' : ''}`}>
                {value || 'N/A'}
            </span>
        </div>
    );

    // Determine if comments textarea and buttons should be generally enabled based on allowed statuses
    const canPerformAction = ['VERIFICATION_PENDING_ANCHOR', 'VERIFIED_ANCHOR', 'MORE_INFO_NEEDED_ANCHOR', 'REJECTED_ANCHOR'].includes(invoice.status);

    return (
        <div className="min-h-screen bg-[#F7F8FA]">
            {/* Header Bar */}
            <div className="bg-white border-b border-gray-200 px-4 sm:px-6 py-4">
                <div className="flex justify-between items-center">
                    <button
                        onClick={() => { history.push('/buyer-invoices') }}
                        className="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center"
                    >
                        <ArrowLeftIcon className="w-4 h-4 mr-1.5" /> Back to invoices
                    </button>
                    <div className="flex items-center">
                        <h1 className="text-lg font-semibold text-gray-700 mr-2">Invoice Detail</h1>
                        <StatusBadge
                            status={invoice.status}
                            displayText={getStatusDisplay(invoice.status)}
                            baseClasses="px-2 py-0.5 text-xs font-medium rounded"
                            colorClass={getStatusStyle(invoice.status)}
                        />
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="flex flex-col lg:flex-row h-[calc(100vh-80px)]">
                {/* Left: PDF Viewer Area */}
                <div className="w-full lg:w-[65%] h-full flex flex-col bg-gray-700 border-r border-gray-300">
                    {/* PDF Controls Bar */}
                    <div className="bg-gray-800 text-white px-3 py-1.5 flex items-center justify-between text-xs border-b border-gray-600">
                        <span className="font-medium">Invoice</span>
                        <div className="flex items-center space-x-3">
                            <span>1 / {invoice.additionalInvoiceDocuments?.length > 0 ? '2' : '1'}</span>
                            <button title="Zoom Out" className="hover:bg-gray-700 p-0.5 rounded">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v6m-3-3h6" />
                                </svg>
                            </button>
                            <span>31%</span>
                            <button title="Zoom In" className="hover:bg-gray-700 p-0.5 rounded">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10h-6" />
                                </svg>
                            </button>
                            <button title="Download" className="hover:bg-gray-700 p-0.5 rounded">
                                <ArrowDownOnSquareIcon className="h-4 w-4" />
                            </button>
                        </div>
                    </div>

                    {/* PDF Viewer */}
                    {invoice.signedUrl ? (
                        <iframe
                            src={invoice.signedUrl}
                            className="w-full flex-grow border-0"
                            title="Invoice PDF Preview"
                        />
                    ) : (
                        <div className="flex items-center justify-center flex-grow text-gray-400 p-10 text-center">
                            Invoice preview is not available or could not be loaded.
                        </div>
                    )}
                </div>

                {/* Right: Details & Actions Pane */}
                <div className="w-full lg:w-[35%] flex flex-col h-full bg-white">
                    <div className="flex-grow overflow-y-auto p-5 space-y-3">
                        {/* Invoice Basic Details */}
                        <div className="grid grid-cols-2 gap-x-4 gap-y-1">
                            {renderDetailItem("Invoice Number:", invoice.invoiceNumber)}
                            {renderDetailItem("Invoice Date:", formatDate(invoice.invoiceDate))}
                            {renderDetailItem("Due Date:", formatDate(invoice.dueDate))}
                        </div>

                        {/* Trade Name and Buyer Name */}
                        {renderDetailItem("Trade Name / Legal Entity Name:", invoice.supplierName)}
                        {renderDetailItem("Buyer Name:", invoice.customerName)}

                        {/* Supporting Documents */}
                        <div className="pt-3 mt-2">
                            <h4 className="text-xs font-medium text-gray-500 mb-1">Supporting Documents</h4>
                            {(invoice.additionalInvoiceDocuments && invoice.additionalInvoiceDocuments.length > 0) ? (
                                <div className="space-y-1 max-h-28 overflow-y-auto pr-1 text-xs border bg-gray-50 p-2 rounded-md">
                                    {invoice.additionalInvoiceDocuments.map((doc, index) => (
                                        doc && (
                                            <div key={doc._id || index} className="flex items-center justify-between hover:bg-gray-100 p-1 rounded">
                                                <div className="flex items-center space-x-1.5 overflow-hidden mr-1">
                                                    <LinkIcon className="h-3 w-3 text-gray-400 flex-shrink-0" />
                                                    <span className="truncate text-gray-600" title={getFilenameFromPath(doc.filePath)}>
                                                        {getFilenameFromPath(doc.filePath)}
                                                    </span>
                                                </div>
                                                {doc.signedUrl ? (
                                                    <a href={doc.signedUrl} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline font-medium flex-shrink-0">View</a>
                                                ) : <span className="text-gray-400 italic">No Link</span>}
                                            </div>
                                        )
                                    ))}
                                </div>
                            ) : (
                                <p className="text-xs text-gray-500 italic bg-gray-50 p-3 rounded-md">No Supporting document found.</p>
                            )}
                        </div>

                        {/* Total Amount */}
                        <div className="bg-gray-100 p-3 rounded-md mt-3">
                            <span className="text-xs font-medium text-gray-600 block mb-0.5">Total Amount Due</span>
                            <div className="text-xl font-bold text-gray-800">{formatAmount(invoice.totalAmount)}</div>
                        </div>

                        {/* Comments Section */}
                        <div className="pt-3 mt-2">
                            <label htmlFor="verification-comments" className="block text-xs font-medium text-gray-500 mb-1">Add/Edit Comments</label>
                            <textarea
                                id="verification-comments"
                                value={verificationComments}
                                onChange={(e) => setVerificationComments(e.target.value)}
                                className="w-full p-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition disabled:bg-gray-100 disabled:cursor-not-allowed"
                                rows={3}
                                placeholder="Add Comments (required for rejection or requesting info)"
                                disabled={isLoading || !canPerformAction}
                            />
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="sticky bottom-0 bg-white border-t border-gray-200 p-4">
                        <div className="flex space-x-3 mb-2">
                            <button
                                onClick={() => handleStatusUpdate(BUYER_STATUS_OPTIONS.APPROVE)}
                                disabled={
                                    isLoading ||
                                    !canPerformAction || // Overall check if any action can be performed
                                    (invoice.status !== 'VERIFICATION_PENDING_ANCHOR' && // Cannot verify if already verified
                                        invoice.status !== 'REJECTED_ANCHOR' && // Can verify if rejected
                                        invoice.status !== 'MORE_INFO_NEEDED_ANCHOR') // Can verify if more info was needed
                                }
                                className="flex-1 py-2.5 px-4 text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
                            >
                                {isLoading ? (
                                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                                ) : (
                                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                                    </svg>
                                )}
                                Verify
                            </button>
                            <button
                                onClick={() => handleStatusUpdate(BUYER_STATUS_OPTIONS.REJECT)}
                                disabled={
                                    isLoading ||
                                    !canPerformAction || // Overall check if any action can be performed
                                    (invoice.status === 'REJECTED_ANCHOR') // Cannot reject if already rejected
                                }
                                className="flex-1 py-2.5 px-4 text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
                            >
                                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"></path>
                                </svg>
                                Reject
                            </button>
                            <button
                                onClick={() => handleStatusUpdate(BUYER_STATUS_OPTIONS.MORE_INFO)}
                                disabled={
                                    isLoading ||
                                    !canPerformAction || // Overall check if any action can be performed
                                    (invoice.status === 'MORE_INFO_NEEDED_ANCHOR') // Cannot request more info if already requested
                                }
                                className="flex-1 py-2.5 px-4 text-sm font-medium rounded-md text-white bg-sky-600 hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
                            >
                                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"></path>
                                </svg>
                                Need More Info
                            </button>
                        </div>
                        {reasonForDisablement && (
                            <p className="text-red-500 text-xs mt-2 text-center">{reasonForDisablement}</p>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default InvoiceVerificationPage;