import * as React from "react";
import { useState, useEffect, useRef } from "react";
import { useHistory, useLocation } from "react-router-dom";
import { fetchOtpForLogin, validateOtpForLoginAndCheckKYC } from "../api/auth";
import SharedCache from "../sharedCache";

const LoginPage = () => {
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [kycCompleted, setKycCompleted] = useState(false);
  const [phone, setPhone] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [panNo, setPanNo] = useState("");
  const [otp, setOtp] = useState("");
  const [timer, setTimer] = useState(119);
  const [otpError, setOtpError] = useState("");
  const [phoneError, setPhoneError] = useState("");
  const [isSendingOtp, setIsSendingOtp] = useState(false);
  const [sendOtpError, setSendOtpError] = useState("");
  const history = useHistory();
  const location = useLocation();
  const containerRef = useRef(null);

  useEffect(() => {
    // Check for KYC completion from location state or SharedCache
    if (location.state?.kycCompleted || SharedCache.get("user")?.kycCompleted) {
      console.log(location.state, SharedCache.get("user"))
      setKycCompleted(true);
      setIsVerified(true);
    }
  }, [location]);

  const handleSendOtpClick = async () => {
    if (phone.length !== 10) {
      setPhoneError("Enter a valid phone number (10 digits)");
      return;
    }

    if (isOtpSent || isSendingOtp) {
      return;
    }

    setIsSendingOtp(true);

    try {
      const response = await fetchOtpForLogin(phone);
      if (response.success) {
        setIsOtpSent(true);
        setTimer(119);
        setPhoneError("");
        setSendOtpError("");
      } else {
        setSendOtpError(
          "There was an error while sending the OTP. Please try again later."
        );
      }
    } catch (error) {
      setSendOtpError(
        "There was an error while sending the OTP. Please try again later."
      );
    } finally {
      setIsSendingOtp(false);
    }
  };

  const handleVerifyOtpClick = async () => {
    if (otp.length !== 6) {
      setOtpError("OTP must be 6 digits");
      return;
    }

    try {
      const otpResponse = await validateOtpForLoginAndCheckKYC(phone, otp);
      console.log("OTP RESPONSE HERE", otpResponse);

      if (otpResponse.success) {
        setIsVerified(true);
        setOtpError("");

        // Check KYC status from response
        if (otpResponse.user?.kycCompleted || SharedCache.get("user")?.kycCompleted) {
          setKycCompleted(true);
        }
      } else {
        setOtpError("Invalid OTP. Please try again.");
      }
    } catch (error) {
      setOtpError("Error verifying OTP. Please try again.");
      console.error(error);
    }
  };

  const handleContinueClick = () => {
    // Basic validation
    if (!firstName.trim() || !lastName.trim() || !email.trim() || !panNo.trim()) {
      return; // Could add specific validation messages here if needed
    }

    // Store user details in SharedCache
    const currentUser = SharedCache.get("user");
    console.log(currentUser, "current user")
    if (currentUser) {
      currentUser.firstName = firstName;
      currentUser.lastName = lastName;
      currentUser.email = email;
      currentUser.panNo = panNo;
      SharedCache.update("user", currentUser);
    }

    history.push({
      pathname: "/documents",
      state: {
        panNo: panNo,
        kycCompleted: kycCompleted
      }
    });
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  const handlePhoneChange = (e) => {
    const value = e.target.value.replace(/\D/g, "");
    if (value.length <= 10) {
      setPhone(value);
    }
  };

  const handleOtpChange = (e) => {
    const value = e.target.value.replace(/\D/g, "");
    if (value.length <= 6) {
      setOtp(value);
    }
  };

  const handleResetOtp = () => {
    setIsOtpSent(false);
    setOtp("");
    setOtpError("");
    setPhone("");
    setPhoneError("");
    setSendOtpError("");
  };

  useEffect(() => {
    let interval;
    if (isOtpSent && timer > 0) {
      interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, 1000);
    } else if (timer === 0) {
      clearInterval(interval);
    }
    return () => clearInterval(interval);
  }, [isOtpSent, timer]);

  const adjustMargins = () => {
    const container = containerRef.current;
    if (container?.scrollHeight > container?.clientHeight) {
      const children = container.children;
      for (let i = 0; i < children.length; i++) {
        const child = children[i];
        const style = window.getComputedStyle(child);
        const marginTop = parseFloat(style.marginTop);
        const marginBottom = parseFloat(style.marginBottom);
        const newMarginTop = Math.max(marginTop - 2, 0);
        const newMarginBottom = Math.max(marginBottom - 2, 0);
        child.style.marginTop = `${newMarginTop}px`;
        child.style.marginBottom = `${newMarginBottom}px`;
      }
    }
  };

  useEffect(() => {
    adjustMargins();
    window.addEventListener("resize", adjustMargins);
    return () => {
      window.removeEventListener("resize", adjustMargins);
    };
  }, [isOtpSent, phone, otp, otpError, phoneError, sendOtpError]);

  return (
    <div className="flex justify-center items-center min-h-screen bg-[#208039] font-poppins">
      <div
        ref={containerRef}
        className="flex flex-col items-center px-6 py-6 max-w-full bg-white rounded-md w-[577px] max-md:px-4 h-[82vh] overflow-hidden"
      >
        <div className="flex flex-col mt-4 items-center max-w-full w-[374px]">
          <img
            loading="lazy"
            src={require("../images/logo.jpg")}
            className="max-w-full w-[174px]"
            alt="Header"
          />
          {!isOtpSent ? (
            <>
              <div className="self-stretch mt-8 text-2xl font-bold leading-10 text-stone-900 max-md:mt-4">
                Please enter your phone number to Log In
              </div>
              <div className="flex flex-col self-stretch pb-2 mt-2 bg-white bg-opacity-0 max-md:mt-4">
                <div className="mt-8 flex flex-col pb-1 text-base leading-6 bg-white bg-opacity-0 text-neutral-800">
                  <div className="justify-center bg-white bg-opacity-0">
                    Please enter the number
                  </div>
                </div>
                <input
                  type="text"
                  value={phone}
                  onChange={handlePhoneChange}
                  className="justify-center items-start px-4 py-2 text-lg leading-8 text-gray-500 bg-white rounded-md border border-gray-300 border-solid max-md:pr-3"
                  placeholder="Phone number"
                  autoFocus
                />
                {phoneError && (
                  <div className="text-red-500 mt-2 text-center">{phoneError}</div>
                )}
                {sendOtpError && (
                  <div className="text-red-500 mt-2 text-center">{sendOtpError}</div>
                )}
              </div>
              <div
                className={`flex justify-center items-center self-stretch px-3 py-2 mt-4 text-lg font-semibold leading-7 ${phone.length === 10 && !isSendingOtp
                  ? "text-white bg-[#004141] cursor-pointer"
                  : "text-gray-500 bg-gray-300 cursor-not-allowed"
                  } rounded-md max-md:px-4`}
                onClick={phone.length === 10 ? handleSendOtpClick : null}
              >
                {isSendingOtp ? "Sending..." : "Send OTP"}
              </div>
            </>
          ) : isVerified === false ? (
            <>
              <div className="self-stretch mt-6 text-2xl font-bold leading-10 text-stone-900 max-md:mt-4 text-center">
                Please enter the OTP to verify your account
              </div>
              <div className="self-stretch mt-4 text-lg leading-8 text-blue-600 text-center">
                <span className="font-bold">An OTP has been sent to </span>
                <span className="font-bold text-blue-600">{`******${phone.slice(6, 10)}`}</span>
              </div>
              <div className="flex flex-col self-stretch pb-2 mt-8 bg-white bg-opacity-0 max-md:mt-4">
                <div className="flex flex-col pb-1 text-base leading-6 bg-white bg-opacity-0 text-neutral-800">
                  <div className="justify-center bg-opacity-0">
                    Please enter OTP
                  </div>
                </div>
                <input
                  type="text"
                  value={otp}
                  onChange={handleOtpChange}
                  className="justify-center items-start px-4 py-2 text-lg leading-8 text-gray-500 bg-white rounded-md border border-gray-300 border-solid max-md:pr-3"
                  placeholder="OTP"
                  autoFocus
                />
                {otpError && (
                  <div className="text-red-500 mt-2 text-center">{otpError}</div>
                )}
              </div>
              <div
                className={`flex justify-center items-center self-stretch px-3 py-2 mt-4 text-lg font-semibold leading-7 ${otp.length === 6
                  ? "text-white bg-[#004141] cursor-pointer"
                  : "text-gray-500 bg-gray-300 cursor-not-allowed"
                  } rounded-md max-md:px-4`}
                onClick={otp.length === 6 ? handleVerifyOtpClick : null}
              >
                Verify OTP
              </div>
              <div className="flex justify-center items-center mt-2">
                <span className="text-gray-600 text-sm">Resend OTP in </span>
                <span className="ml-2 text-gray-600 text-sm font-bold">
                  {formatTime(timer)}
                </span>
              </div>
              {timer === 0 && (
                <div
                  className="flex justify-center items-center self-stretch px-3 py-2 mt-4 text-lg font-semibold leading-7 text-white bg-[#004141] rounded-md max-md:px-4 cursor-pointer"
                  onClick={handleResetOtp}
                >
                  Resend OTP
                </div>
              )}
            </>
          ) : (
            <>
              <div className="self-stretch mt-6 text-2xl font-bold leading-10 text-stone-900 max-md:mt-4 text-center">
                Please fill all the details for onboarding
              </div>
              {kycCompleted && (
                <div className="mt-4 p-3 bg-[#208039] text-[#004141] rounded-md text-center">
                  KYC verification completed successfully! Please complete your profile.
                </div>
              )}
              <div className="flex flex-col self-stretch pb-2 mt-8 bg-white bg-opacity-0 max-md:mt-4">
                <div className="justify-center bg-white bg-opacity-0">
                  First Name
                </div>
                <input
                  type="text"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  className="justify-center items-start px-4 py-2 text-lg leading-8 text-gray-500 bg-white rounded-md border border-gray-300 border-solid max-md:pr-3"
                  placeholder="First Name"
                  autoFocus
                />
              </div>
              <div className="flex flex-col self-stretch pb-2 mt-2 bg-white bg-opacity-0 max-md:mt-2">
                <div className="justify-center bg-white bg-opacity-0">
                  Last Name
                </div>
                <input
                  type="text"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  className="justify-center items-start px-4 py-2 text-lg leading-8 text-gray-500 bg-white rounded-md border border-gray-300 border-solid max-md:pr-3"
                  placeholder="Last Name"
                />
              </div>
              <div className="flex flex-col self-stretch pb-2 mt-2 bg-white bg-opacity-0 max-md:mt-2">
                <div className="justify-center bg-white bg-opacity-0">
                  Email
                </div>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="justify-center items-start px-4 py-2 text-lg leading-8 text-gray-500 bg-white rounded-md border border-gray-300 border-solid max-md:pr-3"
                  placeholder="Email"
                />
              </div>
              <div className="flex flex-col self-stretch pb-2 mt-2 bg-white bg-opacity-0 max-md:mt-2">
                <div className="justify-center bg-white bg-opacity-0">
                  10-Digit PAN Number
                </div>
                <input
                  type="text"
                  value={panNo}
                  onChange={(e) => setPanNo(e.target.value)}
                  className="justify-center items-start px-4 py-2 text-lg leading-8 text-gray-500 bg-white rounded-md border border-gray-300 border-solid max-md:pr-3"
                  placeholder="PAN Number"
                  autoFocus
                />
              </div>
              <div
                className="flex justify-center items-center self-stretch px-3 py-2 mt-4 text-lg font-semibold leading-7 text-white bg-[#004141] rounded-md max-md:px-4 cursor-pointer"
                onClick={handleContinueClick}
              >
                Continue
              </div>
            </>
          )}
        </div>
        <div className="mt-auto w-full">
          <div className="text-xs font-bold leading-6 text-stone-900 text-center">
            Your data is 100% safe with us
          </div>
          <div className="text-xs font-bold leading-6 text-stone-900 text-center">
            © Madad Fintech. All rights reserved
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;