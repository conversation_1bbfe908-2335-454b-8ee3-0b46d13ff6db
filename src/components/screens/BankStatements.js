import React, { useState } from "react";
import axios from "axios";
import config from '../../config.json';
import SharedCache from "../../sharedCache";

const BankStatementUpload = ({ panNo, gstin, setIsBankStatementUploaded, setIsInvoiceUploaded }) => {
    const [isStatementSubmitted, setIsStatementSubmitted] = useState(false);
    const [uploadedFileName, setUploadedFileName] = useState("");
    const [bankDetails, setBankDetails] = useState({});
    const [pdfPreview, setPdfPreview] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);

    // Get user data and token - handle the case where it might not exist
    const user = SharedCache.get("user") || {};
    const userId = user._id || "";
    const token = SharedCache.get("token") || "placeholdertoken";

    console.log("User data:", user);
    console.log("User ID:", userId);

    const handleFileUpload = async (e) => {
        const file = e.target.files[0];
        if (file) {
            try {
                setIsLoading(true);
                setError(null);

                // Create preview URL for the PDF
                const fileUrl = URL.createObjectURL(file);
                setPdfPreview(fileUrl);
                setUploadedFileName(file.name);

                // Create mock data for bank statement (this would be replaced by actual parsing in production)
                const mockBankDetails = {
                    customerName: 'Test Customer',
                    customerId: `CID-${Math.floor(Math.random() * 10000)}`,
                    documentType: 'Bank Statement',
                    category: 'Financial',
                    totalAmount: (Math.random() * 100000 + 10000).toFixed(2),
                    lineItems: [
                        { description: 'Opening Balance', totalAmount: (Math.random() * 50000 + 5000).toFixed(2) },
                        { description: 'Salary Credit', totalAmount: (Math.random() * 10000 + 3000).toFixed(2) },
                        { description: 'Utility Payment', totalAmount: (Math.random() * 1000 + 200).toFixed(2) },
                        { description: 'Bank Transfer', totalAmount: (Math.random() * 5000 + 1000).toFixed(2) },
                        { description: 'ATM Withdrawal', totalAmount: (Math.random() * 2000 + 500).toFixed(2) }
                    ]
                };

                // Prepare form data for upload
                const formData = new FormData();
                formData.append('pdfFile', file);

                // If we have a userId, include it, otherwise fall back to using PAN for backward compatibility
                if (userId) {
                    formData.append('userId', userId);
                    console.log("Added userId to form data:", userId);
                } else {
                    console.log("No userId available, using PAN for lookup");
                }

                // Always include these fields
                formData.append('panNo', panNo);
                formData.append('gstin', gstin);

                // Add bank details to form data
                Object.keys(mockBankDetails).forEach(key => {
                    if (key === 'lineItems') {
                        formData.append(key, JSON.stringify(mockBankDetails[key]));
                    } else {
                        formData.append(key, mockBankDetails[key]);
                    }
                });

                console.log("Form data being sent to backend:", {
                    panNo,
                    gstin,
                    userId,
                    totalAmount: mockBankDetails.totalAmount
                });

                // Send to backend
                const response = await axios.post(
                    `${config.apiUrl}/ops/invoiceFinancing/uploadBankStatement`,
                    formData,
                    {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                            'x-auth-token': token
                        }
                    }
                );

                console.log("Bank statement upload response:", response.data);

                // Set the bank details for display
                setBankDetails(mockBankDetails);
                setIsStatementSubmitted(true);

            } catch (error) {
                console.error('Error processing bank statement:', error);
                setError(error.response?.data?.details || error.message || "Failed to process bank statement. Please try again.");
            } finally {
                setIsLoading(false);
            }
        }
    };

    const handleProceedToInvoiceUpload = () => {
        setIsBankStatementUploaded(true);
        setIsInvoiceUploaded(false);
    };


    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'QAR'
        }).format(amount);
    };

    return (
        <div>
            <div className="flex flex-col items-center mt-4">
                <div className="self-stretch text-2xl font-bold leading-10 text-stone-900 max-md:mt-4 text-center">
                    Upload Bank Statements
                </div>
                <div className="self-stretch mt-4 text-lg leading-8 text-gray-600 text-center">
                    Please upload your last 12 months' bank statements for credit analysis before you upload your invoices for financing
                </div>
                <div className="self-stretch mt-4 text-xl leading-8 text-[#004141]-900 font-bold text-center">
                    Bank Statements
                </div>
            </div>

            {isLoading ? (
                <div className="flex flex-col items-center justify-center mt-8 mb-4">
                    <div className="w-12 h-12 border-4 border-[#208039] border-t-transparent rounded-md animate-spin"></div>
                    <p className="mt-4 text-lg text-neutral-700">Processing bank statement...</p>
                </div>
            ) : (
                <>
                    {error && (
                        <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-md">
                            {error}
                        </div>
                    )}

                    {!isStatementSubmitted && (
                        <div className="flex flex-col items-center mt-8 mb-4">
                            <label className="flex justify-center items-center px-4 py-2 text-lg font-semibold leading-7 bg-[#208039] rounded-md cursor-pointer">
                                <i className="fas fa-upload mr-2" style={{ color: "white" }}></i>
                                <span style={{ color: "white" }}>Upload Bank Statement</span>
                                <input
                                    type="file"
                                    accept=".pdf"
                                    onChange={handleFileUpload}
                                    style={{ display: "none" }}
                                />
                            </label>
                        </div>
                    )}

                    {isStatementSubmitted && uploadedFileName && (
                        <div className="mt-4 px-4">
                            <ul className="list-disc mt-4">
                                <li className="text-lg leading-8 text-[#004141]-900">
                                    Bank Statement: {uploadedFileName}
                                    <i className="fas fa-check ml-2" style={{ color: '#32CD32' }}></i>
                                </li>
                            </ul>

                            <div className="mt-4">
                                <input
                                    type="checkbox"
                                    id="consentCheckbox"
                                    checked={true}
                                    readOnly
                                    className="mr-2"
                                />
                                <label htmlFor="consentCheckbox">
                                    Bank statements pending verification.
                                </label>
                            </div>

                            <div
                                className="mt-8 flex justify-center items-center px-20 py-2 text-lg font-semibold leading-7 text-white bg-[#208039] rounded-md cursor-pointer"
                                onClick={handleProceedToInvoiceUpload}
                            >
                                Proceed to Invoice Upload
                            </div>
                        </div>
                    )}
                </>
            )}

            {/* Bank Statement Details - Right Side */}
            {isStatementSubmitted && bankDetails && (
                <div className="fixed right-10 top-1/2 -translate-y-1/2 bg-white p-4 rounded-md shadow-lg w-[400px] text-sm z-20 max-h-[80vh] overflow-y-auto">
                    <div className="text-xl font-bold leading-10 text-stone-900 text-center mb-4">
                        Bank Statement Details
                    </div>

                    <div className="space-y-4">
                        <div className="bg-gray-50 p-4 rounded-md">
                            <h4 className="text-lg font-semibold mb-3">Account Information</h4>
                            <div className="grid gap-2">
                                <p><strong>Customer Name:</strong> {bankDetails.customerName}</p>
                                <p><strong>Customer ID:</strong> {bankDetails.customerId}</p>
                                <p><strong>Document Type:</strong> {bankDetails.documentType}</p>
                                <p><strong>Category:</strong> {bankDetails.category}</p>
                            </div>
                        </div>

                        <div className="bg-gray-50 p-4 rounded-md">
                            <h4 className="text-lg font-semibold mb-3">Financial Overview</h4>
                            <p><strong>Total Amount:</strong> {formatCurrency(bankDetails.totalAmount)}</p>
                        </div>

                        <div className="bg-gray-50 p-4 rounded-md">
                            <h4 className="text-lg font-semibold mb-3">Transaction History</h4>
                            <div className="overflow-x-auto">
                                <table className="min-w-full table-auto">
                                    <thead>
                                        <tr className="bg-gray-100">
                                            <th className="px-4 py-2 text-left">Description</th>
                                            <th className="px-4 py-2 text-right">Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {bankDetails.lineItems?.map((item, index) => (
                                            <tr key={index} className="border-b">
                                                <td className="px-4 py-2 text-sm">{item.description}</td>
                                                <td className="px-4 py-2 text-right text-sm">
                                                    {formatCurrency(item.totalAmount)}
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* PDF Preview */}
            {pdfPreview && (
                <div className="fixed left-10 top-1/2 -translate-y-1/2 bg-white p-4 rounded-md shadow-lg w-[400px] text-sm z-20">
                    <div className="text-xl font-bold leading-10 text-stone-900 text-center">
                        Bank Statement Preview
                    </div>
                    <div className="mt-4">
                        <iframe
                            src={pdfPreview}
                            width="100%"
                            height="600px"
                            title="Bank Statement Preview"
                            style={{ border: 'none' }}
                        />
                    </div>
                </div>
            )}
        </div>
    );
};

export default BankStatementUpload;