import React, { useState, useEffect } from "react";
import { formatTime } from "../utils";

const OTPVerification = ({ setIsOtpVerified }) => {
    const [otp, setOtp] = useState("");
    const [timer, setTimer] = useState(119); // 2 minutes

    useEffect(() => {
        let interval;
        if (timer > 0) {
            interval = setInterval(() => {
                setTimer((prevTimer) => prevTimer - 1);
            }, 1000);
        }
        return () => clearInterval(interval);
    }, [timer]);

    const handleOtpVerification = () => {
        if (otp.length === 6) {
            setIsOtpVerified(true);
        }
    };

    return (
        <>
            <div className="self-stretch mt-6 text-2xl font-bold leading-10 text-stone-900 max-md:mt-4 text-center">
                OTP sent to your mobile number registered with GSTN
            </div>
            <div className="flex flex-col self-stretch pb-2 mt-8 bg-white bg-opacity-0 max-md:mt-4">
                <input
                    type="text"
                    value={otp}
                    onChange={(e) => setOtp(e.target.value)}
                    className="justify-center items-start px-4 py-2 text-lg leading-8 text-gray-500 bg-white rounded-md border border-gray-300 border-solid max-md:pr-3"
                    placeholder="Enter OTP"
                    autoFocus
                />
                <div className="flex justify-center items-center mt-2">
                    <span className="text-gray-600 text-sm">Resend OTP in </span>
                    <span className="ml-2 text-gray-600 text-sm font-bold">
                        {formatTime(timer)}
                    </span>
                </div>
                <div
                    className={`flex justify-center items-center self-stretch px-3 py-2 mt-6 text-lg font-semibold leading-7 ${
                        otp.length === 6
                            ? "text-white bg-[#004141] cursor-pointer"
                            : "text-gray-500 bg-gray-300 cursor-not-allowed"
                    } rounded-md max-md:px-4`}
                    onClick={handleOtpVerification}
                >
                    Verify OTP
                </div>
            </div>
        </>
    );
};

export default OTPVerification;