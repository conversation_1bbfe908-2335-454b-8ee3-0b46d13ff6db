import React from "react";

const GSTNForm = ({ gstin, setGstin, gstinUserName, setGstinUserName, setIsOtpSent }) => {
    return (
        <>
            <div className="self-stretch align-center mt-2 text-2xl font-bold leading-10 text-stone-900 max-md:mt-4">
                Enter the following details to proceed
            </div>
            <div className="flex flex-col self-stretch pb-2 mt-2 bg-white bg-opacity-0 max-md:mt-4">
                <div className="flex mt-4 flex-col pb-1 text-base leading-6 bg-white bg-opacity-0 text-neutral-800">
                    <div className="justify-center bg-white bg-opacity-0">
                        GSTIN
                    </div>
                </div>
                <input
                    type="text"
                    value={gstin}
                    onChange={(e) => setGstin(e.target.value)}
                    className="justify-center items-start px-4 py-2 text-lg leading-8 text-gray-500 bg-white rounded-md border border-gray-300 border-solid max-md:pr-3"
                    placeholder="GSTIN"
                    autoFocus
                />
                <div className="flex mt-4 flex-col pb-1 text-base leading-6 bg-white bg-opacity-0 text-neutral-800">
                    <div className="justify-center bg-white bg-opacity-0">
                        GSTIN User Name
                    </div>
                </div>
                <input
                    type="text"
                    value={gstinUserName}
                    onChange={(e) => setGstinUserName(e.target.value)}
                    className="justify-center items-start px-4 py-2 text-lg leading-8 text-gray-500 bg-white rounded-md border border-gray-300 border-solid max-md:pr-3"
                    placeholder="GSTIN User Name"
                />
                <div
                    className="flex justify-center items-center self-stretch px-3 py-2 mt-4 text-lg font-semibold leading-7 text-white bg-[#004141] rounded-md max-md:px-4 cursor-pointer"
                    onClick={() => setIsOtpSent(true)}
                >
                    Send OTP
                </div>
            </div>
        </>
    );
};

export default GSTNForm;