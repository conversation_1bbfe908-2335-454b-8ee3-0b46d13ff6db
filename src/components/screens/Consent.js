import React, { useState } from "react";

const Consent = ({ onProceed }) => {
    const [isChecked, setIsChecked] = useState(false);

    const handleProceed = () => {
        if (isChecked) {
            onProceed();
        }
    };

    return (
        <>
            <h2 className="text-2xl font-bold mt-4">Submit Financials and Consent</h2>

            {/* Loan amount and bank logo */}
            <div className="p-4 mt-4 bg-gray-100 rounded-md shadow-lg flex items-center justify-between">
                <span>Requested amount: QAR 1,00,000</span>
            </div>

            {/* Document submissions */}
            <div className="mt-6">
                <div className="flex items-center mb-4">
                    <i className="fas fa-file-alt mr-2"></i>
                    <span className="text-lg font-bold">Provisional of Latest FY</span>
                </div>
                <p className="text-gray-600 mb-4">
                    Please enter all the information of last filing.
                </p>
                <div className="flex items-center mb-4">
                    <i className="fas fa-file-alt mr-2"></i>
                    <span className="text-lg font-bold">Income Tax Returns</span>
                </div>
                <p className="text-gray-600 mb-4">
                    2 Year Income Tax Return documents
                </p>
            </div>

            {/* Consent checkbox */}
            <div className="mt-4">
                <input
                    type="checkbox"
                    id="consentCheckbox"
                    checked={isChecked}
                    onChange={(e) => setIsChecked(e.target.checked)}
                    className="mr-2"
                />
                <label htmlFor="consentCheckbox" className="text-gray-700">
                    I, as authorized representative of the requesting entity, hereby consent, 
                    on behalf of the requesting entity, to the lending parties utilizing and 
                    accessing the aforementioned data for the credit evaluation process.
                </label>
            </div>

            {/* Proceed button */}
            <button
                className={`w-full px-4 py-2 mt-6 text-lg font-semibold rounded-md ${
                    isChecked 
                        ? "bg-[#004141] text-white" 
                        : "bg-gray-400 text-gray-700 cursor-not-allowed"
                }`}
                onClick={handleProceed}
                disabled={!isChecked}
            >
                Proceed
            </button>
        </>
    );
};

export default Consent;