import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useHistory } from 'react-router-dom';
import axios from 'axios';
import { parseISO, formatDistanceToNow, differenceInDays, format } from 'date-fns';
import { CheckCircleIcon, XCircleIcon, InformationCircleIcon, DocumentMagnifyingGlassIcon, ArrowPathIcon as RefreshIconSolid } from '@heroicons/react/24/solid'; // Using solid for consistency if needed
import { LinkIcon, ArrowDownOnSquareIcon, MagnifyingGlassIcon, ChevronDownIcon, ArrowLeftIcon } from '@heroicons/react/24/outline'; // Adjusted icons
import StatusBadge from './StatusBadge'; // Ensure this path is correct
import config from '../config';
import SharedCache from '../sharedCache';
import { useUser } from '../contexts/UserContext'; // For isDualRoleUser logic
import { toast } from 'react-toastify';

// --- Loader Component ---
const Loader = ({ message = 'Loading...' }) => (
    <div className="flex flex-col items-center justify-center py-10">
        <div className="w-10 h-10 border-4 border-[#004141] border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-3 mt-2 text-gray-600">{message}</p>
    </div>
);

// --- Helper function for calculating invoice age ---
const calculateInvoiceAge = (dateString) => {
    if (!dateString) return 'N/A';
    try {
        const date = parseISO(dateString);
        if (isNaN(date.getTime())) return 'Invalid Date';
        // Show "X days ago", "X months ago", or "today" if very recent.
        const diff = differenceInDays(new Date(), date);
        if (diff === 0) return 'Today';
        if (diff === 1) return '1 day ago';
        return `${formatDistanceToNow(date)} ago`; // Default 'about X days/months'
    } catch (error) {
        console.error("Error parsing date for age calculation:", dateString, error);
        return 'Invalid Date';
    }
};

// --- Helper function to safely parse date strings ---
const safeParseDate = (dateString) => {
    if (!dateString) return null;
    try {
        const parsed = parseISO(dateString);
        if (isNaN(parsed.getTime())) return null;
        return parsed;
    } catch (e) {
        console.error("Error parsing date:", dateString, e);
        return null;
    }
};


// --- Constants & Status Definitions for BUYER actions and display ---
const BUYER_STATUS_OPTIONS = {
    APPROVE: 'APPROVED_BY_BUYER', // "Verify" button will use this
    REJECT: 'REJECTED_BY_BUYER',
    MORE_INFO: 'MORE_INFO_NEEDED_BUYER',
};

// All possible statuses for display, styling, and filtering in this view
// Adjusted to match screenshot colors where possible
const STATUS_STYLES = {
    'PENDING_MSME_UPLOAD': { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-300' },
    'VERIFICATION_PENDING_ANCHOR': { bg: 'bg-yellow-400', text: 'text-yellow-900', border: 'border-yellow-500' }, // Yellow like screenshot
    'APPROVED_BY_BUYER': { bg: 'bg-green-500', text: 'text-white', border: 'border-green-600' },         // Green like screenshot
    'REJECTED_BY_BUYER': { bg: 'bg-red-500', text: 'text-white', border: 'border-red-600' },           // Red
    'MORE_INFO_NEEDED_BUYER': { bg: 'bg-orange-400', text: 'text-orange-900', border: 'border-orange-500' },// Orange
    'VERIFICATION_PENDING_LENDER': { bg: 'bg-blue-400', text: 'text-blue-900', border: 'border-blue-500' }, // Blue
    'ACCEPTED_LENDER': { bg: 'bg-sky-500', text: 'text-white', border: 'border-sky-600' }, // Blue like "Ready for disbursal"
    'REJECTED_LENDER': { bg: 'bg-pink-400', text: 'text-pink-900', border: 'border-pink-500' },
    'MORE_INFO_NEEDED_LENDER': { bg: 'bg-amber-400', text: 'text-amber-900', border: 'border-amber-500' },
    'DISBURSED': { bg: 'bg-teal-500', text: 'text-white', border: 'border-teal-600' },                 // Teal like screenshot
    'LOAN_IN_PROGRESS': { bg: 'bg-purple-400', text: 'text-purple-900', border: 'border-purple-500' },
    'DEFAULT': { bg: 'bg-gray-400', text: 'text-gray-900', border: 'border-gray-500' }
};

// Display names for statuses relevant to the Buyer's view, matching screenshot
const STATUS_DISPLAY_NAMES = {
    'VERIFICATION_PENDING_ANCHOR': 'Pending Verification', // Matches screenshot
    'APPROVED_BY_BUYER': 'Verified Buyer',         // Matches screenshot
    'REJECTED_BY_BUYER': 'Rejected by You',
    'MORE_INFO_NEEDED_BUYER': 'More Info Requested', // Simplified
    'VERIFICATION_PENDING_LENDER': 'Pending Lender',  // Simplified
    'ACCEPTED_LENDER': 'Ready for disbursal',    // Matches screenshot logic
    'REJECTED_LENDER': 'Lender Rejected',
    'MORE_INFO_NEEDED_LENDER': 'Lender Needs Info',
    'DISBURSED': 'Disbursed',                  // Matches screenshot
    'LOAN_IN_PROGRESS': 'Loan in Progress',
    'PENDING_MSME_UPLOAD': 'Pending MSME Upload',
    'UNKNOWN': 'Unknown Status'
};


const BuyerInvoiceVerificationPage = () => {
    const history = useHistory();
    const { isDualRoleUser } = useUser();

    const [isLoading, setIsLoading] = useState(false);
    const [isFetching, setIsFetching] = useState(true);
    const [showPdfPreview, setShowPdfPreview] = useState(false);
    const [pdfUrl, setPdfUrl] = useState('');
    const [invoices, setInvoices] = useState([]);
    const [fetchError, setFetchError] = useState(null);
    const [selectedInvoice, setSelectedInvoice] = useState(null);
    const [verificationComments, setVerificationComments] = useState('');
    const [buyerLenderName, setBuyerLenderName] = useState(''); // This is the current user's org name, e.g., Sunkey Mills

    // Filters from screenshot for main page
    const [searchTerm, setSearchTerm] = useState(''); // For "Search by Business or Borrower Name"
    const [statusFilter, setStatusFilter] = useState(''); // "Status: All"
    const [ageFilter, setAgeFilter] = useState(''); // "Age: All"
    const [minAmountFilter, setMinAmountFilter] = useState(''); // "Minimum Amount"
    const [maxAmountFilter, setMaxAmountFilter] = useState(''); // "Maximum Amount"
    const [dateFilter, setDateFilter] = useState('all'); // "Date: Today" - can be 'today', 'last7', 'last30', 'thisYear', 'all'

    // --- Existing filters to map or integrate ---
    // const [customDateRange, setCustomDateRange] = useState({ startDate: '', endDate: '' });
    // const [invoiceNumberFilter, setInvoiceNumberFilter] = useState(''); // May integrate into general search or keep if distinct
    // const [supplierNameFilter, setSupplierNameFilter] = useState(''); // Replaced by searchTerm
    // const [amountRangeFilter, setAmountRangeFilter] = useState({ min: '', max: '' }); // Replaced by minAmountFilter, maxAmountFilter
    // const [useCustomDateRange, setUseCustomDateRange] = useState(false);

    const [filteredInvoices, setFilteredInvoices] = useState([]);
    const fileInputRef = useRef(null);

    useEffect(() => {
        const storedBuyerData = SharedCache.get('buyerFullData');
        if (storedBuyerData && storedBuyerData.lenderName) {
            setBuyerLenderName(storedBuyerData.lenderName);
        } else {
            console.warn("Buyer's lenderName (organization name) not found. Using placeholder. This might affect API calls.");
            // setBuyerLenderName("Sunkey Mills"); // Placeholder for UI, actual fetch needs correct name
            setFetchError("Your organization name could not be retrieved. Invoice fetching may fail.");
            // setIsFetching(false); // Keep true if fetchInvoices will run and handle it
        }
    }, []);

    const fetchInvoices = useCallback(async () => {
        if (!buyerLenderName) {
            setIsFetching(false);
            setFetchError("Cannot fetch invoices without your organization's name.");
            return;
        }
        setIsFetching(true);
        setFetchError(null);
        try {
            const response = await axios.post(`${config.apiUrl}/ops/invoiceFinancing/fetchInvoicesForBuyer`,
                { buyerAdminLenderName: buyerLenderName },
                { headers: { 'Content-Type': 'application/json' } }
            );

            if (response.data && Array.isArray(response.data)) {
                const fetchedInvoices = response.data.map(invoice => ({
                    ...invoice,
                    totalAmount: parseFloat(String(invoice.totalAmount).replace(/[^0-9.]/g, "")) || 0,
                    // Ensure supplierName and customerName are present
                    supplierName: invoice.supplierName || 'N/A',
                    customerName: invoice.customerName || buyerLenderName, // Fallback customer name to current user's org
                })).sort((a, b) => {
                    const statusOrder = { 'VERIFICATION_PENDING_ANCHOR': 1, 'MORE_INFO_NEEDED_BUYER': 2 };
                    const orderA = statusOrder[a.status] || 99;
                    const orderB = statusOrder[b.status] || 99;
                    if (orderA !== orderB) return orderA - orderB;
                    const dateA = safeParseDate(a.createdAt || a.invoiceDate);
                    const dateB = safeParseDate(b.createdAt || b.invoiceDate);
                    return (dateB?.getTime() || 0) - (dateA?.getTime() || 0);
                });
                setInvoices(fetchedInvoices);
            } else {
                setInvoices([]);
                setFetchError(response.data?.message || 'No invoices found or unexpected API response.');
            }
        } catch (error) {
            console.error('Error fetching buyer invoices:', error.response?.data || error.message || error);
            setFetchError(error.response?.data?.message || error.message || "An unknown error occurred.");
            setInvoices([]);
        } finally {
            setIsFetching(false);
        }
    }, [buyerLenderName]);

    useEffect(() => {
        if (buyerLenderName) {
            fetchInvoices();
        }
    }, [buyerLenderName, fetchInvoices]);


    useEffect(() => {
        let tempInvoices = [...invoices];

        // Search by Business or Borrower Name (Supplier Name)
        if (searchTerm.trim()) {
            tempInvoices = tempInvoices.filter(inv =>
                inv.supplierName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                inv.invoiceNumber?.toLowerCase().includes(searchTerm.toLowerCase()) // Also search invoice number here
            );
        }

        // Status Filter
        if (statusFilter) {
            tempInvoices = tempInvoices.filter(inv => inv.status === statusFilter);
        }

        // Age Filter (based on createdAt/uploadedAt)
        if (ageFilter) {
            const now = new Date();
            tempInvoices = tempInvoices.filter(inv => {
                const relevantDate = safeParseDate(inv.createdAt || inv.uploadedAt || inv.insertedOn || inv.invoiceDate);
                if (!relevantDate) return false;
                const diffDays = differenceInDays(now, relevantDate);
                switch (ageFilter) {
                    case '0-7': return diffDays <= 7;
                    case '8-30': return diffDays > 7 && diffDays <= 30;
                    case '31-90': return diffDays > 30 && diffDays <= 90;
                    case 'over90': return diffDays > 90;
                    default: return true;
                }
            });
        }

        // Date Filter (based on invoiceDate)
        if (dateFilter !== 'all') {
            const today = new Date();
            today.setHours(0, 0, 0, 0); // Start of today

            tempInvoices = tempInvoices.filter(inv => {
                const invDate = safeParseDate(inv.invoiceDate);
                if (!invDate) return false;
                invDate.setHours(0, 0, 0, 0); // Normalize invoice date

                if (dateFilter === 'today') {
                    return invDate.getTime() === today.getTime();
                }
                // Add other dateFilter conditions (last7, last30, thisYear) if needed,
                // This might overlap with 'Age' filter logic, ensure clarity.
                // For now, 'Today' is implemented as an example.
                // The screenshot's "Date: Today" might imply invoice date is today.
                // The "This Year" next to Export is a separate global filter usually.
                return true; // Fallback if more date options needed
            });
        }


        // Amount Range Filter
        if (minAmountFilter || maxAmountFilter) {
            tempInvoices = tempInvoices.filter(inv => {
                const amount = inv.totalAmount;
                const min = minAmountFilter ? parseFloat(minAmountFilter) : -Infinity;
                const max = maxAmountFilter ? parseFloat(maxAmountFilter) : Infinity;
                return amount >= min && amount <= max;
            });
        }

        setFilteredInvoices(tempInvoices);
    }, [invoices, searchTerm, statusFilter, ageFilter, minAmountFilter, maxAmountFilter, dateFilter]);


    const getStatusStyle = (status) => {
        const style = STATUS_STYLES[status] || STATUS_STYLES['DEFAULT'];
        return `${style.bg} ${style.text} ${style.border}`; // Ensure border is used if defined
    };

    const getStatusDisplay = (status) => {
        return STATUS_DISPLAY_NAMES[status] || status?.replace(/_/g, ' ') || STATUS_DISPLAY_NAMES['UNKNOWN'];
    };

    const formatAmount = (amount) => {
        if (amount === null || amount === undefined || amount === '') return 'N/A';
        const numAmount = Number(amount);
        if (isNaN(numAmount)) return 'Invalid Amount';
        return `QAR ${numAmount.toLocaleString('en-QA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    };

    const getFilenameFromPath = (path) => {
        if (!path) return 'document';
        try {
            const url = new URL(path);
            return decodeURIComponent(url.pathname.split('/').pop() || 'document');
        } catch (e) {
            return decodeURIComponent(path.split(/[/\\]/).pop() || 'document');
        }
    };

    const handleViewInvoice = (invoice) => {
        if (invoice && invoice._id) {
            // Navigate to the InvoiceVerificationPage route
            // The route path (e.g., `/invoice-detail/${invoice._id}`) needs to be configured in your React Router setup
            history.push({
                pathname: `/buyer-invoice-verify/${invoice._id}`, // Or your actual route for invoice detail
                state: { invoiceData: invoice } // Pass invoice data in location state
            });
        } else {
            toast.error('Could not retrieve invoice details for navigation.');
            console.error("Invoice data or _id is missing for navigation:", invoice);
        }
    };

    // For modal detail items - to match screenshot (label above, value below)
    const renderModalDetailItem = (label, value, isAmount = false) => (
        <div className="mb-3">
            <span className="text-xs text-gray-500 block">{label}</span>
            <span className={`text-sm font-medium text-gray-800 block break-words ${isAmount ? 'text-lg font-bold' : ''}`}>
                {value || 'N/A'}
            </span>
        </div>
    );

    const WelcomeHeader = ({ userName }) => (
        <div className="mb-6 flex flex-col sm:flex-row justify-between items-center">
            <div>
                <h1 className="text-2xl font-bold text-gray-800">Welcome, {userName || "User"}!</h1>
                <p className="text-sm text-gray-500">Here's your invoice summary as of {format(new Date(), 'MMMM dd, yyyy')}</p>
            </div>
            <div className="flex items-center space-x-3 mt-4 sm:mt-0">
                <button className="px-4 py-2 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 flex items-center">
                    Export <ArrowDownOnSquareIcon className="w-4 h-4 ml-2" />
                </button>
                <div className="relative">
                    <select className="appearance-none px-4 py-2 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-[#004141] pr-8">
                        <option>This Year</option>
                        <option>Last 30 Days</option>
                        <option>Last 7 Days</option>
                    </select>
                    <ChevronDownIcon className="w-4 h-4 text-gray-500 absolute right-2.5 top-1/2 transform -translate-y-1/2 pointer-events-none" />
                </div>
            </div>
        </div>
    );

    // Define unique keys for STATUS_DISPLAY_NAMES used in filter dropdown
    const filterableStatuses = Object.entries(STATUS_DISPLAY_NAMES)
        .filter(([key]) => STATUS_STYLES[key] && key !== 'UNKNOWN' && key !== 'PENDING_MSME_UPLOAD') // Example: filter out some
        .sort(([, a], [, b]) => a.localeCompare(b));


    return (
        <div className="p-4 sm:p-6 bg-[#F7F8FA] min-h-screen"> {/* Match screenshot bg */}
            <div className="max-w-full"> {/* Changed to full width based on screenshot look */}

                <WelcomeHeader userName={buyerLenderName} />

                <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200">
                    <h2 className="text-lg font-semibold text-gray-700 mb-4">Buyer Invoice Verification</h2>

                    {/* Filters - Replicating Screenshot PNG */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3 mb-4 items-end">
                        <div className="lg:col-span-2">
                            <label htmlFor="searchTerm" className="block text-xs font-medium text-gray-600 mb-1">Search by Business/Borrower Name or Invoice ID</label>
                            <div className="relative">
                                <MagnifyingGlassIcon className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                                <input
                                    type="text"
                                    id="searchTerm"
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    placeholder="Enter name or invoice ID"
                                    className="w-full pl-10 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-[#004141] focus:border-[#004141]"
                                />
                            </div>
                        </div>
                        <div>
                            <label htmlFor="statusFilterMain" className="block text-xs font-medium text-gray-600 mb-1">Status</label>
                            <select id="statusFilterMain" value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)} className="w-full p-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-[#004141] focus:border-[#004141] bg-white">
                                <option value="">All</option>
                                {filterableStatuses.map(([key, displayName]) => (
                                    <option key={key} value={key}>{displayName}</option>
                                ))}
                            </select>
                        </div>
                        <div>
                            <label htmlFor="ageFilterMain" className="block text-xs font-medium text-gray-600 mb-1">Age</label>
                            <select id="ageFilterMain" value={ageFilter} onChange={(e) => setAgeFilter(e.target.value)} className="w-full p-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-[#004141] focus:border-[#004141] bg-white">
                                <option value="">All</option>
                                <option value="0-7">0-7 days</option>
                                <option value="8-30">8-30 days</option>
                                <option value="31-90">31-90 days</option>
                                <option value="over90">Over 90 days</option>
                            </select>
                        </div>
                        <div>
                            <label htmlFor="minAmountFilterMain" className="block text-xs font-medium text-gray-600 mb-1">Min Amount</label>
                            <input type="number" id="minAmountFilterMain" value={minAmountFilter} onChange={(e) => setMinAmountFilter(e.target.value)} placeholder="0" className="w-full p-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-[#004141] focus:border-[#004141]" />
                        </div>
                        <div>
                            <label htmlFor="maxAmountFilterMain" className="block text-xs font-medium text-gray-600 mb-1">Max Amount</label>
                            <input type="number" id="maxAmountFilterMain" value={maxAmountFilter} onChange={(e) => setMaxAmountFilter(e.target.value)} placeholder="100,000" className="w-full p-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-[#004141] focus:border-[#004141]" />
                        </div>
                        {/* Date filter - simple version, can be expanded */}
                        {/* <div>
                            <label htmlFor="dateFilterMain" className="block text-xs font-medium text-gray-600 mb-1">Date</label>
                            <select id="dateFilterMain" value={dateFilter} onChange={(e) => setDateFilter(e.target.value)} className="w-full p-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-[#004141] focus:border-[#004141] bg-white">
                                <option value="all">All Dates</option>
                                <option value="today">Today</option>
                            </select>
                        </div> */}
                    </div>
                    <div className="flex justify-end items-center mt-1 mb-4">
                        <button
                            onClick={fetchInvoices}
                            disabled={isFetching || !buyerLenderName}
                            className="mr-2 p-2 text-sm text-gray-600 hover:text-[#004141] flex items-center disabled:opacity-50"
                            title="Refresh Invoices"
                        >
                            <RefreshIconSolid className={`w-4 h-4 ${isFetching ? 'animate-spin' : ''}`} />
                        </button>
                        <p className="text-sm text-gray-600">{filteredInvoices.length} invoice(s) found</p>
                    </div>


                    {fetchError && (<div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-3 rounded mb-4 text-sm" role="alert"><p><strong className="font-semibold">Error:</strong> {fetchError}</p></div>)}

                    <div className="overflow-x-auto">
                        <table className="w-full min-w-[900px]"> {/* Min width for horizontal scroll */}
                            <thead className="bg-gray-50">
                                <tr>
                                    {/* Updated headers to match Screenshot.png */}
                                    {["Invoice ID", "Invoice Date", "Age", "Buyer", "Trade/Legal Name", "Invoice Amount", "Status", "Actions"].map(header => (
                                        <th key={header} className={`p-3 text-xs font-semibold text-gray-500 uppercase tracking-wider ${header === "Invoice Amount" ? "text-right" : "text-left"}`}>
                                            {header}
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {isFetching ? (
                                    <tr><td colSpan="8"><Loader message="Fetching invoices..." /></td></tr>
                                ) : filteredInvoices.length === 0 ? (
                                    <tr><td colSpan="8" className="p-6 text-center text-sm text-gray-500">{searchTerm || statusFilter || ageFilter || minAmountFilter || maxAmountFilter ? 'No invoices match current filters.' : 'No invoices found.'}</td></tr>
                                ) : (
                                    filteredInvoices.map((invoice) => (
                                        <tr key={invoice._id} className="hover:bg-gray-50 transition-colors duration-150">
                                            <td className="p-3 text-sm text-blue-600 hover:underline font-medium whitespace-nowrap">
                                                <button onClick={() => handleViewInvoice(invoice)} title={`View ${invoice.invoiceNumber}`}>
                                                    {invoice.invoiceNumber || 'N/A'}
                                                </button>
                                            </td>
                                            <td className="p-3 text-sm text-gray-700 whitespace-nowrap">{invoice.invoiceDate ? format(safeParseDate(invoice.invoiceDate), 'dd MMM, yyyy') : 'N/A'}</td>
                                            <td className="p-3 text-sm text-gray-700 whitespace-nowrap">{calculateInvoiceAge(invoice.createdAt || invoice.uploadedAt || invoice.invoiceDate)}</td>
                                            {/* "Buyer" in screenshot table is the Supplier from current user's perspective */}
                                            <td className="p-3 text-sm text-gray-700 whitespace-nowrap max-w-[180px] truncate" title={invoice.supplierName}>{invoice.supplierName}</td>
                                            {/* "Trade/Legal Name" in screenshot table is the current user's company */}
                                            <td className="p-3 text-sm text-gray-700 whitespace-nowrap max-w-[180px] truncate" title={invoice.customerName}>{invoice.customerName}</td>
                                            <td className="p-3 text-sm text-gray-800 font-semibold whitespace-nowrap text-right">{formatAmount(invoice.totalAmount)}</td>
                                            <td className="p-3 text-sm whitespace-nowrap">
                                                <StatusBadge
                                                    status={invoice.status}
                                                    displayText={getStatusDisplay(invoice.status)}
                                                    baseClasses="px-2.5 py-1 text-xs font-semibold rounded-full leading-tight" // Adjusted for screenshot like badges
                                                    colorClass={getStatusStyle(invoice.status)}
                                                />
                                            </td>
                                            <td className="p-3 text-sm text-center whitespace-nowrap">
                                                <button onClick={() => handleViewInvoice(invoice)} className="text-blue-600 hover:text-blue-800 font-medium py-1 px-2 rounded-md hover:bg-blue-50 transition-colors duration-150 text-xs">
                                                    View Detail
                                                </button>
                                            </td>
                                        </tr>
                                    ))
                                )}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default BuyerInvoiceVerificationPage;