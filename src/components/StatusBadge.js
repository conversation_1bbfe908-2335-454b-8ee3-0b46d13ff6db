import React from 'react';

const StatusBadge = ({ status }) => {
    let badgeClasses = "px-2 py-1 rounded-full text-xs font-semibold ";
    let statusText = "Unknown Status"; // Default text if status is undefined/null

    // Add a null/undefined check for the status prop
    if (!status || typeof status !== 'string') {
        badgeClasses += "bg-gray-100 text-gray-800";
        return (
            <span className={badgeClasses}>
                {statusText}
            </span>
        );
    }

    statusText = status; // Initialize with the raw status for switch

    switch (status) {
        case 'PENDING_BUYER_APPROVAL':
        case 'VERIFICATION_PENDING_ANCHOR':
            badgeClasses += "bg-yellow-100 text-yellow-800";
            statusText = "Pending Approval";
            break;
        case 'APPROVED_BY_BUYER':
        case 'VERIFIED_ANCHOR':
            badgeClasses += "bg-green-100 text-green-800";
            statusText = "Approved";
            break;
        case 'REJECTED_BY_BUYER':
        case 'REJECTED_ANCHOR':
            badgeClasses += "bg-red-100 text-red-800";
            statusText = "Rejected";
            break;
        case 'PAYMENT_PENDING':
            badgeClasses += "bg-blue-100 text-blue-800";
            statusText = "Payment Pending";
            break;
        case 'PAID':
            badgeClasses += "bg-purple-100 text-purple-800";
            statusText = "Paid";
            break;
        case 'MORE_INFO_NEEDED_ANCHOR':
        case 'MORE_INFO_NEEDED_LENDER':
            badgeClasses += "bg-orange-100 text-orange-800";
            statusText = "More Info Needed";
            break;
        default:
            badgeClasses += "bg-gray-100 text-gray-800";
            // Ensure status is a string before using string methods
            statusText = status.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, char => char.toUpperCase());
            break;
    }

    return (
        <span className={badgeClasses}>
            {statusText}
        </span>
    );
};

export default StatusBadge;