import React, { useState, useEffect, useRef } from "react";
import { useHistory, useLocation } from "react-router-dom";
import { registerNewUser, sendOtpEmail, verifyOtpEmail, sendOtpPhone, verifyOtpPhone } from "../api/auth"; // Ensure all are imported
import SharedCache from "../sharedCache";
import {
  countries,
  getCountryFlagEmojiFromCountryCode,
  // getCountryFromCountryCode, // Not used directly in the provided snippet, can be removed if not needed elsewhere
} from 'country-codes-flags-phone-codes';
import backdrop from "../images/backdrop.png";

const OnboardingPage = () => {
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [legalEntityName, setLegalEntityName] = useState("");
  const [crNumber, setCrNumber] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  // Password fields are present in state but not in the visible form based on your provided JSX.
  // If they are part of a different flow or hidden section, their logic remains untouched.
  //test
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isQatarBased, setIsQatarBased] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [firstNameError, setFirstNameError] = useState("");
  const [lastNameError, setLastNameError] = useState("");
  const [legalEntityNameError, setLegalEntityNameError] = useState("");
  const [crNumberError, setCrNumberError] = useState("");
  const [phoneError, setPhoneError] = useState("");
  const [emailError, setEmailError] = useState("");
  // Password errors
  const [passwordError, setPasswordError] = useState("");
  const [confirmPasswordError, setConfirmPasswordError] = useState("");
  const [qatarBasedError, setQatarBasedError] = useState("");
  const [submissionError, setSubmissionError] = useState("");
  const [registrationSuccess, setRegistrationSuccess] = useState(false);

  // Email Verification States
  const [isEmailVerified, setIsEmailVerified] = useState(true); // Changed to true
  const [emailOtpSent, setEmailOtpSent] = useState(false);
  const [emailOtp, setEmailOtp] = useState("");
  const [emailOtpError, setEmailOtpError] = useState("");
  const [isSendingEmailOtp, setIsSendingEmailOtp] = useState(false);

  // Phone Verification States
  const [isPhoneVerified, setIsPhoneVerified] = useState(false);
  const [phoneOtpSent, setPhoneOtpSent] = useState(false);
  const [phoneOtp, setPhoneOtp] = useState("");
  const [phoneOtpError, setPhoneOtpError] = useState("");
  const [isSendingPhoneOtp, setIsSendingPhoneOtp] = useState(false);
  const [isGoogleEmail, setIsGoogleEmail] = useState(false);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const queryEmail = queryParams.get("email");
  const gmail = queryParams.get("gmail"); // Email from Google Sign-In
  const history = useHistory();
  const containerRef = useRef(null);
  const [index, setIndex] = useState(0);
  const allowedCountries = ["IN", "SA", "AE", "QA"];
  const [selectedCountry, setSelectedCountry] = useState({
    code: 'QA',
    name: 'Qatar',
    dialCode: '+974',
    flag: getCountryFlagEmojiFromCountryCode('QA'),
  });
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setIndex((prev) => (prev + 1) % messages.length);
    }, 3000);
    return () => clearInterval(interval);
  }, []);


  useEffect(() => {
    const qp = new URLSearchParams(location.search);
    const phoneFromLoginState = location.state?.phone; // From mobile OTP signup on LoginPage
    console.log(qp, "qpsppf here");

    const userFromCache = SharedCache.get("user") || {};

    const qpFirstName = qp.get("firstName"); // From Google
    const qpLastName = qp.get("lastName");   // From Google
    const qpGmail = qp.get("gmail");         // Email from Google Sign-In
    const qpEmailFromLink = qp.get("email"); // Email from our verification link or other sources

    if (qpFirstName && !firstName) setFirstName(qpFirstName);
    if (qpLastName && !lastName) setLastName(qpLastName);

    let newEmailToSet = email;
    let newPhoneToSet = phone;
    let emailShouldBeVerifiedInitially = isEmailVerified;
    let phoneShouldBeVerifiedInitially = isPhoneVerified;

    if (qpGmail) {
      newEmailToSet = qpGmail;
      emailShouldBeVerifiedInitially = true;
      setIsGoogleEmail(true); // ✅ add this

      if (!phone && userFromCache.mobileNo) {
        newPhoneToSet = userFromCache.mobileNo.toString().replace(/^(\+?974)/, "");
      }
    } else if (qpEmailFromLink) {
      newEmailToSet = qpEmailFromLink;

      if (userFromCache.googleId) {
        emailShouldBeVerifiedInitially = true;
        setIsGoogleEmail(true); // ✅ add this
      } else {
        emailShouldBeVerifiedInitially = false;
      }

      if (!phone && userFromCache.mobileNo) {
        newPhoneToSet = userFromCache.mobileNo.toString().replace(/^(\+?974)/, "");
      }
    } else if (phoneFromLoginState) {
      newPhoneToSet = phoneFromLoginState.toString().replace(/^(\+?974)/, "");
      phoneShouldBeVerifiedInitially = true;

      if (!email && userFromCache.email) {
        newEmailToSet = userFromCache.email;
        if (userFromCache.googleId) {
          emailShouldBeVerifiedInitially = true;
          setIsGoogleEmail(true); // ✅ add this
        }
      }
    } else {
      if (!email && userFromCache.email) {
        newEmailToSet = userFromCache.email;
        if (userFromCache.googleId) {
          emailShouldBeVerifiedInitially = true;
          setIsGoogleEmail(true); // ✅ already here, good
        }
      }
      if (!phone && userFromCache.mobileNo) {
        newPhoneToSet = userFromCache.mobileNo.toString().replace(/^(\+?974)/, "");
      }
    }


    if (newEmailToSet && newEmailToSet !== email) {
      setEmail(newEmailToSet);
    }
    if (newPhoneToSet && newPhoneToSet !== phone) {
      setPhone(newPhoneToSet);
    }

    if (emailShouldBeVerifiedInitially !== isEmailVerified && !emailOtpSent && !isEmailVerified) {
      setIsEmailVerified(emailShouldBeVerifiedInitially);
    }
    if (phoneShouldBeVerifiedInitially !== isPhoneVerified && !phoneOtpSent && !isPhoneVerified) {
      setIsPhoneVerified(phoneShouldBeVerifiedInitially);
    }

  }, [location.search, location.state]);

  // Inside OnboardingPage.js
  const handleCountrySelect = (ctry) => {
    setSelectedCountry(ctry);
    setIsDropdownOpen(false);
    if (phone) { // If a phone number is already entered
      setIsPhoneVerified(false); // <<< ADD THIS
      setPhoneOtpSent(false);    // <<< ADD THIS
      setPhoneOtp("");           // <<< ADD THIS
      setPhoneOtpError("");      // <<< ADD THIS
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => { // Add event parameter
      if (isDropdownOpen) { // Check if dropdown is open before trying to close
        // Assuming containerRef is for the dropdown itself or its direct parent for this logic.
        // A more robust way is to check if event.target is outside the dropdown element.
        // For simplicity here, just closing. You might need a ref on the dropdown.
        setIsDropdownOpen(false);
      }
    };
    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [isDropdownOpen]);


  const validateEmail = (emailToValidate) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(emailToValidate);
  };

  const validatePhone = (phoneToValidate) => {
    const phoneRegex = /^[0-9]{8,11}$/; // Assumes phone number without country code
    return phoneRegex.test(phoneToValidate.replace(/\s|-/g, ""));
  };

  const validatePassword = (passwordToValidate) => { // Renamed parameter
    return passwordToValidate.length >= 8;
  };

  const handleSendEmailOTP = async () => {
    if (!email || !validateEmail(email)) {
      setEmailError("Please enter a valid email");
      return;
    }

    setIsSendingEmailOtp(true);
    setEmailError("");
    setEmailOtpError("");
    try {
      const response = await sendOtpEmail(email);
      if (response.success) {
        setEmailOtpSent(true);
      } else {
        if (response.message?.includes("already registered") || response.status === 409) {
          setEmailError("This email is already registered. Please login instead.");
        } else {
          setEmailError(response.message || "Failed to send OTP to email");
        }
        setEmailOtpSent(false);
      }
    } catch (error) {
      console.error("Send Email OTP error:", error);
      setEmailError("An error occurred while sending email OTP");
      setEmailOtpSent(false);
    } finally {
      setIsSendingEmailOtp(false);
    }
  };

  const handleVerifyEmailOTP = async () => {
    if (!email || !emailOtp) {
      setEmailOtpError("Email and OTP are required");
      return;
    }
    try {
      const response = await verifyOtpEmail(email, emailOtp);
      if (response.success) {
        setIsEmailVerified(true);
        setEmailOtpError("");
      } else {
        setIsEmailVerified(false);
        setEmailOtpError(response.message || "Invalid email OTP");
      }
    } catch (error) {
      setIsEmailVerified(false);
      setEmailOtpError("An error occurred while verifying email OTP");
    }
  };

  const handleSendPhoneOTP = async () => {
    console.log("asdadas 1");
    const trimmedPhone = phone.trim();
    if (!trimmedPhone || !validatePhone(trimmedPhone)) {
      setPhoneError("Please enter a valid phone number (8-11 digits).");
      return;
    }
    setIsSendingPhoneOtp(true);
    setPhoneError("");
    setPhoneOtpError("");
    try {
      const phoneWithCountryCode = selectedCountry.dialCode + trimmedPhone;
      const formattedPhone = phoneWithCountryCode.replace(/^\+/, "");
      const response = await sendOtpPhone(formattedPhone);
      if (response.success) {
        setPhoneOtpSent(true);
      } else {
        setPhoneError(response.message || "Failed to send phone OTP");
        setPhoneOtpSent(false);
      }
    } catch (error) {
      console.error("Send Phone OTP error:", error);
      setPhoneError("An error occurred while sending phone OTP");
      setPhoneOtpSent(false);
    } finally {
      setIsSendingPhoneOtp(false);
    }
  };

  const handleVerifyPhoneOTP = async () => {
    const trimmedPhone = phone.trim();
    if (!trimmedPhone || !phoneOtp) {
      setPhoneOtpError("Phone number and OTP are required");
      return;
    }
    try {
      const phoneWithCountryCode = selectedCountry.dialCode + trimmedPhone;
      const formattedPhone = phoneWithCountryCode.replace(/^\+/, "");
      const response = await verifyOtpPhone(formattedPhone, phoneOtp);
      if (response.success) {
        setIsPhoneVerified(true);
        setPhoneOtpError("");
      } else {
        setIsPhoneVerified(false);
        setPhoneOtpError(response.message || "Invalid phone OTP");
      }
    } catch (error) {
      setIsPhoneVerified(false);
      setPhoneOtpError("An error occurred while verifying phone OTP");
    }
  };


  const validateForm = () => {
    let isValid = true;
    setFirstNameError("");
    setLastNameError("");
    setLegalEntityNameError("");
    setCrNumberError("");
    setEmailError("");
    setPhoneError("");
    setQatarBasedError("");
    setSubmissionError("");

    if (!firstName.trim()) {
      setFirstNameError("First name is required");
      isValid = false;
    }
    if (!lastName.trim()) {
      setLastNameError("Last name is required");
      isValid = false;
    }
    if (!legalEntityName.trim()) {
      setLegalEntityNameError("Legal Entity Name is required");
      isValid = false;
    }

    const trimmedCrNumber = crNumber.trim();
    const minLength = 5;
    const maxLength = 15; // Increased CR max length as 8 seemed short
    if (!trimmedCrNumber) {
      setCrNumberError("CR Number is required");
      isValid = false;
    } else if (!/^\d+$/.test(trimmedCrNumber)) {
      setCrNumberError("CR Number must contain only digits");
      isValid = false;
    } else if (trimmedCrNumber.length < minLength || trimmedCrNumber.length > maxLength) {
      setCrNumberError(`CR Number must be between ${minLength} and ${maxLength} digits`);
      isValid = false;
    }

    // Validate email only if it's not already verified
    // if (!isEmailVerified) {
    //   if (!email.trim()) {
    //     setEmailError("Email is required");
    //     isValid = false;
    //   } else if (!validateEmail(email)) {
    //     setEmailError("Please enter a valid email");
    //     isValid = false;
    //   }
    // }

    // Validate phone only if it's not already verified
    if (!isPhoneVerified) {
      const trimmedPhone = phone.trim();
      if (!trimmedPhone) {
        setPhoneError("Phone number is required");
        isValid = false;
      } else if (!validatePhone(trimmedPhone)) {
        setPhoneError("Please enter a valid phone number (8-11 digits)");
        isValid = false;
      }
    }

    if (isQatarBased === null) {
      setQatarBasedError("Please select if your business is based in Qatar");
      isValid = false;
    }
    return isValid;
  };

  const handleSubmit = async () => {
    if (!validateForm() || isSubmitting) {
      return;
    }

    // Ensure both email and phone are verified before final submission
    // if (!isEmailVerified) {
    //   alert("Please verify your email address.");
    //   return;
    // }
    if (!isPhoneVerified) {
      alert("Please verify your phone number.");
      return;
    }

    setIsSubmitting(true);
    try {
      const phoneWithCountryCode = selectedCountry.dialCode + phone;
      const formattedPhone = phoneWithCountryCode.replace(/^\+/, "");

      const response = await registerNewUser({
        firstName,
        lastName,
        legalEntityName,
        crNumber,
        phone: formattedPhone,
        isQatarBased,
        email,
        // password: password, // Include password if it's part of registration data
      });

      if (response.success) {
        if (response.user) {
          SharedCache.set("user", response.user);
        }
        setRegistrationSuccess(true);
      } else {
        if (response.authMethod === 'google') {
          setSubmissionError("This phone number is already associated with another account. Please sign in with Google using your email.");
        } else {
          setSubmissionError(response.message || "Failed to register user. Please try again.");
        }
      }
    } catch (error) {
      console.error("Error registering user:", error);
      setSubmissionError("An unexpected error occurred. Please try again later.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFirstNameChange = (e) => {
    setFirstName(e.target.value);
    if (firstNameError && e.target.value.trim()) setFirstNameError("");
  };
  const handleLastNameChange = (e) => {
    setLastName(e.target.value);
    if (lastNameError && e.target.value.trim()) setLastNameError("");
  };
  const handleLegalEntityNameChange = (e) => {
    setLegalEntityName(e.target.value);
    if (legalEntityNameError && e.target.value.trim()) setLegalEntityNameError("");
  };
  const handleCrNumberChange = (e) => {
    setCrNumber(e.target.value);
    // Basic CR validation on change (length, digits) could be added here if desired for immediate feedback
    if (crNumberError && e.target.value.trim()) setCrNumberError("");
  };
  // Inside OnboardingPage component
  const handleEmailChange = (e) => {
    const newEmail = e.target.value;
    setEmail(newEmail);
    // setIsEmailVerified(false); // <<< ADD THIS: Reset verification status
    setEmailOtpSent(false);    // <<< ADD THIS: Reset OTP sent status
    setEmailOtp("");           // <<< ADD THIS: Clear OTP input
    setEmailOtpError("");      // <<< ADD THIS: Clear OTP error

    if (emailError && validateEmail(newEmail)) { // Your existing logic
      setEmailError("");
    } else if (!validateEmail(newEmail) && newEmail.trim() !== "") {
      // setEmailError("Please enter a valid email format."); // Optional: immediate feedback
    } else {
      setEmailError("");
    }
  };

  const handlePhoneChange = (e) => {
    const value = e.target.value.replace(/[^0-9]/g, "");
    if (value.length <= 11) { // Your existing length check
      setPhone(value);
      setIsPhoneVerified(false); // <<< ADD THIS: Reset verification status
      setPhoneOtpSent(false);    // <<< ADD THIS: Reset OTP sent status
      setPhoneOtp("");           // <<< ADD THIS: Clear OTP input
      setPhoneOtpError("");      // <<< ADD THIS: Clear OTP error

      if (phoneError && validatePhone(value)) { // Your existing logic
        setPhoneError("");
      } else if (!validatePhone(value) && value.trim() !== "") {
        // setPhoneError("Please enter a valid phone number (8-11 digits)."); // Optional
      } else {
        setPhoneError("");
      }
    }
  };

  // Password change handlers (if password fields were to be used in the form)
  const handlePasswordChange = (e) => {
    setPassword(e.target.value);
    if (passwordError && validatePassword(e.target.value)) setPasswordError("");
    if (confirmPasswordError && e.target.value === confirmPassword) setConfirmPasswordError("");
  };
  const handleConfirmPasswordChange = (e) => {
    setConfirmPassword(e.target.value);
    if (confirmPasswordError && e.target.value === password) setConfirmPasswordError("");
  };

  // adjustMargins and its useEffect are untouched as requested.
  const adjustMargins = () => {
    const container = containerRef.current;
    if (container?.scrollHeight > container?.clientHeight) {
      const children = container.children;
      for (let i = 0; i < children.length; i++) {
        const child = children[i];
        const style = window.getComputedStyle(child);
        const marginTop = parseFloat(style.marginTop);
        const marginBottom = parseFloat(style.marginBottom);
        const newMarginTop = Math.max(marginTop - 2, 0);
        const newMarginBottom = Math.max(marginBottom - 2, 0);
        child.style.marginTop = `${newMarginTop}px`;
        child.style.marginBottom = `${newMarginBottom}px`;
      }
    }
  };

  useEffect(() => {
    adjustMargins();
    window.addEventListener("resize", adjustMargins);
    return () => {
      window.removeEventListener("resize", adjustMargins);
    };
  }, [
    firstName, lastName, legalEntityName, crNumber, email, phone, // Added phone
    password, confirmPassword, firstNameError, lastNameError,
    legalEntityNameError, crNumberError, emailError, phoneError, // Added phoneError
    passwordError, confirmPasswordError, qatarBasedError, submissionError,
    // Added verification states that might affect layout
    isEmailVerified, emailOtpSent, isPhoneVerified, phoneOtpSent
  ]);

  // isFormValid is not directly used by handleSubmit anymore, validateForm() is called instead.
  // If you need it for UI (e.g. disabling submit button directly), it needs to include OTP checks.
  // For simplicity, handleSubmit now handles the OTP verified checks.

  const navigateToLogin = () => {
    history.push("/login");
  };

  // Determine if email verification UI should be active
  const showEmailVerification = !isEmailVerified && (!isPhoneVerified || (isPhoneVerified && !isEmailVerified && !phoneOtpSent));
  // Determine if phone verification UI should be active
  const showPhoneVerification = !isPhoneVerified && isEmailVerified && !emailOtpSent;

  const messages = [
    {
      title: 'Get Cash Today, Against Due Invoices',
      text: 'Say goodbye to delays - discount your unpaid invoices for instant cash',
    },
    {
      title: 'Boost Your Business Cashflow',
      text: 'Convert invoices into working capital in days, not weeks.',
    },
    {
      title: 'Flexible Invoice Financing',
      text: 'Choose which invoices to finance and when - complete control',
    },
  ];


  return (
    <div className="flex flex-col md:flex-row font-poppins">
      {/* Left Panel (Onboarding Form Content) */}
      <div className="w-full md:w-1/2 bg-gray-100 flex justify-center items-center order-2 md:order-1 py-8 md:py-0"> {/* Added padding for mobile view */}
        <div className="w-full max-w-lg px-4 md:px-0">
          {!registrationSuccess ? (
            <div
              ref={containerRef}
              className="flex flex-col items-center w-full"
            >
              <img
                loading="lazy"
                src={require("../images/logo.jpg")}
                className="w-32 md:w-36 mb-6" // Adjusted margin
                alt="Madad Fintech Logo"
              />
              <div className="text-2xl sm:text-2xl font-bold text-gray-800 mb-6 text-center"> {/* Adjusted margin */}
                Signup to Madad
              </div>
              <div className="w-full bg-gray-50 p-6 sm:p-8 rounded-xl shadow-xl space-y-5">
                {/* First Name & Last Name */}
                <div className="flex flex-col sm:flex-row sm:space-x-4">
                  <div className="w-full sm:w-1/2 mb-5 sm:mb-0">
                    <label htmlFor="onboardingFirstName" className="block text-sm font-medium text-gray-700 mb-1">
                      First Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="onboardingFirstName"
                      value={firstName}
                      onChange={handleFirstNameChange}
                      className="w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 bg-white text-gray-900 placeholder-gray-400"
                      placeholder="John"
                      autoFocus={!queryEmail && !gmail && !location.state?.phone}
                    />
                    {firstNameError && <div className="text-red-500 text-xs mt-1">{firstNameError}</div>}
                  </div>
                  <div className="w-full sm:w-1/2">
                    <label htmlFor="onboardingLastName" className="block text-sm font-medium text-gray-700 mb-1">
                      Last Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="onboardingLastName"
                      value={lastName}
                      onChange={handleLastNameChange}
                      className="w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 bg-white text-gray-900 placeholder-gray-400"
                      placeholder="Doe"
                    />
                    {lastNameError && <div className="text-red-500 text-xs mt-1">{lastNameError}</div>}
                  </div>
                </div>

                {/* Legal Entity Name */}
                <div>
                  <label htmlFor="onboardingLegalEntityName" className="block text-sm font-medium text-gray-700 mb-1">
                    Legal Entity Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="onboardingLegalEntityName"
                    value={legalEntityName}
                    onChange={handleLegalEntityNameChange}
                    className="w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 bg-white text-gray-900 placeholder-gray-400"
                    placeholder="e.g., JD Inc"
                  />
                  {legalEntityNameError && <div className="text-red-500 text-xs mt-1">{legalEntityNameError}</div>}
                </div>

                {/* Email with Verification */}
                <div>
                  <label htmlFor="onboardingEmail" className="block text-sm font-medium text-gray-700 mb-1">
                    Email <span className="text-red-500">*</span>
                    {isGoogleEmail && (
                      <p className="text-xs text-gray-500 mt-1">This email is linked to your Google account and cannot be changed.</p>
                    )}
                  </label>
                  <div className="flex items-end space-x-2">
                    {/* The main email input field */}
                    <input
                      type="email"
                      id="onboardingEmail"
                      value={email}
                      onChange={handleEmailChange}
                      disabled={isGoogleEmail}
                      className={`flex-grow px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 text-gray-900 placeholder-gray-400
        ${isGoogleEmail ? 'bg-gray-100 cursor-not-allowed' : ''}
        ${isEmailVerified ? 'bg-green-50 border-green-300' : 'bg-white'}`}
                      placeholder="<EMAIL>"
                    />
                    {/*
    {!isEmailVerified ? (
      <>
        {!emailOtpSent ? (
          <button
            type="button"
            onClick={handleSendEmailOTP}
            disabled={isSendingEmailOtp || !email || !validateEmail(email)}
            className={`px-4 py-2.5 text-sm font-medium text-white bg-[#004141] rounded-md hover:bg-[#003535] focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-[#004141] whitespace-nowrap ${(isSendingEmailOtp || !email || !validateEmail(email)) ? 'opacity-60 cursor-not-allowed' : ''}`}
          >
            {isSendingEmailOtp ? "Sending..." : "Get OTP"}
          </button>
        ) : (
          <>
            <input
              type="text"
              value={emailOtp}
              onChange={(e) => setEmailOtp(e.target.value.replace(/\D/g, "").slice(0, 6))}
              maxLength={6}
              className="flex-grow w-24 sm:w-28 px-3 py-2.5 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 text-center placeholder-gray-400"
              placeholder="OTP"
            />
            <button
              type="button"
              onClick={handleVerifyEmailOTP}
              disabled={emailOtp.length !== 6 || isSendingEmailOtp}
              className={`px-4 py-2.5 text-sm font-medium text-white bg-[#004141] rounded-md hover:bg-[#003535] focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-green-600 whitespace-nowrap ${(emailOtp.length !== 6 || isSendingEmailOtp) ? 'opacity-60 cursor-not-allowed' : ''}`}
            >
              Verify OTP
            </button>
          </>
        )}
      </>
    ) : isGoogleEmail ? (
      <span className="px-4 py-2.5 text-sm font-medium text-green-700 bg-green-100 rounded-md whitespace-nowrap">Email Verified ✔</span>
    ) : (
      <button
        type="button"
        onClick={() => { setIsEmailVerified(false); setEmailOtpSent(false); setEmailOtp(""); setEmailOtpError(""); }}
        className="px-4 py-2.5 text-sm font-medium text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-500 whitespace-nowrap"
      >
        Edit Email
      </button>
    )}
    */}
                  </div>
                  {/*
  {emailError && !isEmailVerified && <div className="text-red-500 text-xs mt-1">{emailError}</div>}
  {emailOtpSent && !isEmailVerified && emailOtpError && <div className="text-red-500 text-xs mt-1">{emailOtpError}</div>}
  */}
                </div>

                {/* Phone Number with Verification */}
                <div>
                  <label htmlFor="onboardingPhone" className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number <span className="text-red-500">*</span>
                  </label>
                  <div className="flex items-start space-x-2">
                    <div className="relative flex-grow"> {/* Country code dropdown part remains same */}
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 cursor-pointer" onClick={(e) => { e.stopPropagation(); setIsDropdownOpen(!isDropdownOpen); }}>
                        <span className="text-base sm:text-lg">{selectedCountry.flag}</span>
                        <span className="text-sm text-gray-600 font-medium mx-1 sm:mx-2">{selectedCountry.dialCode}</span>
                        <svg className={`w-3 h-3 text-gray-500 transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path></svg>
                      </div>
                      {isDropdownOpen && (
                        <div className="absolute top-full left-0 mt-1 w-64 max-h-60 overflow-y-auto bg-white border border-gray-300 rounded-md shadow-lg z-20" onClick={(e) => e.stopPropagation()}>
                          {countries.filter((c) => allowedCountries.includes(c.code)).map((c) => (
                            <div key={c.code} className="flex items-center px-4 py-2 hover:bg-gray-100 cursor-pointer" onClick={() => handleCountrySelect(c)}>
                              <div className="mr-2 text-base sm:text-lg">{getCountryFlagEmojiFromCountryCode(c.code)}</div>
                              <div className="flex-1 text-sm">{c.name} {/* Corrected: use c.name for country name */} </div>
                              <div className="text-xs text-gray-500">{c.dialCode}</div>
                            </div>
                          ))}
                        </div>
                      )}
                      <input
                        type="tel"
                        id="onboardingPhone"
                        value={phone}
                        onChange={handlePhoneChange}
                        // UPDATED:
                        disabled={isSendingPhoneOtp} // Always allow phone input changes unless OTP is sending
                        className={`w-full px-3 py-2.5 pl-[calc(3rem+3rem)] sm:pl-[calc(3.5rem+3.5rem)] border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 text-gray-900 placeholder-gray-400 
                ${isPhoneVerified ? 'bg-green-50 border-green-300' : 'bg-white'}
                ${isSendingPhoneOtp ? 'bg-gray-100 cursor-not-allowed' : ''}`} // Disable input if sending OTP
                        placeholder="00000000"
                      />
                    </div>
                    {!isPhoneVerified ? ( // If phone is NOT verified, show these controls
                      <>
                        {!phoneOtpSent ? (
                          <button
                            type="button"
                            onClick={handleSendPhoneOTP}
                            disabled={isSendingPhoneOtp || !phone || !validatePhone(phone) || isPhoneVerified} // Disable if already verified
                            className={`px-4 py-2.5 text-sm font-medium text-white bg-[#004141] rounded-md hover:bg-[#003535] focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-[#004141] whitespace-nowrap ${(isSendingPhoneOtp || !phone || !validatePhone(phone)) ? 'opacity-60 cursor-not-allowed' : ''}`}
                          >
                            {isSendingPhoneOtp ? "Sending..." : "Get OTP"}
                          </button>
                        ) : (
                          <>
                            {/* Input for OTP */}
                            <input
                              type="text"
                              value={phoneOtp}
                              onChange={(e) => setPhoneOtp(e.target.value.replace(/\D/g, "").slice(0, 6))}
                              maxLength={6}
                              className="flex-grow w-24 sm:w-28 px-3 py-2.5 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 text-center placeholder-gray-400"
                              placeholder="OTP"
                            />
                            <button
                              type="button"
                              onClick={handleVerifyPhoneOTP}
                              disabled={phoneOtp.length !== 6 || isSendingPhoneOtp}
                              className={`px-4 py-2.5 text-sm font-medium text-white bg-[#004141] rounded-md hover:bg-[#003535] focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-green-600 whitespace-nowrap ${(phoneOtp.length !== 6 || isSendingPhoneOtp) ? 'opacity-60 cursor-not-allowed' : ''}`}
                            >
                              Verify OTP
                            </button>
                          </>
                        )}
                      </>
                    ) : ( // If phone is verified, show "Phone Verified" or "Edit Phone"
                      <button
                        type="button"
                        onClick={() => { setIsPhoneVerified(false); setPhoneOtpSent(false); setPhoneOtp(""); setPhoneOtpError(""); }}
                        className="px-4 py-2.5 text-sm font-medium text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-500 whitespace-nowrap"
                      >
                        Edit Phone
                      </button>
                    )}
                  </div>
                  {phoneError && !isPhoneVerified && <div className="text-red-500 text-xs mt-1">{phoneError}</div>}
                  {phoneOtpSent && !isPhoneVerified && phoneOtpError && <div className="text-red-500 text-xs mt-1">{phoneOtpError}</div>}
                </div>

                {/* CR Number */}
                <div>
                  <label htmlFor="onboardingCrNumber" className="block text-sm font-medium text-gray-700 mb-1">
                    CR Number <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="onboardingCrNumber"
                    value={crNumber}
                    onChange={handleCrNumberChange}
                    className="w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 bg-white text-gray-900 placeholder-gray-400"
                    placeholder="Enter CR number"
                  />
                  {crNumberError && <div className="text-red-500 text-xs mt-1">{crNumberError}</div>}
                </div>

                {/* Is your business based in Qatar? */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Is your business based in Qatar? <span className="text-red-500">*</span>
                  </label>
                  <div className="flex items-center space-x-6">
                    <label htmlFor="qatarBasedYes" className="flex items-center cursor-pointer">
                      <input
                        type="radio"
                        id="qatarBasedYes"
                        name="isQatarBased"
                        value="yes"
                        checked={isQatarBased === true}
                        onChange={() => setIsQatarBased(true)}
                        className="h-4 w-4 text-green-600 border-gray-300 focus:ring-green-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">Yes</span>
                    </label>
                    <label htmlFor="qatarBasedNo" className="flex items-center cursor-pointer">
                      <input
                        type="radio"
                        id="qatarBasedNo"
                        name="isQatarBased"
                        value="no"
                        checked={isQatarBased === false}
                        onChange={() => setIsQatarBased(false)}
                        className="h-4 w-4 text-green-600 border-gray-300 focus:ring-green-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">No</span>
                    </label>
                  </div>
                  {qatarBasedError && <div className="text-red-500 text-xs mt-1">{qatarBasedError}</div>}
                </div>

                {submissionError && (
                  <div className="text-red-500 text-sm mt-1 text-center w-full">{submissionError}</div>
                )}

                <button
                  type="button"
                  onClick={handleSubmit}
                  disabled={isSubmitting || (!isPhoneVerified)} // Disable if not both verified
                  className={`w-full py-3 mt-2 text-base font-semibold rounded-md shadow-sm transition-colors duration-150 ${(isSubmitting || (!isEmailVerified || !isPhoneVerified))
                    ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                    : "bg-[#004141] text-white hover:bg-[#003535]"
                    }`}
                >
                  {isSubmitting ? "Submitting..." : "Complete Registration"}
                </button>
              </div>

              <div className="mt-8 text-center text-sm">
                <span className="text-gray-600">Already have an account? </span>
                <button
                  onClick={navigateToLogin}
                  className="text-green-600 font-medium hover:underline"
                >
                  Sign In
                </button>
              </div>
              <div className="w-full mt-8 text-xs sm:text-sm text-center text-gray-500 space-y-1">
                <p>Your data is 100% safe with us</p>
                <p>© Madad Fintech. All rights reserved</p>
              </div>
            </div>
          ) : (
            // Registration Success Message
            <div className="flex flex-col items-center w-full text-center">
              <img
                loading="lazy"
                src={require("../images/logo.jpg")}
                className="w-32 md:w-36 mb-6 sm:mb-8"
                alt="Madad Fintech Logo"
              />
              <div className="w-full bg-white rounded-xl">
                <div className="w-full bg-gray-200 border border-blue-200 rounded-lg p-6 flex flex-col items-center">
                  <svg
                    className="w-16 h-16 text-green-500 mb-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <h2 className="text-xl mb-4 sm:text-2xl font-bold text-[#004141]">
                    Welcome to Madad Financial Technologies!
                  </h2>
                  <p className="text-sm sm:text-base text-gray-700 px-2">
                    We have created your account on Madad platform!
                  </p>
                  <p className="text-sm sm:text-base text-gray-700 px-2">
                    You are very close to start discounting your invoices.
                  </p>
                  <button
                    onClick={() => history.push('/eligibility-checker')}
                    className="w-full max-w-xs mt-4 py-3 px-4 text-base sm:text-lg font-semibold text-white bg-[#004141] rounded-lg hover:bg-[#003535] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#004141] transition-all duration-200 shadow-md"
                  >
                    Access Your Dashboard Here
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Right Panel (Image and Text) */}
      <div className="hidden md:flex w-full md:w-1/2 bg-cover bg-center relative flex-col justify-end items-start p-8 sm:p-12 text-white order-1 md:order-2 min-h-[300px] md:min-h-screen transition-all duration-700 ease-in-out overflow-hidden"
        style={{ backgroundImage: `url(${backdrop})` }}>

        <div className="absolute inset-0 bg-black opacity-40"></div>

        {/* Animated Text Block with Slide Animation */}
        <div className="relative z-10 mb-10 w-full">
          <div
            key={index} // this triggers transition on change
            className="transform transition-all duration-500 ease-in-out"
            style={{
              transform: 'translateX(0)',
              opacity: 1,
              animation: 'slideIn 0.5s ease-in-out forwards'
            }}
          >
            <h2
              className="text-3xl lg:text-4xl font-bold mb-3 leading-tight"
              style={{
                transform: 'translateX(0)',
                opacity: 1,
                transition: 'all 0.5s ease-in-out'
              }}
            >
              {messages[index].title}
            </h2>
            <p
              className="text-base lg:text-lg"
              style={{
                transform: 'translateX(0)',
                opacity: 1,
                transition: 'all 0.5s ease-in-out 0.1s'
              }}
            >
              {messages[index].text}
            </p>
          </div>
        </div>

        {/* Animated Dots */}
        <div className="absolute bottom-10 left-8 sm:left-12 z-10 flex space-x-2">
          {messages.map((_, i) => (
            <span
              key={i}
              className={`block w-2 h-2 sm:w-2.5 sm:h-2.5 bg-white rounded-full transition-all duration-300 transform ${i === index
                ? 'opacity-100 scale-110'
                : 'opacity-50 scale-100 hover:opacity-75'
                }`}
              style={{
                transform: i === index ? 'scale(1.1)' : 'scale(1)',
                opacity: i === index ? 1 : 0.5
              }}
            />
          ))}
        </div>

        {/* Inline CSS Animation Keyframes */}
        <style dangerouslySetInnerHTML={{
          __html: `
      @keyframes slideIn {
        0% {
          transform: translateX(30px);
          opacity: 0;
        }
        100% {
          transform: translateX(0);
          opacity: 1;
        }
      }
      
      @keyframes slideOut {
        0% {
          transform: translateX(0);
          opacity: 1;
        }
        100% {
          transform: translateX(-30px);
          opacity: 0;
        }
      }
    `
        }} />
      </div>
    </div>
  );
};

export default OnboardingPage;