import React, { useEffect, useState, useRef } from "react";
import config from '../config.json';
import { useHistory, useLocation } from "react-router-dom";
import { verifyEmail } from "../api/auth";

const VerifyEmail = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const token = queryParams.get("token");
  const [isVerified, setIsVerified] = useState(false);
  const containerRef = useRef(null);
  const history = useHistory();
  // Adjust container margins to prevent overflow
  const adjustMargins = () => {
    const container = containerRef.current;
    if (container?.scrollHeight > container?.clientHeight) {
      const children = container.children;
      for (let i = 0; i < children.length; i++) {
        const child = children[i];
        const style = window.getComputedStyle(child);
        const marginTop = parseFloat(style.marginTop);
        const marginBottom = parseFloat(style.marginBottom);
        const newMarginTop = Math.max(marginTop - 2, 0);
        const newMarginBottom = Math.max(marginBottom - 2, 0);
        child.style.marginTop = `${newMarginTop}px`;
        child.style.marginBottom = `${newMarginBottom}px`;
      }
    }
  };

  // Set up resize listener for margin adjustment
  useEffect(() => {
    adjustMargins();
    window.addEventListener("resize", adjustMargins);
    return () => {
      window.removeEventListener("resize", adjustMargins);
    };
  }, [
  ]);

  useEffect(() => {
    if (token) {
      (async () => {
        try {
          const response = await verifyEmail(token);
          if (response.success) {
            setIsVerified("Email verified successfully!");
          } else {
            setIsVerified(response.message || "Email verification failed.");
          }
        } catch (error) {
          console.error("Error verifying email:", error);
          setIsVerified("An error occurred during email verification.");
        }
      }
      )();
    }
  }, [token]);

  
    const welcomeToMadadScreen = () => {
        return (
            <div className="flex flex-col items-center px-8 py-10 max-w-full bg-white rounded-md w-[500px] max-md:px-6 h-auto shadow-lg mx-4 my-20 text-center">
            <img
              loading="lazy"
              src={require("../images/logo.jpg")}
              className="w-[150px] mb-8"
              alt="Madad Fintech Logo"
            />
    
            {!isVerified ? 'Verifying...' :<div className="w-full max-w-[374px] bg-blue-50 border border-blue-200 rounded-md p-6 mb-6">
              <h2 className="text-2xl font-semibold text-[#004141] mb-4">
                Welcome to Madad Financial Technologies!
              </h2>
              
              <p className="text-gray-700 mb-4">
                We have created your account on Madad platform!
              </p>
              
              <p className="text-gray-700 mb-6">
                You are very close to start discounting your invoices
              </p>
    
              <button 
                onClick={() => history.push('/dashboard')}
                className="w-full py-2.5 text-lg font-medium text-white bg-[#004141] rounded-md transition-all duration-200"
              >
                Access Your Dashboard Here
              </button>
            </div>}
          </div>
        );
      };

    return <div className="flex justify-center items-center min-h-screen bg-cover bg-center bg-no-repeat" style={{ backgroundImage: "url('/assets/madad_background.jpg')" }}>
        {welcomeToMadadScreen()}
        </div>
};

export default VerifyEmail;
