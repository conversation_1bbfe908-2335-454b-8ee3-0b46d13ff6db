import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useHistory } from 'react-router-dom';
import {
    BriefcaseIcon,
    DocumentTextIcon,
    UsersIcon,
    MagnifyingGlassCircleIcon,
    ListBulletIcon,
    ShieldCheckIcon,
    VideoCameraIcon
} from '@heroicons/react/24/outline';
import { CheckCircleIcon } from '@heroicons/react/16/solid';

import BusinessDetails from './steps/businessDetails';
import FinancialInfo from './steps/financialInfo';
import ReviewKYC from './steps/reviewKYC';
import BusinessDocuments from './steps/businessDocuments';

import PersonalInfo from './steps/personalInfo';
import { getKycInfo, updateKyc } from '../../api/kyc';
import SharedCache from '../../sharedCache';
import FinancialDocuments from './steps/financialsNew';

function classNames(...classes) {
    return classes.filter(Boolean).join(' ');
}

// Define icons for each step.
const stepSpecificIcons = {
    'business-documents': (status) => <BriefcaseIcon className="w-5 h-5 text-gray-600" />,
    'financials': (status) => <DocumentTextIcon className="w-5 h-5 text-gray-600" />,
    'shareholders-personal-info': (status) => <MagnifyingGlassCircleIcon className="w-5 h-5 text-gray-600" />,
    'top-buyers': (status) => <UsersIcon className="w-5 h-5 text-gray-600" />,
    'review': (status) => <ListBulletIcon className="w-5 h-5 text-gray-600" />,
    'personalInfo': (status) => <ShieldCheckIcon className="w-5 h-5 text-gray-600" />,
    'ekyc': (status) => <VideoCameraIcon className="w-5 h-5 text-gray-600" />,
};

// --- HELPER FUNCTION (UNCHANGED) ---
const determineActiveSteps = (appStatus, currentRoute) => {
    const registrationSteps = [
        { id: 'business-documents', label: 'Business Documents' },
        { id: 'review', label: 'Review' },
    ];
    const creditEvalSteps = [
        { id: 'financials', label: 'Financials' },
    ];
    const creditLineSteps = [
        { id: 'shareholders-personal-info', label: 'Shareholder & Personal Info' },
        { id: 'top-buyers', label: 'Top Buyers' },
        { id: 'ekyc', label: 'eKYC' },
    ];

    if (registrationSteps.some(s => s.id === currentRoute)) return registrationSteps;
    if (creditEvalSteps.some(s => s.id === currentRoute)) return creditEvalSteps;
    if (creditLineSteps.some(s => s.id === currentRoute)) return creditLineSteps;

    if (appStatus?.registration?.status === 'Pending') return registrationSteps;
    if (appStatus?.creditEvaluation?.status === 'Pending') return creditEvalSteps;
    if (appStatus?.creditLine?.status === 'Pending') return creditLineSteps;

    return registrationSteps;
};


const KycJourney = () => {
    const { step: routeStep } = useParams();
    const history = useHistory();

    const [activeSteps, setActiveSteps] = useState([]);
    const [formData, setFormData] = useState({});
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    const [showTooltip, setShowTooltip] = useState(false);
    const [tooltipPos, setTooltipPos] = useState({ x: 0, y: 0 });

    const { currentStepId, currentStepIndex } = useMemo(() => {
        const defaultStepId = activeSteps[0]?.id || '';
        const currentId = routeStep && activeSteps.some(s => s.id === routeStep) ? routeStep : defaultStepId;
        const currentIndex = Math.max(0, activeSteps.findIndex(s => s.id === currentId));
        return { currentStepId: currentId, currentStepIndex: currentIndex };
    }, [routeStep, activeSteps]);

    useEffect(() => {
        if (!isLoading && activeSteps.length > 0 && routeStep !== currentStepId) {
            history.replace(`/kyc/${currentStepId}`);
        }
    }, [routeStep, currentStepId, history, activeSteps, isLoading]);

    // --- DATA FETCHING & STEP DETERMINATION ---
    useEffect(() => {
        const fetchKycData = async () => {
            setIsLoading(true);
            setError(null);
            try {
                const user = SharedCache.get("user");
                if (!user || (!user._id && !user.id)) {
                    throw new Error("User information not found. Please log in again.");
                }
                const userId = user._id || user.id;
                const response = await getKycInfo(userId);

                // --- THIS IS THE ONLY LINE THAT CHANGED ---
                const userData = response.success ? response.user : null;

                if (userData) {
                    setFormData(prev => ({ ...prev, ...userData, userId, firstName: prev.firstName || user.firstName, lastName: prev.lastName || user.lastName, email: prev.email || user.email }));

                    const appStatus = userData.kyc?.applicationStatus;

                    if (appStatus) {
                        const determinedSteps = determineActiveSteps(appStatus, routeStep);
                        setActiveSteps(determinedSteps);

                        if (determinedSteps.length > 0 && !determinedSteps.some(s => s.id === routeStep)) {
                           history.push('/application-status');
                        }
                    } else {
                        throw new Error("Could not retrieve your application status.");
                    }
                } else {
                    throw new Error(response.message || "Failed to retrieve your application details.");
                }
            } catch (err) {
                console.error("Error fetching KYC data:", err);
                setError(err.message || "An error occurred while fetching your data.");
                setActiveSteps(determineActiveSteps(null, routeStep));
            } finally {
                setIsLoading(false);
            }
        };
        fetchKycData();
    }, []);

    const isEkycEnabled = formData?.verificationStatus !== "INITIATED";

    const handleNext = async (stepData) => {
        setIsLoading(true);
        setError(null);
        try {
            const updatedData = { ...formData, ...stepData };
            setFormData(updatedData);
            await updateKyc(updatedData);

            const nextStepIndex = currentStepIndex + 1;
            if (nextStepIndex < activeSteps.length) {
                history.push(`/kyc/${activeSteps[nextStepIndex].id}`);
            } else {
                history.push('/application-status');
            }
        } catch (err) {
            console.error("Error during handleNext:", err);
            setError(err.message || "Failed to save your information.");
        } finally {
            setIsLoading(false);
        }
    };

    const handleBack = () => {
        const prevStepIndex = currentStepIndex - 1;
        if (prevStepIndex >= 0) {
            history.push(`/kyc/${activeSteps[prevStepIndex].id}`);
        } else {
            history.push('/application-status');
        }
    };
    
    const renderStep = () => {
        if (isLoading || (activeSteps.length === 0 && !error)) {
            return <div className="flex items-center justify-center h-64"><div className="w-12 h-12 border-4 border-green-600 border-t-transparent rounded-full animate-spin"></div></div>;
        }
        if (error && activeSteps.length === 0) {
            return null;
        }
        switch (currentStepId) {
            case 'business-documents': return <BusinessDocuments onNext={handleNext} initialData={formData} onBack={handleBack} />;
            case 'financials': return <FinancialDocuments onNext={handleNext} onBack={handleBack} initialData={formData} />;
            case 'shareholders-personal-info': return <BusinessDetails onNext={handleNext} onBack={handleBack} initialData={formData} />;
            case 'top-buyers': return <FinancialInfo onNext={handleNext} onBack={handleBack} initialData={formData} />;
            case 'review': return <ReviewKYC onNext={handleNext} onBack={handleBack} data={formData} />;
            case 'ekyc': return <PersonalInfo onNext={handleNext} onBack={handleBack} data={formData} />;
            default: return <div className="text-center p-8">Step not found or you do not have access.</div>;
        }
    };

    const getStepStatus = (index) => {
        if (index < currentStepIndex) return 'completed';
        if (index === currentStepIndex) return 'current';
        return 'pending';
    };

    return (
        <div className="min-h-screen bg-[#fafafa] flex flex-col bg-gray-100">
            <div className=" bg-[#fafafa] flex flex-1 h-[calc(100vh-var(--header-height,64px)-var(--footer-height,56px))]">
                <div className="flex flex-col lg:flex-row w-full gap-4">
                    {/* <aside
                        className="bg-white rounded-lg shadow-md pt-6 border border-gray-100 w-full lg:w-[20vw] lg:min-w-[200px] lg:max-w-[350px] self-start"
                    >
                        <div>
                            <div className='px-4'>
                                <p className="text-[12px] text-gray-500 uppercase tracking-wide mb-1">
                                    LET'S FINISH SETTING UP YOUR ACCOUNT
                                </p>
                                <h3 className="text-xl font-semibold text-gray-900 mb-3 leading-snug">
                                    You are just {Math.max(1, activeSteps.length - currentStepIndex)} {activeSteps.length - currentStepIndex === 1 ? 'step' : 'steps'} away from discounting your invoices
                                </h3>

                                <div className="flex gap-2 mb-8">
                                    {activeSteps.map((_, index) => (
                                        <div
                                            key={index}
                                            className={classNames(
                                                "h-2 flex-1 rounded-full",
                                                index <= currentStepIndex ? 'bg-green-500' : 'bg-gray-300'
                                            )}
                                        />
                                    ))}
                                </div>
                            </div>

                            <nav className="space-y-0">
                                {activeSteps.map((s, index) => {
                                    const status = getStepStatus(index);
                                    const isThisEkycStep = s.id === 'ekyc';
                                    const ekycRelatedDisabled = isThisEkycStep && !isEkycEnabled;
                                    const isCurrentStep = status === 'current';
                                    const shouldShowOnMobile = isCurrentStep;

                                    const LeftIconDisplay = () => {
                                        if (status === 'completed') {
                                            return <CheckCircleIcon className="w-6 h-6 text-green-500" />;
                                        }
                                        return <div className="w-6 h-6 border-2 border-gray-300 rounded-full"></div>;
                                    };

                                    const StepIcon = stepSpecificIcons[s.id];

                                    return (
                                        <button
                                            key={s.id}
                                            onClick={(e) => {
                                                if (ekycRelatedDisabled) {
                                                    const buttonRect = e.currentTarget.getBoundingClientRect();
                                                    setTooltipPos({ x: buttonRect.left + window.scrollX, y: buttonRect.bottom + window.scrollY + 6 });
                                                    setShowTooltip(true);
                                                    setTimeout(() => setShowTooltip(false), 3000);
                                                    return;
                                                }
                                                if (status === 'completed') history.push(`/kyc/${s.id}`);
                                            }}
                                            disabled={ekycRelatedDisabled || status !== 'completed' && status !== 'current'}
                                            className={classNames(
                                                "w-full flex items-center px-4 py-3 text-left transition-colors duration-150 ease-in-out focus:outline-none focus-visible:ring-2 focus-visible:ring-green-500",
                                                "border-b border-gray-100 last:border-b-0",
                                                status === 'current' ? 'bg-[#f5f5f5]' : 'bg-white hover:bg-gray-50',
                                                (ekycRelatedDisabled || (status !== 'completed' && status !== 'current')) ? 'cursor-not-allowed opacity-70' : 'cursor-pointer',
                                                shouldShowOnMobile ? 'block' : 'hidden lg:flex'
                                            )}
                                            aria-current={status === 'current' ? 'step' : undefined}
                                        >
                                            <div className="flex items-center flex-1">
                                                <div className="mr-4">
                                                    {StepIcon && StepIcon(status)}
                                                </div>
                                                <span className={classNames(
                                                    'text-base font-xs',
                                                    status === 'current' ? 'text-gray-900' : 'text-gray-700',
                                                    status === 'pending' ? 'text-gray-500' : ''
                                                )}>
                                                    {s.label}
                                                </span>
                                            </div>
                                            <div className="ml-4">
                                                <LeftIconDisplay />
                                            </div>
                                        </button>
                                    );
                                })}
                            </nav>

                            {showTooltip && (
                                <div className="fixed z-[100] bg-gray-900 text-white text-xs px-3 py-1.5 rounded-md shadow-lg pointer-events-none" style={{ top: tooltipPos.y, left: tooltipPos.x }}>
                                    eKYC is currently disabled for your account.
                                </div>
                            )}
                        </div>
                    </aside> */}
                    <main className="flex-1 bg-[#fafafa] overflow-y-auto">
                        {error && (
                            <div className="bg-red-50 border-l-4 border-red-500 text-red-700 mb-6 rounded-md shadow" role="alert">
                                <div className="flex p-4">
                                    <div className="py-1"><svg className="fill-current h-6 w-6 text-red-500 mr-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zM11 14v-4a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1zm0-8a1 1 0 1 0-2 0 1 1 0 0 0 2 0z" /></svg></div>
                                    <div>
                                        <p className="font-semibold">An error occurred</p>
                                        <p className="text-sm">{error}</p>
                                        <button className="mt-2 text-xs text-red-600 hover:text-red-800 font-semibold" onClick={() => setError(null)} aria-label="Dismiss error">Dismiss</button>
                                    </div>
                                </div>
                            </div>
                        )}
                        {renderStep()}
                    </main>
                </div>
            </div>
        </div>
    );
};

export default KycJourney;