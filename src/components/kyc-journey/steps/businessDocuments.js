import React, { useState, useCallback, useRef, useEffect } from 'react';
import SharedCache from '../../../sharedCache';
// Import API functions (make sure paths are correct)
import { getKycInfo, updateKyc } from '../../../api/kyc';
import axios from 'axios'; // Assuming you use axios for updateKyc
import config from "../../../config.json";
import { shortenDocumentName } from '../../../components/utils';
import { ArrowPathIcon, EyeIcon } from '@heroicons/react/24/outline';
import LoadingModal from '../../Reusable/Loading';
// --- Constants ---
const MAX_FILE_SIZE_MB = 20;
const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;
const ACCEPTED_FORMATS = [
    'application/pdf',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/csv',
    'image/jpeg',
    'image/png',
];
const ACCEPTED_FORMATS_STRING = '.pdf, .jpeg, .jpg, .png';

const UploadingPopup = ({ fileName }) => {
    return (
        <LoadingModal
            message={
                `We are uploading your file${fileName ? ` (${shortenDocumentName(fileName, 15)})` : ''} and preparing it for your review. This may take a moment—longer documents take more time to process. Thanks for your patience!`
            }
        />
    );
};


const LoadingPopup = ({ fileName }) => {
    return <LoadingModal message='Please wait a moment while we load your journey data.' />;
};

const CloseIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
    </svg>
);

const DocumentReviewModal = ({
    isOpen,
    onClose,
    docKey,
    fileName,
    signedUrl,
    extractedData,
    // verificationStatus, // You can use this if needed
    onReplace,
    onVerify, // Add an onVerify handler
    displayLabels // Pass the displayLabels map
}) => {
    if (!isOpen) return null;

    const handleReplace = () => {
        if (onReplace) {
            onReplace(docKey);
        }
    };

    const handleVerify = () => {
        if (onVerify) {
            onVerify(docKey, extractedData); // You might want to pass data to verify
        }
    };

    // Use the actual keys from the extractedData object
    const dataEntries = extractedData ? Object.entries(extractedData) : [];

    return (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[9998] p-4" onClick={onClose}>
            <div className="bg-white rounded-lg shadow-xl w-full max-w-5xl h-[80vh] flex flex-col" onClick={(e) => e.stopPropagation()}>
                {/* Header */}
                <div className="flex justify-between items-center p-4 border-b border-gray-200">
                    <h2 className="text-xl font-semibold text-gray-800">Review Business Details</h2>
                    <button onClick={onClose} className="text-gray-500 hover:text-gray-700 p-1">
                        <CloseIcon />
                    </button>
                </div>

                {/* Body */}
                <div className="flex-grow flex md:flex-row flex-col overflow-hidden">
                    {/* Left: Document Viewer */}
                    <div className="md:w-3/5 w-full p-1 bg-gray-100 md:border-r border-gray-200 overflow-auto">
                        {signedUrl ? (
                            <iframe
                                src={signedUrl}
                                title={fileName || 'Document Preview'}
                                className="w-full h-full min-h-[300px] md:min-h-[500px]"
                                frameBorder="0"
                            />
                        ) : (
                            <div className="flex items-center justify-center h-full">
                                <p className="text-gray-500">Document preview not available.</p>
                            </div>
                        )}
                    </div>

                    {/* Right: Extracted Details */}
                    <div className="md:w-2/5 w-full p-4 overflow-auto bg-gray-50">
                        <h3 className="text-base font-semibold mb-3 text-gray-700 border-b pb-2">Extracted Details</h3>
                        {extractedData && Object.keys(extractedData).length > 0 ? (
                            <div className="space-y-1 text-xs">
                                <div className="flex justify-between py-1.5 border-b border-gray-200">
                                    <span className="font-medium text-gray-500 w-2/5">File:</span>
                                    <span className="text-gray-800 text-right w-3/5 truncate" title={fileName}>
                                        {fileName ? shortenDocumentName(fileName, 15) : 'N/A'}
                                    </span>
                                </div>
                                {dataEntries.map(([key, value]) => (
                                    <div key={key} className="flex justify-between py-1.5 border-b border-gray-200">
                                        <span className="font-medium text-gray-500 w-2/5">{displayLabels[key] || key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</span>
                                        <span className="text-gray-800 text-right w-3/5 break-words">{String(value) || '-'}</span>
                                    </div>
                                ))}
                                {/* Example for verification status from design if it's part of extractedData */}
                                {/* {extractedData.commercial_reg_status && (
                                    <div className="flex justify-between py-1.5 border-b border-gray-200">
                                        <span className="font-medium text-gray-500 w-2/5">Commercial Reg. Status:</span>
                                        <span className="text-green-600 font-semibold text-right w-3/5">{extractedData.commercial_reg_status}</span>
                                    </div>
                                )} */}
                            </div>
                        ) : (
                            <p className="text-gray-500 italic mt-2 text-sm">No details extracted or data is not available.</p>
                        )}
                    </div>
                </div>

                {/* Footer */}
                <div className="p-4 border-t border-gray-200 flex justify-end space-x-3 bg-gray-100">
                    <button
                        onClick={onClose}
                        className="px-4 py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-200 transition-colors"
                    >
                        Close
                    </button>
                    <button
                        onClick={handleReplace}
                        className="px-6 py-2 text-sm text-white bg-[#214D4B] rounded-md hover:bg-[#1A3C3A] transition-colors flex items-center justify-center"
                    >
                        <ArrowPathIcon className="h-5 w-5"></ArrowPathIcon><span className="ml-2">Replace</span>
                    </button>
                    <button
                        onClick={handleVerify}
                        className="px-6 py-2 text-sm text-white bg-[#5CB85C] rounded-md hover:bg-[#4CAF50] transition-colors flex items-center justify-center ml-4"
                    >
                        <VerifyIcon /> <span className="ml-2">Verify</span>
                    </button>
                </div>
            </div>
        </div>
    );
};

const ClockIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
);

// Icon for Rejected (X Circle - Solid)
const XCircleIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
    </svg>
);

// Icon for Submitted (Info Circle - Solid)
const InformationCircleIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
    </svg>
);

// Icon for Verified (Check Circle - Solid)
const CheckCircleIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20" >
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
    </svg>
);

// --- Helper Icons ---
const UploadIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1 inline-block text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
    </svg>
);

const CheckIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1 inline-block text-green-500" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
    </svg>
);

const ReplaceIcon = () => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5 text-white"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        strokeWidth="2"
    >
        <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M4 4v6h6M20 20v-6h-6"
        />
        <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M5.5 9a7 7 0 0113 6M18.5 15a7 7 2 01-13-6"
        />
    </svg>
);

const VerifyIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
        <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
    </svg>
);

const LoadingSpinner = () => (
    <svg className="animate-spin h-5 w-5 mr-1 inline-block text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
);

const ACCEPTED_FORMATS_DISPLAY = 'PDF, JPEG, PNG, JPG';

// --- File Upload Field Component (Handles individual upload status) ---
const FileUploadField = ({ label, docKey, isRequired, fileState = {}, onFileSelect, accept, inputRef, disabled, onViewClick }) => { // Add onViewClick    // Note: console.log removed as requested to only provide necessary code
    // console.log(disabled, "adsaasdasdaad")

    const handleFileChange = (event) => {
        const file = event.target.files?.[0];
        if (!file) return;
        onFileSelect(docKey, file); // Pass file to parent for validation and upload trigger

        // Reset the actual file input value so the same file can be selected again if needed
        if (inputRef.current) {
            inputRef.current.value = '';
        }
    };

    const handleReplace = () => inputRef.current?.click();

    const handleView = () => {
        if (onViewClick) { // Prioritize the new prop
            onViewClick();
        } else if (fileState?.signedUrl) { // Fallback to original behavior if prop not passed (should not happen ideally)
            console.warn("onViewClick prop not provided to FileUploadField, using direct open for:", fileState.signedUrl);
            window.open(fileState.signedUrl, '_blank', 'noopener,noreferrer');
        } else if (fileState?.file) {
            console.warn("Viewing local file object URL. This might not work reliably after upload completes.");
            try {
                const url = URL.createObjectURL(fileState.file);
                window.open(url, '_blank', 'noopener,noreferrer');
                setTimeout(() => URL.revokeObjectURL(url), 100);
            } catch (e) {
                console.error("Error creating object URL:", e);
                alert(`Could not open preview for local file: ${fileState.fileName}`);
            }
        } else {
            alert("No file or URL available for viewing.");
        }
    };

    // --- Helper Function for Status Badge with Icons ---
    const renderVerificationStatus = (status) => {
        // Don't render status if file is not uploaded yet
        if (fileState?.status !== 'uploaded') {
            return null;
        }

        let bgColor = 'bg-blue-100';    // Default blue for Submitted
        let textColor = 'text-blue-700'; // Default blue text
        let IconComponent = InformationCircleIcon; // Default icon
        let text = 'Submitted'; // Default text

        switch (status) {
            case 'PENDING':
                bgColor = 'bg-yellow-100';
                textColor = 'text-yellow-700';
                IconComponent = ClockIcon;
                text = 'Pending';
                break;
            case 'VERIFIED':
                bgColor = 'bg-green-100';
                textColor = 'text-green-700';
                IconComponent = CheckCircleIcon; // Use specific verified icon
                text = 'Verified';
                break;
            case 'REJECTED':
                bgColor = 'bg-red-100';
                textColor = 'text-red-700';
                IconComponent = XCircleIcon;
                text = 'Rejected';
                break;
            case 'SKIPPED':
                bgColor = 'bg-gray-100'; // Neutral gray for skipped
                textColor = 'text-gray-700';
                IconComponent = InformationCircleIcon; // Info icon for skipped
                text = 'Skipped';
                break;
            case 'SUBMITTED':
            default:
                // Use defaults defined above (Blue)
                break;
        }

        return (
            <div className="z-10">
                <span className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium ${bgColor} ${textColor}`}>
                    <IconComponent className="h-4 w-4" />
                    {text}
                </span>
            </div>
        );

    };
    // --- End Helper ---


    return (
        <div className="relative border border-gray-200 rounded-lg p-3 bg-white shadow-sm flex flex-col justify-between w-full max-w-full overflow-hidden">
            <div className="flex-1">
                <div className="flex flex-wrap items-center justify-between mb-1 gap-2">
                    <label className="text-sm font-medium text-gray-700">
                        {label} {isRequired && <span className="text-red-500">*</span>}
                    </label>
                    {fileState?.status === 'uploaded' && (
                        <span className="text-sm text-green-600 font-semibold flex items-center shrink-0">
                            <CheckIcon className="h-4 w-4 mr-1" /> Uploaded
                        </span>
                    )}
                </div>

                {fileState?.status === 'uploading' ? (
                    <div className="text-sm text-blue-600 font-semibold flex flex-col items-center text-center gap-1">
                        <LoadingSpinner />
                        <span>Uploading...</span>
                        {fileState.fileName && (
                            <span className="text-xs text-gray-500 truncate max-w-full break-all">
                                {shortenDocumentName(fileState.fileName, 15)}
                            </span>
                        )}
                    </div>
                ) : fileState?.status === 'uploaded' ? (
                    <div>
                        <div className="bg-[#eff7f7] border-2 border-dashed border-gray-300 rounded-md p-3 text-center overflow-hidden">
                            <span
                                className="text-sm font-medium text-gray-700 truncate break-words block"
                                title={fileState.fileName}
                            >
                                {fileState.fileName ? shortenDocumentName(fileState.fileName, 15) : 'File uploaded'}
                            </span>
                        </div>
                        {fileState.error && (
                            <p className="mt-1 text-xs text-orange-500 text-center break-words">
                                Note: {fileState.error}
                            </p>
                        )}
                    </div>
                ) : (
                    <div>
                        <button
                            type="button"
                            onClick={() => !disabled && inputRef.current?.click()}
                            className={`bg-[#eff7f7] w-full flex flex-col items-center justify-center px-3 py-2 border-2 border-dashed rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#004141] group
              ${disabled ? 'border-gray-300 bg-gray-100 text-gray-400 cursor-not-allowed'
                                    : fileState?.error ? 'border-red-400 text-red-600 hover:bg-red-50'
                                        : 'border-gray-300 text-gray-500 hover:border-gray-400 hover:bg-gray-100'}`}
                            disabled={disabled}
                        >
                            <div className="flex items-center space-x-2">
                                <UploadIcon />
                                <span>Upload</span>
                            </div>
                            <p className="text-xs text-gray-500 text-center">Max filesize {MAX_FILE_SIZE_MB}MB</p>
                        </button>

                        <p className="mt-1 text-xs text-center text-gray-500">Formats: {ACCEPTED_FORMATS_DISPLAY}</p>
                        {fileState?.error && fileState.status === 'error' && (
                            <p className="mt-1 text-xs text-red-500 text-center break-words">{fileState.error}</p>
                        )}
                    </div>
                )}
            </div>

            {fileState?.status === 'uploaded' && (
                <div className="mt-3 flex flex-wrap items-center justify-between gap-y-2">
                    <div className="flex gap-4 flex-wrap">
                        {(fileState.signedUrl || fileState.file) && (
                            <button
                                type="button"
                                onClick={handleView}
                                className="flex items-center text-xs text-black hover:underline font-medium"
                            >
                                <EyeIcon className="h-4 w-4 mr-1" /> View
                            </button>
                        )}
                        {!disabled && (
                            <button
                                type="button"
                                onClick={handleReplace}
                                className="flex items-center text-xs text-black hover:underline font-medium"
                            >
                                <ArrowPathIcon className="h-4 w-4 mr-1" /> Replace
                            </button>
                        )}
                    </div>
                    <div className="shrink-0">{renderVerificationStatus(fileState?.verificationStatus)}</div>
                </div>
            )}

            <input
                ref={inputRef}
                type="file"
                className="hidden"
                onChange={handleFileChange}
                accept={accept}
                disabled={disabled}
            />
        </div>
    );


};

const OnboardingModal = ({ isOpen, onClose }) => {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[9999]" onClick={onClose}>
            <div className="bg-white p-8 rounded-2xl text-left w-full max-w-2xl mx-4" onClick={(e) => e.stopPropagation()}>
                {/* Header with logo and close button */}
                <div className="flex justify-between items-center mb-4">
                    <img
                        src={require("../../../images/madad_logo_noname.png")}
                        alt="Madad"
                        className="w-12 h-12 object-contain"
                    />
                    <button onClick={onClose} className="text-gray-400 hover:text-gray-600 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                {/* Title */}
                <h2 className="text-xl font-bold text-gray-900 mb-2 leading-tight">
                    Let's begin with your onboarding journey!
                </h2>

                {/* Subtitle */}
                <p className="text-sm text-gray-600 mb-4 leading-relaxed">
                    A seamless, quick and hassle-free process to get access to cash
                </p>

                {/* Get Started button */}
                <button
                    onClick={onClose}
                    className="w-auto px-6 py-2.5 bg-[#003a3b] text-white rounded-md text-sm font-medium hover:bg-teal-900 transition-colors flex items-center"
                >
                    Get Started
                    <span className="ml-2 text-lg">→</span>
                </button>
            </div>
        </div>
    );
};

// --- Main Upload Documents Component ---
const BusinessDocuments = ({ onSubmitSuccess, onNext, onBack }) => {

    const otherDocSchemaKeys = [
        'otherDocument', 'otherDocumentTwo', 'otherDocument3', 'otherDocument4', 'otherDocument5',
        'otherDocument6', 'otherDocument7', 'otherDocument8', 'otherDocument9', 'otherDocument10'
    ];
    const [showOnboardingModal, setShowOnboardingModal] = useState(false);
    const initialOtherDocsState = {};
    otherDocSchemaKeys.forEach(key => {
        initialOtherDocsState[key] = { file: null, fileName: '', status: 'empty', error: '', extractedData: null, signedUrl: null, verificationStatus: 'SUBMITTED' };
    });
    const [isUploadingGlobal, setIsUploadingGlobal] = useState(false);
    const [currentUploadingFileLabel, setCurrentUploadingFileLabel] = useState('');
    const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
    const [modalData, setModalData] = useState({
        docKey: null,
        fileName: '',
        signedUrl: null,
        extractedData: null,
        verificationStatus: 'SUBMITTED',
    });
    const [files, setFiles] = useState({
        commercialRegistration: { file: null, fileName: '', status: 'empty', error: '', extractedData: null, signedUrl: null },
        tradeLicense: { file: null, fileName: '', status: 'empty', error: '', extractedData: null, signedUrl: null },
        taxCard: { file: null, fileName: '', status: 'empty', error: '', extractedData: null, signedUrl: null },
        establishmentCard: { file: null, fileName: '', status: 'empty', error: '', extractedData: null, signedUrl: null },
        memorandumOfAssociation: { file: null, fileName: '', status: 'empty', error: '', extractedData: null, signedUrl: null },
        articleOfAssociation: { file: null, fileName: '', status: 'empty', error: '', extractedData: null, signedUrl: null },
        commercialCreditReport: { file: null, fileName: '', status: 'empty', error: '', extractedData: null, signedUrl: null },
        ...initialOtherDocsState // Spread the 10 other document states
    });
    const [isLoading, setIsLoading] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false); // For the final "Save and Continue" button
    const [submitError, setSubmitError] = useState('');
    const [formErrors, setFormErrors] = useState({});
    const inputRefs = useRef({});
    const [kycStatus, setKycStatus] = useState(null);
    Object.keys(files).forEach(key => {
        inputRefs.current[key] = inputRefs.current[key] ?? React.createRef();
    });

    const setFileState = useCallback((key, state) => {
        setFiles(prev => ({ ...prev, [key]: { ...(prev[key] || {}), ...state } })); // Merge state carefully
        // Clear specific form error when state changes positively
        if (formErrors[key] && (state.status === 'uploaded' || state.status === 'uploading')) {
            setFormErrors(prev => {
                const newErrors = { ...prev };
                delete newErrors[key];
                return newErrors;
            });
        }
        // Clear general submission error if user interacts successfully
        if (state.status === 'uploaded') {
            setSubmitError('');
        }
    }, [formErrors]); // Include formErrors dependency

    useEffect(() => {
        const hasShownOnboardingModal = sessionStorage.getItem('hasShownOnboardingModal');
        if (!hasShownOnboardingModal) {
            setShowOnboardingModal(true);
            sessionStorage.setItem('hasShownOnboardingModal', 'true');
        }
    }, []);

    // --- Load existing data ---
    useEffect(() => {
        let isMounted = true;
        const fetchUserData = async () => {
            const userId = SharedCache.get("user")?._id || SharedCache.get("user")?.id;
            if (!userId) {
                console.warn('User ID not found in SharedCache for fetching documents.');
                return;
            }
            try {
                setIsLoading(true);
                const apiResponse = await getKycInfo(userId);
                if (!isMounted) return;

                if (apiResponse && apiResponse.user) {
                    const userDocuments = apiResponse.user;
                    const updates = {};
                    Object.keys(files).forEach(key => {
                        const docData = userDocuments[key];
                        if (docData && typeof docData === 'object' && docData.filePath) {
                            const actualFileName = docData.filePath.substring(docData.filePath.lastIndexOf('/') + 1);
                            updates[key] = {
                                file: null,
                                fileName: actualFileName,
                                status: 'uploaded', // Assume uploaded if path exists
                                error: '',
                                signedUrl: docData.signedUrl || null, // Use stored signed URL
                                extractedData: docData.extractedFields || null, // *** Use real extracted data if available ***
                                verificationStatus: docData.verificationStatus || 'SUBMITTED' // Default if missing
                            };
                        } else {
                            // Default empty state if not found in API response
                            updates[key] = { file: null, fileName: '', status: 'empty', error: '', extractedData: null, signedUrl: null };
                        }
                    });
                    setFiles(updates);
                    setKycStatus(apiResponse.user.kyc.verificationStatus); // Add this line
                } else {
                    console.warn("User data or user object not found in API response during fetch.");
                    // Optionally reset all fields to empty if needed
                }
            } catch (error) {
                if (!isMounted) return;
                console.error('Error fetching user documents:', error);
                setSubmitError('Failed to load existing document information.');
            } finally {
                setIsLoading(false);
            }
        };
        fetchUserData();
        return () => { isMounted = false; };
        // Don't depend on setFileState here as it causes re-renders, fetch only once on mount
    }, []); // Empty dependency array ensures this runs only once on mount


    // --- Function to call updateKyc API ---
    const callUpdateKyc = async (userId, docKey, extractedData) => {
        if (!extractedData || Object.keys(extractedData).length === 0) {
            console.log(`No extracted data for ${docKey}, skipping updateKyc call.`);
            return;
        }

        const kycUpdatePayload = { id: userId, kyc: { businessDetails: {} } };
        let hasBusinessDetails = false;

        // --- Map fields based on docKey ---
        if (docKey === 'commercialRegistration') {
            if (extractedData.commercial_reg_number !== undefined) { kycUpdatePayload.kyc.businessDetails.crNumber = extractedData.commercial_reg_number; hasBusinessDetails = true; }
            if (extractedData.tax_reg_number !== undefined) { kycUpdatePayload.kyc.businessDetails.taxRegNo = extractedData.tax_reg_number; hasBusinessDetails = true; }
            if (extractedData.trade_name !== undefined) { kycUpdatePayload.kyc.businessDetails.businessName = extractedData.trade_name; hasBusinessDetails = true; } // Or legalEntityName
            if (extractedData.issue_date !== undefined) { kycUpdatePayload.kyc.businessDetails.crIssueDate = extractedData.issue_date; hasBusinessDetails = true; }
            if (extractedData.creation_date !== undefined) { kycUpdatePayload.kyc.businessDetails.crIssueDate = extractedData.creation_date; hasBusinessDetails = true; } // Mapping creation to issue date? Verify
            if (extractedData.expiry_date !== undefined) { kycUpdatePayload.kyc.businessDetails.crExpiryDate = extractedData.expiry_date; hasBusinessDetails = true; }
            // commercial_reg_status needs backend field if desired
            // mail_id needs backend field if desired
            if (extractedData.address !== undefined) { kycUpdatePayload.kyc.businessDetails.businessAddressLine1 = extractedData.address; hasBusinessDetails = true; } // Basic mapping
        } else if (docKey === 'tradeLicense') {
            // Use the keys you specified for Trade License extraction
            if (extractedData.trade_license_number !== undefined) { kycUpdatePayload.licenseNumber = extractedData.trade_license_number; hasBusinessDetails = true; } // Map to root licenseNumber?
            if (extractedData.trade_license_expiry_date !== undefined) { kycUpdatePayload.kyc.businessDetails.tlExpiryDate = extractedData.trade_license_expiry_date; hasBusinessDetails = true; }
            if (extractedData.trade_license_issue_date !== undefined) { kycUpdatePayload.kyc.businessDetails.tlIssueDate = extractedData.trade_license_issue_date; hasBusinessDetails = true; }
            // Also potentially map trade_name here if it's different from CR?
            // if (extractedData.trade_name !== undefined && !kycUpdatePayload.kyc.businessDetails.businessName) {
            //     kycUpdatePayload.kyc.businessDetails.businessName = extractedData.trade_name;
            //     hasBusinessDetails = true;
            // }
        } else if (docKey === 'taxCard') {
            // ** Placeholder Mapping - Adjust based on your Tax Card extracted fields and backend schema **
            console.log("Mapping Tax Card Data (Placeholder):", extractedData);
            if (extractedData.tin_number !== undefined) { kycUpdatePayload.kyc.businessDetails.taxRegNo = extractedData.tin_number; hasBusinessDetails = true; } // Assuming map to taxRegNo
            if (extractedData.trade_name !== undefined) { kycUpdatePayload.kyc.businessDetails.legalEntityName = extractedData.trade_name; hasBusinessDetails = true; hasBusinessDetails = true; }
            // Map other relevant fields like TIN number if extracted
            // if (extractedData.tin_number !== undefined) { kycUpdatePayload.kyc.businessDetails.tinNumber = extractedData.tin_number; hasBusinessDetails = true; }
        } else if (docKey === 'establishmentCard') {
            console.log("Mapping Establishment Card Data:", extractedData); // Updated log

            // Use the correct keys from extractedData object
            if (extractedData.establishment_id !== undefined) {
                kycUpdatePayload.kyc.businessDetails.establishmentId = extractedData.establishment_id;
                hasBusinessDetails = true;
            }
            if (extractedData.establishment_name !== undefined) {
                // Correct target key VVVVVVVVVVVVVVVVVVV uses camelCase
                kycUpdatePayload.kyc.businessDetails.establishmentName = extractedData.establishment_name;
                hasBusinessDetails = true;
            }
            if (extractedData.issue_date !== undefined) { // <-- Use correct key from extractedData
                kycUpdatePayload.kyc.businessDetails.establishmentIdIssueDate = extractedData.issue_date; // Map to correct backend field
                hasBusinessDetails = true;
            }
            if (extractedData.expiry_date !== undefined) { // <-- Use correct key from extractedData
                kycUpdatePayload.kyc.businessDetails.establishmentIdExpiryDate = extractedData.expiry_date; // Map to correct backend field
                hasBusinessDetails = true;
            }
            if (extractedData.sector !== undefined) {
                kycUpdatePayload.kyc.businessDetails.sector = extractedData.sector;
                hasBusinessDetails = true;
            }
        }
        // --- End Mapping ---


        if (!hasBusinessDetails) {
            console.log(`Extracted data for ${docKey} did not contain fields relevant for KYC update.`);
            return; // Don't call update if nothing relevant was mapped
        }

        console.log(`Calling updateKyc for user ${userId} with data from ${docKey}:`, JSON.stringify(kycUpdatePayload, null, 2));
        try {
            const updateResponse = await updateKyc(kycUpdatePayload);
            if (updateResponse.success) { console.log(`KYC update success for ${docKey}.`); }
            else { console.error(`KYC update failed for ${docKey}:`, updateResponse.message); setFileState(docKey, { error: `KYC Update Failed: ${updateResponse.message}` }); }
        } catch (error) { console.error(`Error calling updateKyc API for ${docKey}:`, error); setFileState(docKey, { error: `KYC Update Error.` }); }
    };

    // --- Function to handle upload and extraction for a single file ---
    const handleUploadAndExtract = async (docKey, file) => {
        const userId = SharedCache.get("user")?._id || SharedCache.get("user")?.id;
        if (!userId) {
            console.error('User ID not found for upload.');
            setFileState(docKey, { status: 'error', error: 'User ID not found.', file: null, fileName: file.name });
            return;
        }

        // Show global uploading popup
        setCurrentUploadingFileLabel(fieldConfigurations[docKey]?.label || 'document');
        setIsUploadingGlobal(true);

        setFileState(docKey, { status: 'uploading', file: file, fileName: file.name, error: '', extractedData: null, signedUrl: files[docKey]?.signedUrl });

        const formData = new FormData();
        formData.append('file', file);
        formData.append('userId', userId);
        formData.append('documentType', docKey);

        const uploadUrl = `${config.apiUrl}/ops/invoiceFinancing/uploadKycDocument`;
        const token = SharedCache.get("token") || "placeholdertoken";

        try {
            const response = await axios.post(uploadUrl, formData, {
                headers: { 'x-auth-token': token }
            });

            const responseData = response.data;
            const newSignedUrl = responseData?.signedUrl || null;
            const newFilePath = responseData?.filePath || '';
            const newFileName = newFilePath ? newFilePath.substring(newFilePath.lastIndexOf('/') + 1) : file.name;
            const realExtractedData = responseData?.extractedFields || null;
            let verificationStatus = responseData?.verificationStatus || files[docKey]?.verificationStatus;
            if (!verificationStatus) {
                verificationStatus = docKey === 'commercialRegistration' ? 'PENDING' : 'SUBMITTED';
            }

            setFileState(docKey, {
                status: 'uploaded',
                file: null, // Clear the local file object after successful upload
                fileName: newFileName,
                signedUrl: newSignedUrl,
                extractedData: realExtractedData,
                error: '',
                verificationStatus: verificationStatus, // Store verification status
                // Potentially store filePath from responseData.filePath if needed later
            });

            // Open the modal after successful upload
            setModalData({
                docKey: docKey,
                fileName: newFileName,
                signedUrl: newSignedUrl,
                extractedData: realExtractedData,
                verificationStatus: verificationStatus
            });
            setIsDetailsModalOpen(true);

            if (realExtractedData) {
                await callUpdateKyc(userId, docKey, realExtractedData);
            }

        } catch (axiosError) {
            console.error(`--- Direct Axios: Error uploading/extracting ${docKey}:`, axiosError);
            const errorMessage = axiosError.response?.data?.error || axiosError.response?.data?.message || axiosError.message || 'Upload failed. Please try again.';
            setFileState(docKey, {
                status: 'error',
                file: null, // Clear local file on error too
                fileName: file.name, // Keep original file name for context
                error: errorMessage,
                extractedData: null,
                signedUrl: files[docKey]?.signedUrl // Keep old signed URL if it existed
            });
            // Optionally open modal with error, or just rely on FileUploadField's error display
            // setModalData({
            //     docKey: docKey,
            //     fileName: file.name,
            //     signedUrl: files[docKey]?.signedUrl,
            //     extractedData: { error: errorMessage }, // Special handling for error in modal
            //     verificationStatus: files[docKey]?.verificationStatus
            // });
            // setIsDetailsModalOpen(true);
        } finally {
            // Hide global uploading popup
            setIsUploadingGlobal(false);
            setCurrentUploadingFileLabel('');
        }
    };

    const openModalForView = (docKeyToView) => {
        const fileToView = files[docKeyToView];
        if (fileToView && fileToView.status === 'uploaded') {
            setModalData({
                docKey: docKeyToView,
                fileName: fileToView.fileName,
                signedUrl: fileToView.signedUrl,
                extractedData: fileToView.extractedData,
                verificationStatus: fileToView.verificationStatus
            });
            setIsDetailsModalOpen(true);
        } else {
            // Fallback for consistency, though FileUploadField's View button is disabled if no signedUrl
            alert("Document not ready for viewing or no viewable URL.");
        }
    };

    // Handler for "Verify" button in the modal
    const handleVerifyDocumentInModal = (docKeyToVerify, extractedDetails) => {
        console.log("Verifying document:", docKeyToVerify, "with details:", extractedDetails);

        // If the document is the Commercial Registration, set its status to 'PENDING'.
        // For all other documents, set the status to 'SUBMITTED'.
        const newStatus = docKeyToVerify === 'commercialRegistration' ? 'PENDING' : 'SUBMITTED';

        setFileState(docKeyToVerify, { verificationStatus: newStatus });
        setIsDetailsModalOpen(false); // Close modal after action
    };
    // Keep this for the modal, or define it in BusinessDocuments and pass as prop
    const displayLabels = {
        'commercial_reg_number': 'Commercial Reg. No.',
        'tax_reg_number': 'Tax Reg. No.',
        'trade_name': 'Trade Name',
        'issue_date': 'Issue Date',
        'creation_date': 'Creation Date',
        'expiry_date': 'Expiry Date',
        'commercial_reg_status': 'Commercial Reg. Status',
        'mail_id': 'Mail ID',
        'address': 'Address',
        // From Trade License
        'trade_license_number': 'Trade License No.',
        'trade_license_issue_date': 'TL Issue Date',
        'trade_license_expiry_date': 'TL Expiry Date',
        // From Tax Card
        'tin_number': 'TIN Number',
        // From Establishment Card
        'establishment_id': 'Establishment ID',
        'establishment_name': 'Establishment Name',
        // 'issue_date' will conflict if not namespaced, use specific like 'establishment_issue_date' if different
        // 'expiry_date' will conflict, use specific like 'establishment_expiry_date'
        'sector': 'Sector',
        // Add other mappings as needed
    };

    // --- Called when a file is selected in FileUploadField ---
    const handleFileSelect = (docKey, file) => {
        // Basic Validation (can be expanded)
        let error = '';
        if (file.size > MAX_FILE_SIZE_BYTES) {
            error = `Ensure format ${ACCEPTED_FORMATS_DISPLAY} of size not more than ${MAX_FILE_SIZE_MB}MB.`;
        } else {
            const isMimeTypeAllowed = ACCEPTED_FORMATS.includes(file.type);
            // Basic extension check as fallback
            const fileExt = file.name.split('.').pop()?.toLowerCase();
            const allowedExts = ACCEPTED_FORMATS_STRING.split(',').map(e => e.trim().substring(1));
            const isExtensionAllowed = allowedExts.includes(fileExt);

            if (!isMimeTypeAllowed && !isExtensionAllowed) {
                error = `Invalid file format. Accepted: ${ACCEPTED_FORMATS_STRING.toUpperCase()}`;
            }
            // Add warnings for mismatch if needed (like in original FileUploadField)
        }

        if (error) {
            setFileState(docKey, { status: 'error', error: error, file: null, fileName: file.name, extractedData: null });
        } else {
            // Validation passed, trigger the upload and extraction process
            handleUploadAndExtract(docKey, file);
        }
    };


    // Function to trigger file input click for replacement
    const triggerFileInput = (key) => inputRefs.current[key]?.current?.click();

    // Handles the "View" button click in the DocumentPreview section
    const handlePreviewView = (key) => {
        const fileState = files[key];
        if (!fileState || !fileState.signedUrl) {
            alert("Signed URL not available for viewing.");
            return;
        };
        console.log("Opening signed URL from preview:", fileState.signedUrl);
        window.open(fileState.signedUrl, '_blank', 'noopener,noreferrer');
    };
    //comment for pysh
    // Form validation logic - checks if required fields are 'uploaded'
    const validateForm = () => {
        const errors = {};
        const requiredFields = [
            // 'commercialRegistration', 'tradeLicense', 'taxCard',
            // 'establishmentCard',
        ];
        let isValid = true;

        requiredFields.forEach(key => {
            const fileState = files[key];
            // Field is invalid if required AND not 'uploaded' OR if it's in 'error' state
            if (!fileState || (fileState.status !== 'uploaded' && fileState.status !== 'uploading')) { // Allow submitting if uploading
                if (fileState?.status !== 'uploading') { // Only mark error if not currently uploading
                    errors[key] = 'This document is required.';
                    setFileState(key, { status: 'error', error: 'This document is required.' });
                    isValid = false;
                }
            } else if (fileState.status === 'error' && fileState.error !== 'Upload failed. Please try again.' && !fileState.error?.includes('auto-update')) {
                // If status is error for reasons other than just a failed upload attempt or auto-update failure (like size/type)
                errors[key] = fileState.error;
                isValid = false;
            }
        });

        setFormErrors(errors); // Update form errors state
        return isValid;
    };

    // --- Helper function to safely get nested values (if not already defined) ---
    const getNestedValue = (obj, pathArray) => {
        return pathArray.reduce((current, key) => (current && current[key] !== undefined ? current[key] : undefined), obj);
    };

    // --- Helper function for date conversion ---
    const parseExtractedDate = (dateString) => {
        if (!dateString || typeof dateString !== 'string') return undefined; // Return undefined if invalid or not string
        // Try YYYY/MM/DD first
        const partsYMD = dateString.match(/^(\d{4})\/(\d{1,2})\/(\d{1,2})$/);
        if (partsYMD) {
            const year = partsYMD[1];
            const month = partsYMD[2].padStart(2, '0');
            const day = partsYMD[3].padStart(2, '0');
            const isoDateString = `${year}-${month}-${day}`;
            // Check if the constructed date is valid
            if (!isNaN(new Date(isoDateString).getTime())) {
                return isoDateString; // Return YYYY-MM-DD
            }
        }
        // Try direct parsing for other formats (like YYYY-MM-DD already)
        if (!isNaN(new Date(dateString).getTime())) {
            // If it's already a valid date string (e.g., ISO), return it
            // We might want to format it consistently, e.g., return new Date(dateString).toISOString().split('T')[0];
            return new Date(dateString).toISOString().split('T')[0]; // Return YYYY-MM-DD
        }
        console.warn("Could not parse date:", dateString);
        return undefined; // Return undefined if parsing fails
    };


    const handleFinalSubmit = async (e) => {
        e.preventDefault();
        setSubmitError('');
        setFormErrors({}); // Clear previous form errors

        // --- Validation Checks ---
        const stillUploading = Object.values(files).some(state => state.status === 'uploading');
        if (stillUploading) {
            setSubmitError('Please wait for all uploads to complete.');
            return;
        }
        if (!validateForm()) { // Assumes validateForm checks required uploads
            setSubmitError('Please ensure all required documents are uploaded before submitting.');
            return;
        }

        setIsSubmitting(true);
        console.log("Validation passed. Preparing data for final submission...");

        // Get user ID and potentially *existing* user data from cache
        const cachedUser = SharedCache.get('user') || {};
        const userId = cachedUser._id || cachedUser.id || "";

        if (!userId) {
            setSubmitError("User session invalid or ID missing. Please log in again.");
            setIsSubmitting(false);
            return;
        }

        // --- Construct Payload for updateKyc ---
        const payload = { id: userId };
        payload.kyc = { businessDetails: {} }; // Initialize necessary nested objects
        let businessDetailsHasData = false;

        // --- Helper to get the best available extracted data ---
        // Prioritizes current session's 'files' state, falls back to 'cachedUser' data
        const getBestExtractedData = (docKey) => {
            const currentExtracted = files?.[docKey]?.extractedFields;
            // Check if current extracted data exists and is not empty
            if (currentExtracted && typeof currentExtracted === 'object' && Object.keys(currentExtracted).length > 0) {
                console.log(`Using current session extracted data for ${docKey}`);
                return currentExtracted;
            }
            // Fallback to cached data if current is missing/empty
            const cachedExtracted = cachedUser?.[docKey]?.extractedFields;
            if (cachedExtracted && typeof cachedExtracted === 'object' && Object.keys(cachedExtracted).length > 0) {
                console.log(`Using cached extracted data for ${docKey}`);
                return cachedExtracted;
            }
            return null; // No usable extracted data found
        };

        // --- Populate Root Document Data in Payload ---
        // Include filePath, signedUrl, extractedFields (using best available), statuses etc.
        const rootDocKeys = [
            'commercialRegistration', 'tradeLicense', 'taxCard', 'establishmentCard',
            'memorandumOfAssociation', 'articleOfAssociation', 'commercialCreditReport',
            'auditedFinancialReport', 'otherDocument', 'otherDocumentTwo', 'otherDocument3', 'otherDocument4', 'otherDocument5',
            'otherDocument6', 'otherDocument7', 'otherDocument8', 'otherDocument9', 'otherDocument10',
            'bankStatement', 'cashFlowLedger' // Add other relevant docs
        ];

        rootDocKeys.forEach(docKey => {
            const currentFileState = files[docKey];
            const cachedDocState = cachedUser[docKey];

            // Include document in payload only if it has been uploaded (has a filePath)
            const filePathToUse = currentFileState?.filePath || cachedDocState?.filePath;
            if (filePathToUse) {
                const extractedDataToUse = getBestExtractedData(docKey); // Get best extracted data
                const extractionStatusToUse = currentFileState?.extractionStatus || // Current status first
                    (extractedDataToUse ? 'SUCCESS' : // SUCCESS if we have data
                        cachedDocState?.extractionStatus || // Cached status next
                        'NOT_ATTEMPTED'); // Default

                payload[docKey] = {
                    filePath: filePathToUse,
                    signedUrl: currentFileState?.signedUrl || cachedDocState?.signedUrl, // Prioritize current URL
                    extractedFields: extractedDataToUse,
                    extractionStatus: extractionStatusToUse,
                    verificationStatus: currentFileState?.verificationStatus || cachedDocState?.verificationStatus || 'SUBMITTED',
                    mimeType: currentFileState?.mimeType || cachedDocState?.mimeType,
                    uploadedOn: currentFileState?.uploadedOn || cachedDocState?.uploadedOn,
                    // Add other fields from DocumentSchema as needed, prioritizing current state
                };
            }
        });

        // --- Map specific extracted fields to relevant schema locations ---

        // Commercial Registration Mapping (uses best available extracted data)
        const crExtracted = getBestExtractedData('commercialRegistration');
        if (crExtracted) {
            if (crExtracted.commercial_reg_number) { payload.kyc.businessDetails.crNumber = crExtracted.commercial_reg_number; businessDetailsHasData = true; }
            if (crExtracted.trade_name) {
                payload.kyc.businessDetails.businessName = crExtracted.trade_name;
                payload.kyc.businessDetails.legalEntityName = crExtracted.trade_name; // Map to both? Adjust if needed
                businessDetailsHasData = true;
            }
            const crIssue = parseExtractedDate(crExtracted.issue_date || crExtracted.creation_date);
            if (crIssue !== undefined) { payload.kyc.businessDetails.crIssueDate = crIssue; businessDetailsHasData = true; } // Check undefined, allow null
            const crExpiry = parseExtractedDate(crExtracted.expiry_date);
            if (crExpiry !== undefined) { payload.kyc.businessDetails.crExpiryDate = crExpiry; businessDetailsHasData = true; }
            if (crExtracted.tax_reg_number) { payload.kyc.businessDetails.taxRegNo = crExtracted.tax_reg_number; businessDetailsHasData = true; }
        }

        // Trade License Mapping
        const tlExtracted = getBestExtractedData('tradeLicense');
        if (tlExtracted) {
            if (tlExtracted.trade_license_number) { payload.licenseNumber = tlExtracted.trade_license_number; } // Root field
            const tlIssue = parseExtractedDate(tlExtracted.trade_license_issue_date);
            if (tlIssue !== undefined) { payload.kyc.businessDetails.tlIssueDate = tlIssue; businessDetailsHasData = true; }
            const tlExpiry = parseExtractedDate(tlExtracted.trade_license_expiry_date);
            if (tlExpiry !== undefined) { payload.kyc.businessDetails.tlExpiryDate = tlExpiry; businessDetailsHasData = true; }
        }

        // Tax Card Mapping
        const tcExtracted = getBestExtractedData('taxCard');
        if (tcExtracted) {
            if (tcExtracted.tin_number) {
                payload.kyc.businessDetails.tinNumber = tcExtracted.tin_number; // Map to specific TIN field
                businessDetailsHasData = true;
                // Decide if TIN should *also* populate taxRegNo if taxRegNo is empty
                if (!payload.kyc.businessDetails.taxRegNo) {
                    payload.kyc.businessDetails.taxRegNo = tcExtracted.tin_number;
                }
            }
            // Map trade name only if not already populated by CR
            if (tcExtracted.trade_name && tcExtracted.trade_name !== '-' && !payload.kyc.businessDetails.businessName) {
                payload.kyc.businessDetails.businessName = tcExtracted.trade_name;
                if (!payload.kyc.businessDetails.legalEntityName) { // Also set legalEntityName if empty
                    payload.kyc.businessDetails.legalEntityName = tcExtracted.trade_name;
                }
                businessDetailsHasData = true;
            }
        }

        // Establishment Card Mapping
        const ecExtracted = getBestExtractedData('establishmentCard');
        if (ecExtracted) {
            if (ecExtracted.establishment_id) { payload.kyc.businessDetails.establishmentId = ecExtracted.establishment_id; businessDetailsHasData = true; }
            if (ecExtracted.establishment_name) { payload.kyc.businessDetails.establishmentName = ecExtracted.establishment_name; businessDetailsHasData = true; }
            const ecIssue = parseExtractedDate(ecExtracted.issue_date);
            if (ecIssue !== undefined) { payload.kyc.businessDetails.establishmentIdIssueDate = ecIssue; businessDetailsHasData = true; }
            const ecExpiry = parseExtractedDate(ecExtracted.expiry_date);
            if (ecExpiry !== undefined) { payload.kyc.businessDetails.establishmentIdExpiryDate = ecExpiry; businessDetailsHasData = true; }
            if (ecExtracted.sector) { payload.kyc.businessDetails.sector = ecExtracted.sector; businessDetailsHasData = true; }
        }
        console.log("EXTRACTED DETAILS EHREE", crExtracted, tcExtracted, tlExtracted, ecExtracted);
        // --- Clean up empty objects ---
        if (!businessDetailsHasData) {
            delete payload.kyc.businessDetails;
        }
        // Only delete kyc if it's truly empty (no businessDetails and no other direct kyc fields were added)
        if (Object.keys(payload.kyc).length === 0) {
            delete payload.kyc;
        }

        console.log("Constructed Payload for API (using merged extracted data):", JSON.stringify(payload, null, 2));

        // Check if there's actually anything to update besides the ID
        if (Object.keys(payload).length <= 1) { // Only 'id' present
            console.log("No data to update after processing extracted fields.");
            setIsSubmitting(false);
            // Still proceed to next step as validation passed, even if no API call needed
            if (onNext) onNext(cachedUser || {}); // Pass cached user data
            if (onSubmitSuccess) onSubmitSuccess(); // Indicate success
            return;
        }

        // --- Call API ---
        try {
            const result = await updateKyc(payload); // Use the constructed payload
            if (result.success) {
                console.log("KYC data including extracted fields updated successfully!");
                setSubmitError('');
                const updatedUserData = result.user || {};
                SharedCache.set("user", updatedUserData); // Update cache with latest
                if (onNext) onNext(updatedUserData); // Navigate with latest data
                if (onSubmitSuccess) onSubmitSuccess();
            } else {
                setSubmitError(result.message || 'Failed to submit extracted details. Please try again.');
                setIsSubmitting(false); // Re-enable button on failure
            }
        } catch (error) {
            console.error("Error calling updateKyc API:", error);
            setSubmitError(error.message || 'An unexpected network error occurred.');
            setIsSubmitting(false); // Re-enable button on failure
        }
    }; // End of handleFinalSubmit

    // Configuration for field labels and requirements
    const fieldConfigurations = {
        commercialRegistration: { label: "Commercial Registration", isRequired: false },
        tradeLicense: { label: "Trade License", isRequired: false },
        taxCard: { label: "Tax Card", isRequired: false },
        establishmentCard: { label: "Establishment Card", isRequired: false },
        // memorandumOfAssociation: { label: "Memorandum of Association", isRequired: false },
        articleOfAssociation: { label: "Article of Association", isRequired: false },
        // commercialCreditReport: { label: "Commercial Credit Report", isRequired: false },
        otherDocument: { label: "Business Address (Optional)", isRequired: false },
        otherDocumentTwo: { label: "Other Document 2", isRequired: false },
        otherDocument3: { label: "Other Document 3", isRequired: false },
        otherDocument4: { label: "Other Document 4", isRequired: false },
        otherDocument5: { label: "Other Document 5", isRequired: false },
        otherDocument6: { label: "Other Document 6", isRequired: false },
        otherDocument7: { label: "Other Document 7", isRequired: false },
        otherDocument8: { label: "Other Document 8", isRequired: false },
        otherDocument9: { label: "Other Document 9", isRequired: false },
        otherDocument10: { label: "Other Document 10", isRequired: false },
    };

    // Filter and sort documents for the preview section
    const documentsToPreview = Object.entries(files)
        .filter(([key, state]) => state.status === 'uploaded' && state.extractedData) // Only show previews with extracted data
        .sort(([keyA], [keyB]) => {
            const order = ['commercialRegistration', 'tradeLicense', 'taxCard', 'establishmentCard', 'memorandumOfAssociation', 'articleOfAssociation', 'commercialCreditReport'];
            const indexA = order.indexOf(keyA);
            const indexB = order.indexOf(keyB);
            if (indexA === -1 && indexB === -1) return 0;
            if (indexA === -1) return 1;
            if (indexB === -1) return -1;
            return indexA - indexB;
        });

    const orderedOtherDocKeys = [
        'otherDocument', 'otherDocumentTwo', 'otherDocument3', 'otherDocument4', 'otherDocument5',
        'otherDocument6', 'otherDocument7', 'otherDocument8', 'otherDocument9', 'otherDocument10'
    ];

    const primaryDocKeys = Object.keys(fieldConfigurations).filter(
        key => !orderedOtherDocKeys.includes(key)
    );

    if (isLoading) {
        return (
            <LoadingPopup />
        );
    }

    return (
        <div className="flex w-full min-h-screen bg-gray-50">
            {/* === LEFT SIDE (8/12) - UPDATED UI === */}
            <div className="w-full lg:w-8/12 p-4 md:p-8">
                {/* All modals now live at the top level of the component */}
                {showOnboardingModal && (
                    <OnboardingModal isOpen={showOnboardingModal} onClose={() => setShowOnboardingModal(false)} />
                )}

                {/* The UploadingPopup component is removed from rendering */}

                {isDetailsModalOpen && (
                    <DocumentReviewModal
                        isOpen={isDetailsModalOpen}
                        onClose={() => setIsDetailsModalOpen(false)}
                        docKey={modalData.docKey}
                        fileName={modalData.fileName}
                        signedUrl={modalData.signedUrl}
                        extractedData={modalData.extractedData}
                        verificationStatus={modalData.verificationStatus}
                        onReplace={(docKeyToReplace) => {
                            setIsDetailsModalOpen(false);
                            triggerFileInput(docKeyToReplace);
                        }}
                        onVerify={handleVerifyDocumentInModal}
                        displayLabels={displayLabels}
                    />
                )}

                {/* Centered Main Header */}
                <div className="text-center mb-8">
                    <h1 className="text-3xl font-bold text-gray-800">
                        Upload your Business Documents
                    </h1>
                    <p className="mt-2 text-md text-gray-600 max-w-2xl mx-auto">
                        Please upload the relevant documents required to process your onboarding. Ensure all documents are clear, legible, and contain accurate information.
                    </p>
                </div>

                {/* Primary Documents Section with Gray Background */}
                <div className="bg-[#f0f0f0] p-4 sm:p-6 rounded-lg mb-10">
                    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-5">
                        {primaryDocKeys.map((key) => {
                            const config = fieldConfigurations[key] || { label: "Document", isRequired: false };
                            if (!files[key]) {
                                console.warn(`File state for primary document key "${key}" is missing.`);
                                return null;
                            }
                            return (
                                <FileUploadField
                                    key={key}
                                    label={config.label}
                                    docKey={key}
                                    isRequired={config.isRequired}
                                    fileState={files[key]}
                                    onFileSelect={handleFileSelect}
                                    accept={ACCEPTED_FORMATS_STRING}
                                    inputRef={inputRefs.current[key]}
                                    onViewClick={() => openModalForView(key)}
                                    disabled={(kycStatus !== "INITIATED" && kycStatus !== null) || files[key]?.status === 'uploading'} />
                            );
                        })}
                    </div>
                </div>

                {/* Centered Additional Documents Header */}
                <div className="text-center my-8">
                    <h2 className="text-2xl font-bold text-gray-800">
                        Additional Supporting Documents
                    </h2>
                    <p className="mt-1 text-md text-gray-600">
                        Upload any additional documents that may support your application (Optional).
                    </p>
                </div>

                {/* Additional Documents Section with Gray Background */}
                <div className="bg-[#f0f0f0] p-4 sm:p-6 rounded-lg mb-10">
                    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-5">
                        {orderedOtherDocKeys.map((docKey, index) => {
                            const previousDocKey = index > 0 ? orderedOtherDocKeys[index - 1] : null;
                            const showCurrentField = previousDocKey ? (files[previousDocKey]?.status === 'uploaded') : true;

                            if (showCurrentField) {
                                const config = fieldConfigurations[docKey] || { label: `Other Document ${index + 1}`, isRequired: false };
                                if (!files[docKey]) {
                                    console.warn(`File state for other document key "${docKey}" is missing.`);
                                    return null;
                                }
                                return (
                                    <FileUploadField
                                        key={docKey}
                                        label={config.label}
                                        docKey={docKey}
                                        isRequired={config.isRequired}
                                        fileState={files[docKey]}
                                        onFileSelect={handleFileSelect}
                                        accept={ACCEPTED_FORMATS_STRING}
                                        inputRef={inputRefs.current[docKey]}
                                        onViewClick={() => openModalForView(docKey)}
                                        disabled={(kycStatus !== "INITIATED" && kycStatus !== null) || files[docKey]?.status === 'uploading'}
                                    />
                                );
                            }
                            return null;
                        })}
                    </div>
                </div>

                {submitError && (
                    <div className="mb-6 bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-md text-sm text-center" role="alert">
                        <span className="block sm:inline">{submitError}</span>
                    </div>
                )}

                <div className="flex justify-center pb-8">
                    <button
                        type="button"
                        onClick={handleFinalSubmit}
                        disabled={
                            (kycStatus !== "INITIATED" && kycStatus !== null) ||
                            isSubmitting ||
                            Object.values(files).some(s => s.status === 'uploading') ||
                            isUploadingGlobal
                        }
                        className={`px-10 py-3 text-base font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition duration-150 ease-in-out flex items-center justify-center
                    ${isSubmitting || (kycStatus !== "INITIATED" && kycStatus !== null) || Object.values(files).some(s => s.status === 'uploading') || isUploadingGlobal
                                ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
                                : 'bg-[#003a39] hover:bg-gray-700 text-white'
                            }`}
                    >
                        {isSubmitting ? (
                            <><LoadingSpinner /> Processing...</>
                        ) : Object.values(files).some(s => s.status === 'uploading') ? (
                            <><LoadingSpinner /> Uploading Files...</>
                        ) : 'Save and Continue'}
                    </button>
                </div>
            </div>

            {/* === RIGHT SIDE (4/12) - NEW WELCOME PANEL WITH GRADIENT === */}
            <div className="hidden lg:flex w-4/12 bg-gradient-to-br from-[#E6F9F4] to-white p-10 flex-col items-center justify-center border-l">
                <div className="max-w-md w-full">
                    <img src={require("../../../images/salor_telescope.png")} alt="Welcome Aboard" className="w-48 h-auto mx-auto mb-6" />

                    <h2 className="text-2xl font-bold text-center text-[#003a39] mb-3">
                        Welcome Aboard,
                    </h2>
                    <p className="text-center text-gray-600 text-md mb-4">
                        Congratulation on your eligibility.
                    </p>
                    <p className="text-center text-gray-600 text-sm mb-6">
                        Your are just one step away from completing your registration. You need to share your business documents. We will verify that and post that you can access you dashboard.
                    </p>

                    <div className="space-y-4 text-gray-700">
                        <div className="flex items-start">
                            <svg className="w-6 h-6 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                            <span>Commercial Registration</span>
                        </div>
                        <div className="flex items-start">
                            <svg className="w-6 h-6 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                            <span>Trade License</span>
                        </div>
                        <div className="flex items-start">
                            <svg className="w-6 h-6 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                            <span>Tax Card</span>
                        </div>
                        <div className="flex items-start">
                            <svg className="w-6 h-6 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                            <span>Establishment Card</span>
                        </div>
                        <div className="flex items-start">
                            <svg className="w-6 h-6 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                            <span>Business Address (National Address certificate/ utility bills of business /business bank statement)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default BusinessDocuments;