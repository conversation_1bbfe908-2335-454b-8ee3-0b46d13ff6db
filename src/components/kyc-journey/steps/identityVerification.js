import React, { useState, useRef, useEffect } from 'react';
import { updateKyc, uploadKycDocument } from '../../../api/kyc';
import Modal from 'react-modal';
import { prepareKycData } from '../../utils';
import { getKycInfo } from '../../../api/kyc';
import SharedCache from '../../../sharedCache';
import { shortenDocumentName } from '../../../components/utils';

const IdentityVerification = ({ onNext, onBack }) => {
    const [formState, setFormState] = useState({
        isResident: 'yes',
        qatariId: {},
        passport: {},
        utilityBill: {},
        hasUploadedUtilityBill: false,
        hasUploadedIdDocument: false
    });

    const [userData, setUserData] = useState({})

    const [documentsToUpload, setDocumentsToUpload] = useState({
        idDocument: null,
        utilityBill: null
    });

    const [documentNames, setDocumentNames] = useState({
        idDocument: userData?.kyc?.qatariId?.filePath?.split('/').pop() || userData?.kyc?.passport?.filePath?.split('/').pop() || '',
        utilityBill: userData?.kyc?.utilityBill?.filePath?.split('/').pop() || ''
    });

    const [errors, setErrors] = useState({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [uploadStatus, setUploadStatus] = useState({
        idDocument: '',
        utilityBill: ''
    });

    const idDocumentRef = useRef(null);
    const utilityBillRef = useRef(null);

    const [modalIsOpen, setModalIsOpen] = useState(false);
    const [modalDocumentUrl, setModalDocumentUrl] = useState(null);

    useEffect(() => {
        const fetchData = async () => {
            const userDataFromCache = SharedCache.get("user") || {};

            if (userDataFromCache._id) {
                try {
                    const userKycDataRes = await getKycInfo(userDataFromCache._id);

                    if (userKycDataRes && userKycDataRes.user) {
                        setUserData(userKycDataRes.user);

                        setFormState({
                            isResident: userKycDataRes.user?.isResident || 'yes',
                            qatariId: userKycDataRes.user?.kyc?.qatariId || {},
                            passport: userKycDataRes.user?.kyc?.passport || {},
                            utilityBill: userKycDataRes.user?.kyc?.utilityBill || {},
                            hasUploadedUtilityBill: !!userKycDataRes.user?.kyc?.utilityBill?.signedUrl,
                            hasUploadedIdDocument: !!(userKycDataRes.user?.kyc?.qatariId?.signedUrl || userKycDataRes.user?.kyc?.passport?.signedUrl)
                        });

                        setDocumentNames({
                            idDocument: userKycDataRes.user?.kyc?.qatariId?.filePath?.split('/').pop() || userKycDataRes.user?.kyc?.passport?.filePath?.split('/').pop() || '',
                            utilityBill: userKycDataRes.user?.kyc?.utilityBill?.filePath?.split('/').pop() || ''
                        });

                        console.log(userKycDataRes.user, "userdata from api");
                    } else {
                        // Handle cases where user or user data is not present.
                        setFormState({
                            isResident: userDataFromCache?.isResident || 'yes',
                            qatariId: userDataFromCache?.kyc?.qatariId || {},
                            passport: userDataFromCache?.kyc?.passport || {},
                            utilityBill: userDataFromCache?.kyc?.utilityBill || {},
                            hasUploadedUtilityBill: !!userDataFromCache?.kyc?.utilityBill?.signedUrl,
                            hasUploadedIdDocument: !!(userDataFromCache?.kyc?.qatariId?.signedUrl || userDataFromCache?.kyc?.passport?.signedUrl)
                        });
                        setDocumentNames({
                            idDocument: userDataFromCache?.kyc?.qatariId?.filePath?.split('/').pop() || userDataFromCache?.kyc?.passport?.filePath?.split('/').pop() || '',
                            utilityBill: userDataFromCache?.kyc?.utilityBill?.filePath?.split('/').pop() || ''
                        });
                        console.log("user or user data not found");
                    }
                } catch (error) {
                    console.error("Error fetching KYC info:", error);
                    setFormState({
                        isResident: userDataFromCache?.isResident || 'yes',
                        qatariId: userDataFromCache?.kyc?.qatariId || {},
                        passport: userDataFromCache?.kyc?.passport || {},
                        utilityBill: userDataFromCache?.kyc?.utilityBill || {},
                        hasUploadedUtilityBill: !!userDataFromCache?.kyc?.utilityBill?.signedUrl,
                        hasUploadedIdDocument: !!(userDataFromCache?.kyc?.qatariId?.signedUrl || userDataFromCache?.kyc?.passport?.signedUrl)
                    });
                    setDocumentNames({
                        idDocument: userDataFromCache?.kyc?.qatariId?.filePath?.split('/').pop() || userDataFromCache?.kyc?.passport?.filePath?.split('/').pop() || '',
                        utilityBill: userDataFromCache?.kyc?.utilityBill?.filePath?.split('/').pop() || ''
                    });
                }
            } else {
                setFormState({
                    isResident: userDataFromCache?.isResident || 'yes',
                    qatariId: userDataFromCache?.kyc?.qatariId || {},
                    passport: userDataFromCache?.kyc?.passport || {},
                    utilityBill: userDataFromCache?.kyc?.utilityBill || {},
                    hasUploadedUtilityBill: !!userDataFromCache?.kyc?.utilityBill?.signedUrl,
                    hasUploadedIdDocument: !!(userDataFromCache?.kyc?.qatariId?.signedUrl || userDataFromCache?.kyc?.passport?.signedUrl)
                });

                setDocumentNames({
                    idDocument: userDataFromCache?.kyc?.qatariId?.filePath?.split('/').pop() || userDataFromCache?.kyc?.passport?.filePath?.split('/').pop() || '',
                    utilityBill: userDataFromCache?.kyc?.utilityBill?.filePath?.split('/').pop() || ''
                });
            }
        };

        fetchData();

    }, []);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormState(prev => ({
            ...prev,
            [name]: value,
            // Reset ID document fields when residency status changes
            ...(name === 'isResident' && value === 'no'
                ? { qatariId: {}, hasUploadedIdDocument: !!userData?.kyc?.passport?.signedUrl }
                : name === 'isResident' && value === 'yes'
                    ? { passport: {}, hasUploadedIdDocument: !!userData?.kyc?.qatariId?.signedUrl }
                    : {}
            )
        }));

        // Update document names when residency status changes
        if (name === 'isResident') {
            setDocumentNames(prev => ({
                ...prev,
                idDocument: value === 'yes'
                    ? userData?.kyc?.qatariId?.filePath?.split('/').pop() || ''
                    : userData?.kyc?.passport?.filePath?.split('/').pop() || ''
            }));
        }

        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: null
            }));
        }
    };

    const handleFileChange = (e, documentType) => {
        const file = e.target.files[0];
        if (file) {
            setDocumentsToUpload(prev => ({
                ...prev,
                [documentType]: file
            }));

            setDocumentNames(prev => ({
                ...prev,
                [documentType]: file.name
            }));

            // Clear error when a file is selected
            if (errors[documentType]) {
                setErrors(prev => ({
                    ...prev,
                    [documentType]: null
                }));
            }
        }
    };

    const handleFileUpload = async (documentType) => {
        if (!documentsToUpload[documentType]) {
            setErrors(prev => ({
                ...prev,
                [documentType]: 'Please select a file to upload'
            }));
            return;
        }

        setUploadStatus(prev => ({
            ...prev,
            [documentType]: 'uploading'
        }));

        try {
            const formData = new FormData();
            // Determine the correct document type for the backend
            const actualDocumentType = documentType === 'idDocument'
                ? (formState.isResident === 'yes' ? 'qatariId' : 'passport')
                : 'utilityBill';

            formData.append('documentType', actualDocumentType);
            formData.append('file', documentsToUpload[documentType]);

            // Get userId from SharedCache
            const currentUser = SharedCache.get("user");
            const userId = currentUser?._id || currentUser?.id; // Safely access _id or id

            if (!userId) {
                throw new Error("User ID not found in cache");
            }

            formData.append('userId', userId);

            const response = await uploadKycDocument(userId, actualDocumentType, documentsToUpload[documentType]);

            if (response.success) {
                setUploadStatus(prev => ({
                    ...prev,
                    [documentType]: 'success'
                }));

                let updatedKycData = { ...userData.kyc }; // Start with existing KYC data

                if (documentType === 'idDocument') {
                    // Update the correct ID document based on resident status
                    const idDocumentField = formState.isResident === 'yes' ? 'qatariId' : 'passport';

                    // Update the specific ID document in local state
                    setFormState(prev => ({
                        ...prev,
                        hasUploadedIdDocument: true,
                        [idDocumentField]: {
                            filePath: response.filePath,
                            signedUrl: response.signedUrl,
                            uploadedOn: new Date(),
                            mimeType: documentsToUpload[documentType].type
                        }
                    }));

                    // Update document name
                    setDocumentNames(prev => ({
                        ...prev,
                        idDocument: response.filePath.split('/').pop()
                    }));

                    // Update KYC data for API
                    updatedKycData[idDocumentField] = {
                        filePath: response.filePath,
                        signedUrl: response.signedUrl,
                        uploadedOn: new Date(),
                        mimeType: documentsToUpload[documentType].type
                    };

                    // Clear the other ID document field if needed
                    if (idDocumentField === 'qatariId') {
                        updatedKycData.passport = {};
                    } else {
                        updatedKycData.qatariId = {};
                    }
                } else {
                    // Update utility bill in local state
                    setFormState(prev => ({
                        ...prev,
                        hasUploadedUtilityBill: true,
                        utilityBill: {
                            filePath: response.filePath,
                            signedUrl: response.signedUrl,
                            uploadedOn: new Date(),
                            mimeType: documentsToUpload[documentType].type
                        }
                    }));

                    // Update document name
                    setDocumentNames(prev => ({
                        ...prev,
                        utilityBill: response.filePath.split('/').pop()
                    }));

                    // Update KYC data for API
                    updatedKycData.utilityBill = {
                        filePath: response.filePath,
                        signedUrl: response.signedUrl,
                        uploadedOn: new Date(),
                        mimeType: documentsToUpload[documentType].type
                    };
                }

                // Call updateKyc to update the KYC data in the backend
                const updateKycResponse = await updateKyc({
                    userId: userId, // Use userId from cache
                    kyc: updatedKycData
                });

                if (!updateKycResponse.success) {
                    console.error("Error updating KYC after upload:", updateKycResponse.message);
                    setErrors(prev => ({
                        ...prev,
                        [documentType]: updateKycResponse.message || 'Failed to update KYC after upload'
                    }));
                    setUploadStatus(prev => ({
                        ...prev,
                        [documentType]: 'error'
                    }));
                    return;
                }

                // After successful upload, show the document preview
                const documentToView = documentType === 'idDocument'
                    ? (formState.isResident === 'yes' ? formState.qatariId : formState.passport)
                    : formState.utilityBill;

                setModalDocumentUrl(response.signedUrl);
                setModalIsOpen(true);

            } else {
                setUploadStatus(prev => ({
                    ...prev,
                    [documentType]: 'error'
                }));
                setErrors(prev => ({
                    ...prev,
                    [documentType]: response.message || 'Failed to upload document'
                }));
            }
        } catch (error) {
            console.error(`Error uploading ${documentType}:`, error);
            setUploadStatus(prev => ({
                ...prev,
                [documentType]: 'error'
            }));
            setErrors(prev => ({
                ...prev,
                [documentType]: 'An error occurred while uploading the document'
            }));
        }
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formState.hasUploadedIdDocument) {
            newErrors.idDocument = 'Identity document is required';
        }

        if (!formState.hasUploadedUtilityBill) {
            newErrors.utilityBill = 'Proof of address is required';
        }

        return newErrors;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        const validationErrors = validateForm();
        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
        }

        setIsSubmitting(true);

        try {
            // Use prepareKycData to format the userData for API
            const kycData = prepareKycData({
                ...formState,
                // Add any additional fields required by prepareKycData
                // You might need to pass the full user object or additional user details
                firstName: userData?.firstName,
                lastName: userData?.lastName,
                email: userData?.email,
                // Add other fields from userData or existing form state as needed
            }, {
                _id: userData._id // Assuming data contains the userId
            });

            // Call onNext with the prepared KYC data
            onNext(kycData);
        } catch (error) {
            console.error('Error submitting form:', error);
            setErrors({
                submit: 'An error occurred while saving your information. Please try again.'
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const openModal = (doc) => {
        if (doc && doc.signedUrl) {
            setModalDocumentUrl(doc.signedUrl);
            setModalIsOpen(true);
        } else {
            console.error('Document URL not found:', doc);
        }
    };

    const closeModal = () => {
        setModalDocumentUrl(null);
        setModalIsOpen(false);
    };

    // Helper function to determine the correct document to view
    const getIdDocumentToView = () => {
        return formState.isResident === 'yes' ? formState.qatariId : formState.passport;
    };

    return (
        <div className="space-y-6">
            <div className="flex justify-between pt-6">
                <h2 className="text-2xl font-bold">E-KYC</h2>
                <button
                    type="button"
                    disabled
                    className="px-4 py-2 bg-gray-300 text-gray-600 rounded-md cursor-not-allowed"
                >
                    Complete E-KYC Registration
                </button>
            </div>
            <form onSubmit={handleSubmit} className="space-y-6">
                {/* Resident Status */}
                <div className="bg-blue-50 p-4 rounded-md">
                    <h3 className="text-lg font-semibold mb-4">Resident Status</h3>
                    <div className="flex space-x-4">
                        <label className="flex items-center">
                            <input
                                type="radio"
                                name="isResident"
                                value="yes"
                                checked={formState.isResident === 'yes'}
                                onChange={handleChange}
                                disabled={formState.hasUploadedIdDocument}
                                className="form-radio h-5 w-5 text-blue-600"
                            />
                            <span className="ml-2">Resident of Qatar</span>
                        </label>
                        <label className="flex items-center">
                            <input
                                type="radio"
                                name="isResident"
                                value="no"
                                checked={formState.isResident === 'no'}
                                onChange={handleChange}
                                disabled={formState.hasUploadedIdDocument}
                                className="form-radio h-5 w-5 text-blue-600"
                            />
                            <span className="ml-2">Not a Resident</span>
                        </label>
                    </div>
                </div>

                {/* ID Document Upload */}
                <div className="bg-blue-50 p-4 rounded-md">
                    <h3 className="text-lg font-semibold mb-4">Identity Document</h3>
                    <p className="mb-4 text-sm text-gray-600">
                        Please upload your {formState.isResident === 'yes' ? 'Qatari ID card (front and back)' : 'passport'}.
                    </p>

                    <div className="space-y-4">
                        <div className="border border-gray-300 rounded-md p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h4 className="font-medium">Identity Document <span className="text-red-500">*</span></h4>
                                    <p className="text-sm text-gray-500">
                                        Accepted formats: JPEG, PNG, PDF. Max size: 15MB
                                    </p>
                                </div>
                                <input type="file"
                                    ref={idDocumentRef}
                                    onChange={(e) => handleFileChange(e, 'idDocument')}
                                    className="hidden"
                                    accept=".jpg,.jpeg,.png,.pdf"
                                />
                                {formState.hasUploadedIdDocument ? (
                                    <div className="flex items-center text-[#004141]">
                                        <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        <span>Uploaded</span>
                                        <button
                                            type="button"
                                            onClick={() => openModal(getIdDocumentToView())}
                                            className="ml-2 px-3 py-1 bg-[#004141] text-white rounded-md   text-sm"
                                        >
                                            View
                                        </button>
                                        <button
                                            type="button"
                                            onClick={() => {
                                                setFormState(prev => ({ ...prev, hasUploadedIdDocument: false }));
                                                setDocumentNames(prev => ({ ...prev, idDocument: '' }));
                                            }}
                                            className="ml-2 px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 text-sm"
                                        >
                                            Re-upload
                                        </button>
                                    </div>
                                ) : (
                                    <button
                                        type="button"
                                        onClick={() => idDocumentRef.current.click()}
                                        className="px-4 py-2 bg-[#004141] text-white rounded-md  "
                                    >
                                        Select File
                                    </button>
                                )}
                            </div>

                            {documentNames.idDocument && (
                                <div className="mt-2 flex items-center justify-between">
                                    <span className="text-sm" title={documentNames.idDocument}>{shortenDocumentName(documentNames.idDocument)}</span>
                                    {!formState.hasUploadedIdDocument && (
                                        <button
                                            type="button"
                                            onClick={() => handleFileUpload('idDocument')}
                                            disabled={uploadStatus.idDocument === 'uploading'}
                                            className={`px-3 py-1 ${uploadStatus.idDocument === 'uploading'
                                                ? 'bg-gray-400 cursor-not-allowed'
                                                : 'bg-[#208039] hover:bg-[#208039]'
                                                } text-white rounded-md text-sm`}
                                        >
                                            {uploadStatus.idDocument === 'uploading' ? 'Uploading...' : 'Upload Now'}
                                        </button>
                                    )}
                                </div>
                            )}

                            {errors.idDocument && (
                                <p className="mt-2 text-sm text-red-500">{errors.idDocument}</p>
                            )}
                        </div>
                    </div>
                </div>

                {/* Proof of Address */}
                <div className="bg-gray-50 p-4 rounded-md">
                    <h3 className="text-lg font-semibold mb-4">Proof of Address</h3>
                    <p className="mb-4 text-sm text-gray-600">
                        Please upload a document showing your current address (utility bill, bank statement, etc.).
                        Document should be less than 3 months old.
                    </p>

                    <div className="border border-gray-300 rounded-md p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <h4 className="font-medium">Utility Bill or Bank Statement <span className="text-red-500">*</span></h4>
                                <p className="text-sm text-gray-500">
                                    Accepted formats: JPEG, PNG, PDF. Max size: 15MB
                                </p>
                            </div>
                            <input
                                type="file"
                                ref={utilityBillRef}
                                onChange={(e) => handleFileChange(e, 'utilityBill')}
                                className="hidden"
                                accept=".jpg,.jpeg,.png,.pdf"
                            />
                            {formState.hasUploadedUtilityBill ? (
                                <div className="flex items-center text-[#004141]">
                                    <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    <span>Uploaded</span>
                                    <button
                                        type="button"
                                        onClick={() => openModal(formState.utilityBill)}
                                        className="ml-2 px-3 py-1 bg-[#004141] text-white rounded-md   text-sm"
                                    >
                                        View
                                    </button>
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setFormState(prev => ({ ...prev, hasUploadedUtilityBill: false }));
                                            setDocumentNames(prev => ({ ...prev, utilityBill: '' }));
                                        }}
                                        className="ml-2 px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 text-sm"
                                    >
                                        Re-upload
                                    </button>
                                </div>
                            ) : (
                                <button
                                    type="button"
                                    onClick={() => utilityBillRef.current.click()}
                                    className="px-4 py-2 bg-[#004141] text-white rounded-md  "
                                >
                                    Select File
                                </button>
                            )}
                        </div>

                        {documentNames.utilityBill && (
                            <div className="mt-2 flex items-center justify-between">
                                <span className="text-sm" title={documentNames.utilityBill}>{shortenDocumentName(documentNames.utilityBill)}</span>
                                {!formState.hasUploadedUtilityBill && (
                                    <button
                                        type="button"
                                        onClick={() => handleFileUpload('utilityBill')}
                                        disabled={uploadStatus.utilityBill === 'uploading'}
                                        className={`px-3 py-1 ${uploadStatus.utilityBill === 'uploading'
                                            ? 'bg-gray-400 cursor-not-allowed'
                                            : 'bg-[#208039] hover:bg-[#208039]'
                                            } text-white rounded-md text-sm`}
                                    >
                                        {uploadStatus.utilityBill === 'uploading' ? 'Uploading...' : 'Upload Now'}
                                    </button>
                                )}
                            </div>
                        )}

                        {errors.utilityBill && (
                            <p className="mt-2 text-sm text-red-500">{errors.utilityBill}</p>
                        )}
                    </div>
                </div>

                {/* Verification Note */}
                <div className="bg-yellow-50 p-4 rounded-md">
                    <div className="flex items-start">
                        <div className="flex-shrink-0 pt-0.5">
                            <svg className="h-5 w-5 text-yellow-600" xmlns="http://www.w3.org/2000/svg" viewBox="00 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="ml-3">
                            <h3 className="text-sm font-medium text-yellow-800">Important</h3>
                            <div className="mt-2 text-sm text-yellow-700">
                                <p>All uploaded documents will be verified by our team. Any discrepancies may result in application rejection.</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Submit Error */}
                {errors.submit && (
                    <div className="bg-red-50 text-red-50 p-4 rounded-md">
                        {errors.submit}
                    </div>
                )}

                {/* Navigation Buttons */}
                <div className="flex justify-between pt-6">
                    <button
                        type="button"
                        onClick={onBack}
                        className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                        Back
                    </button>
                    <button
                        type="submit"
                        disabled={isSubmitting || !formState.hasUploadedIdDocument || !formState.hasUploadedUtilityBill}
                        className={`px-6 py-2 ${isSubmitting || !formState.hasUploadedIdDocument || !formState.hasUploadedUtilityBill
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-[#208039] hover:bg-[#208039]'
                            } text-white rounded-md flex items-center`}
                    >
                        {isSubmitting ? (
                            <>
                                <svg className="animate-spin h-5 w-5 mr-2" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z" />
                                </svg>
                                Processing...
                            </>
                        ) : (
                            'Next'
                        )}
                    </button>
                </div>
            </form>

            <Modal
                isOpen={modalIsOpen}
                onRequestClose={closeModal}
                style={{
                    overlay: {
                        backgroundColor: 'rgba(0, 0, 0, 0.6)'
                    },
                    content: {
                        top: '50%',
                        left: '50%',
                        right: 'auto',
                        bottom: 'auto',
                        marginRight: '-50%',
                        transform: 'translate(-50%, -50%)',
                        padding: '20px',
                        width: '80%',
                        maxWidth: '800px'
                    }
                }}
                contentLabel="Document Preview"
            >
                <div className="flex justify-end">
                    <button onClick={closeModal} className="text-gray-500 hover:text-gray-700">
                        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                {modalDocumentUrl && (
                    <iframe src={modalDocumentUrl} className="w-full h-[600px]" title="Document Preview" />
                )}
            </Modal>
        </div>
    );
};

export default IdentityVerification;