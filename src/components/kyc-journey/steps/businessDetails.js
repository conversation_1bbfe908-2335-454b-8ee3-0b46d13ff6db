import React, { useState, useCallback, useRef, useEffect } from 'react'; // Added useEffect
import SharedCache from '../../../sharedCache';
import config from '../../../config.json'; // Assuming config file path
// --- Import the API function ---
import { getKycInfo } from '../../../api/kyc'; // Adjust path if needed
import { shortenDocumentName } from '../../../components/utils';
import {
    ChevronDownIcon,
    ChevronUpIcon,
    TrashIcon as HeroTrashIcon, // Renamed to avoid conflict if you have another TrashIcon
    EyeIcon,
    ArrowPathIcon as HeroArrowPathIcon, // Renamed
    XMarkIcon,
    ArrowUpTrayIcon as HeroUploadIcon // For the new upload field style
} from '@heroicons/react/24/outline'; // Or solid, as needed
import LoadingModal from '../../Reusable/Loading';

// --- Constants ---
const MAX_SHAREHOLDERS = 10;
const MAX_FILE_SIZE_MB = 20;
const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;
const ACCEPTED_FORMATS = ['application/pdf', 'image/jpeg', 'image/png'];
const ACCEPTED_FORMATS_STRING = '.pdf, .jpeg, .jpg, .png';
const ACCEPTED_FORMATS_DISPLAY = 'PDF, JPEG, PNG';

// (Assuming shortenDocumentName is available in this scope)
const UploadingPopup = ({ fileName }) => {
    return <LoadingModal message={fileName
        ? `We are processing your file (${shortenDocumentName(fileName, 20)}) and preparing it for verification.`
        : "We are processing your details. This may take a moment."
    } />;
};
// LoadingSpinner for FileUploadField (from BusinessDocuments.js)
const LoadingSpinner = () => (
    <svg className="animate-spin h-5 w-5 mr-1 inline-block text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
);

// --- ICONS (ensure you have these or similar from heroicons) ---

const UploadIconFromBizDocs = () => ( // From your reference, for the file upload box
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1 inline-block text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
    </svg>
);

const DeleteConfirmationModal = ({ isOpen, onClose, onConfirm, shareholderName }) => {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4">
            <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Confirm Deletion</h2>
                <p className="text-gray-600 mb-6">
                    Are you sure you want to delete <span className="font-medium">{shareholderName}</span>? This action cannot be undone.
                </p>
                <div className="flex justify-end space-x-3">
                    <button
                        type="button"
                        onClick={onClose}
                        className="px-4 py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                        Cancel
                    </button>
                    <button
                        type="button"
                        onClick={onConfirm}
                        className="px-4 py-2 text-sm bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors"
                    >
                        Delete
                    </button>
                </div>
            </div>
        </div>
    );
};

// --- Adapted Document Review Modal (Simplified) ---
const ShareholderDocumentReviewModal = ({
    isOpen,
    onClose,
    docKey,
    fileName,
    signedUrl, // Can be a blob URL or remote URL
    onReplace, // Callback to trigger file input: onReplace(shareholderIndex, docKey)
    shareholderIndexForDoc, // Pass this to onReplace
}) => {
    if (!isOpen) return null;

    const handleReplace = () => {
        if (onReplace) {
            onReplace(shareholderIndexForDoc, docKey);
        }
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[9998] p-4" onClick={onClose}>
            <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col" onClick={(e) => e.stopPropagation()}>
                <div className="flex justify-between items-center p-4 border-b border-gray-200">
                    <h2 className="text-xl font-semibold text-gray-800">Review Document: {shortenDocumentName(fileName, 40)}</h2>
                    <button onClick={onClose} className="text-gray-500 hover:text-gray-700 p-1">
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>
                <div className="flex-grow p-1 bg-gray-100 overflow-auto">
                    {signedUrl ? (
                        <iframe
                            src={signedUrl}
                            title={fileName || 'Document Preview'}
                            className="w-full h-full min-h-[300px] md:min-h-[500px]"
                            frameBorder="0"
                        />
                    ) : (
                        <div className="flex items-center justify-center h-full">
                            <p className="text-gray-500">Document preview not available.</p>
                        </div>
                    )}
                </div>
                <div className="p-4 border-t border-gray-200 flex justify-end space-x-3 bg-gray-100">
                    <button
                        onClick={onClose}
                        className="px-4 py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-200 transition-colors"
                    >
                        Close
                    </button>
                    {onReplace && ( // Only show replace if handler is provided (i.e., form not disabled)
                        <button
                            onClick={handleReplace}
                            className="px-6 py-2 text-sm text-white bg-[#214D4B] rounded-md hover:bg-[#1A3C3A] transition-colors flex items-center justify-center"
                        >
                            <HeroArrowPathIcon className="h-5 w-5 mr-2" /> Replace
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

const LoadingPopup = ({ fileName }) => {
    return <LoadingModal message='Please wait while we load your journey data.' />;
};

// --- Helper Icons --- (Keep existing icons)
const UploadIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1 inline-block text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
    </svg>
);
const CheckIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1 inline-block text-green-500" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
    </svg>
);
const TrashIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
    </svg>
);

const ClockIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
);
const XCircleIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
    </svg>
);
const InformationCircleIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
    </svg>
);
const CheckCircleIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20" >
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
    </svg>
);

// --- Helper to extract filename ---
const getFileNameFromPath = (path) => {
    // ... (keep existing helper function)
    if (!path) return '';
    try {
        const decodedPath = decodeURIComponent(path);
        return decodedPath.split('/').pop() || '';
    } catch (e) {
        console.error("Error decoding or splitting path:", path, e);
        const parts = path.split('/');
        return parts.length > 0 ? parts[parts.length - 1] : '';
    }
};

// --- Default State for a new Shareholder ---
const getDefaultShareholder = (id = null) => {
    // Generate a unique ID if none provided
    const uniqueId = id || `temp-${Date.now()}-${Math.floor(Math.random() * 10000)}`;

    return {
        id: uniqueId, // Use provided ID or generate new one
        firstName: '', middleName: '', lastName: '', email: '',
        address: { zone: '', streetNo: '', buildingNo: '', floorNo: '', unitNo: '', additionalLandmark: '-' },
        passport: { file: null, fileName: '', status: 'empty', error: '', signedUrl: null, verificationStatus: null }, // Added verificationStatus
        qid: { file: null, fileName: '', status: 'empty', error: '', signedUrl: null, verificationStatus: null }, // Added verificationStatus
        proofOfAddress: { file: null, fileName: '', status: 'empty', error: '', signedUrl: null, verificationStatus: null }, // Added verificationStatus
    };
};

// --- Refactored ShareholderFileUploadField ---
const ShareholderFileUploadField = (props) => {
    const {
        label,
        description, // You were already passing this
        docKey,
        isRequired,
        fileState = {}, // Default to empty object
        setFileState,
        onFileValidated, // This is the prop your FinancialDocuments.js uses
        accept,
        inputRef,
        disabled,
        // NEW prop to handle triggering the modal for viewing
        onViewClick,
    } = props;

    const handleFileChange = (event) => {
        const file = event.target.files?.[0];
        if (!file) return;

        // Basic validation (already in your FinancialDocuments FileUploadField, kept here)
        let error = '';
        // Ensure these constants are accessible here, either passed as props or defined globally/module-level
        // For this example, assuming they are defined at the top of FinancialDocuments.js
        if (file.size > MAX_FILE_SIZE_BYTES) { // MAX_FILE_SIZE_BYTES should be defined
            error = `File exceeds maximum size of ${MAX_FILE_SIZE_MB} MB.`;
        } else {
            const extension = file.name.split('.').pop()?.toLowerCase();
            const allowedExtensions = accept.split(',').map(ext => ext.trim().substring(1).toLowerCase());
            // Use ACCEPTED_FORMATS if more precise, or stick to extension check. 
            // BusinessDocuments uses a combined check.
            if (!ACCEPTED_FORMATS.includes(file.type) && (!extension || !allowedExtensions.includes(extension))) {
                error = `Invalid file format. Accepted: ${ACCEPTED_FORMATS_DISPLAY}`;
            }
        }

        if (error) {
            // Use setFileState if available (from FinancialDocuments logic), otherwise onFileValidated could also set error status
            if (setFileState) {
                setFileState(docKey, (prevState) => ({
                    ...prevState,
                    file: null, fileName: file.name, // Keep fileName for context on error
                    status: 'error', error: error,
                    signedUrl: prevState?.signedUrl, // Preserve old URL if replacing and new fails validation
                    extractedData: prevState?.extractedData
                }));
            }
        } else {
            if (onFileValidated) {
                onFileValidated(docKey, file); // Notify parent (FinancialDocuments) for upload trigger
            }
        }

        if (inputRef.current) {
            inputRef.current.value = '';
        }
    };

    const handleReplace = () => {
        if (!disabled) {
            inputRef.current?.click();
        }
    };

    const handleView = () => {
        if (onViewClick) {
            onViewClick(docKey); // Call parent's handler to open modal
        } else {
            // Fallback or error if onViewClick is not provided
            console.warn("onViewClick not provided to FileUploadField for docKey:", docKey);
            // You could add the old direct window.open(fileState?.signedUrl) here as a fallback
            // but the goal is to use the modal.
            if (fileState?.signedUrl) {
                window.open(fileState.signedUrl, '_blank', 'noopener,noreferrer');
            } else {
                alert("No document to view.");
            }
        }
    };

    // --- Helper Function for Status Badge with Icons ---
    const renderVerificationStatus = (status) => {
        // Don't render status if file is not uploaded yet
        if (fileState?.status !== 'uploaded') {
            return null;
        }

        let bgColor = 'bg-blue-100';    // Default blue for Submitted
        let textColor = 'text-blue-700'; // Default blue text
        let IconComponent = InformationCircleIcon; // Default icon
        let text = 'Submitted'; // Default text

        switch (status) {
            case 'PENDING':
                bgColor = 'bg-yellow-100';
                textColor = 'text-yellow-700';
                IconComponent = ClockIcon;
                text = 'Pending';
                break;
            case 'VERIFIED':
                bgColor = 'bg-green-100';
                textColor = 'text-green-700';
                IconComponent = CheckCircleIcon; // Use specific verified icon
                text = 'Verified';
                break;
            case 'REJECTED':
                bgColor = 'bg-red-100';
                textColor = 'text-red-700';
                IconComponent = XCircleIcon;
                text = 'Rejected';
                break;
            case 'SKIPPED':
                bgColor = 'bg-gray-100'; // Neutral gray for skipped
                textColor = 'text-gray-700';
                IconComponent = InformationCircleIcon; // Info icon for skipped
                text = 'Skipped';
                break;
            case 'SUBMITTED':
            default:
                // Use defaults defined above (Blue)
                break;
        }

        return (
            <div className="z-10">
                <span className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium ${bgColor} ${textColor}`}>
                    <IconComponent className="h-4 w-4" />
                    {text}
                </span>
            </div>
        );

    };
    // --- End Helper ---




    return (
        <div className="relative border border-gray-200 rounded-lg p-3 bg-white shadow-sm flex flex-col justify-between w-full max-w-full overflow-hidden">
            <div className="flex-1">
                <div className="flex flex-wrap items-center justify-between mb-1 gap-2">
                    <label className="text-sm font-medium text-gray-700">
                        {label} {isRequired && <span className="text-red-500">*</span>}
                    </label>
                    {fileState?.status === 'uploaded' && (
                        <span className="text-sm text-green-600 font-semibold flex items-center shrink-0">
                            <CheckIcon className="h-4 w-4 mr-1" /> Uploaded
                        </span>
                    )}
                </div>

                {fileState?.status === 'uploading' ? (
                    <div className="text-sm text-blue-600 font-semibold flex flex-col items-center text-center gap-1">
                        <LoadingSpinner />
                        <span>Uploading...</span>
                        {fileState.fileName && (
                            <span className="text-xs text-gray-500 truncate max-w-full break-all">
                                {shortenDocumentName(fileState.fileName, 15)}
                            </span>
                        )}
                    </div>
                ) : fileState?.status === 'uploaded' ? (
                    <div>
                        <div className="bg-[#eff7f7] border-2 border-dashed border-gray-300 rounded-md p-3 text-center overflow-hidden">
                            <span
                                className="text-sm font-medium text-gray-700 truncate break-words block"
                                title={fileState.fileName}
                            >
                                {fileState.fileName ? shortenDocumentName(fileState.fileName, 15) : 'File uploaded'}
                            </span>
                        </div>
                        {fileState.error && (
                            <p className="mt-1 text-xs text-orange-500 text-center break-words">
                                Note: {fileState.error}
                            </p>
                        )}
                    </div>
                ) : (
                    <div>
                        <button
                            type="button"
                            onClick={() => !disabled && inputRef.current?.click()}
                            className={`bg-[#eff7f7] w-full flex flex-col items-center justify-center px-3 py-2 border-2 border-dashed rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#004141] group
              ${disabled ? 'border-gray-300 bg-gray-100 text-gray-400 cursor-not-allowed'
                                    : fileState?.error ? 'border-red-400 text-red-600 hover:bg-red-50'
                                        : 'border-gray-300 text-gray-500 hover:border-gray-400 hover:bg-gray-100'}`}
                            disabled={disabled}
                        >
                            <div className="flex items-center space-x-2">
                                <UploadIcon />
                                <span>Upload</span>
                            </div>
                            <p className="text-xs text-gray-500 text-center">Max filesize {MAX_FILE_SIZE_MB}MB</p>
                        </button>

                        <p className="mt-1 text-xs text-center text-gray-500">Formats: {ACCEPTED_FORMATS_DISPLAY}</p>
                        {fileState?.error && fileState.status === 'error' && (
                            <p className="mt-1 text-xs text-red-500 text-center break-words">{fileState.error}</p>
                        )}
                    </div>
                )}
            </div>

            {fileState?.status === 'uploaded' && (
                <div className="mt-3 flex flex-wrap items-center justify-between gap-y-2">
                    <div className="flex gap-4 flex-wrap">
                        {(fileState.signedUrl || fileState.file) && (
                            <button
                                type="button"
                                onClick={handleView}
                                className="flex items-center text-xs text-black hover:underline font-medium"
                            >
                                <EyeIcon className="h-4 w-4 mr-1" /> View
                            </button>
                        )}
                        {!disabled && (
                            <button
                                type="button"
                                onClick={handleReplace}
                                className="flex items-center text-xs text-black hover:underline font-medium"
                            >
                                <HeroArrowPathIcon className="h-4 w-4 mr-1" /> Replace
                            </button>
                        )}
                    </div>
                    <div className="shrink-0">{renderVerificationStatus(fileState?.verificationStatus)}</div>
                </div>
            )}

            <input
                ref={inputRef}
                type="file"
                className="hidden"
                onChange={handleFileChange}
                accept={accept}
                disabled={disabled}
            />
        </div>
    );


};
// --- Main Shareholder Additions Page Component ---
const ShareholderAdditions = ({ onNext, onBack }) => {
    const [shareholders, setShareholders] = useState([]); // Start empty, will be populated by useEffect
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitError, setSubmitError] = useState('');
    const [isLoading, setIsLoading] = useState(true); // Start loading
    const [loadError, setLoadError] = useState(''); // State for initial loading errors
    const [kycStatus, setKycStatus] = useState(null);
    const [isApplicantBeneficialOwner, setIsApplicantBeneficialOwner] = useState(null); // null, 'yes', or 'no'
    const [mainUserData, setMainUserData] = useState(null); // T
    // --- New UI State ---
    const [expandedShareholders, setExpandedShareholders] = useState({}); // { [shareholder.id]: true/false }
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [shareholderToDelete, setShareholderToDelete] = useState(null); // { id, name }
    const shareholderFileInputRefs = useRef({}); // Key: `${shareholder.id}-${docKey}`
    // Helper to manage refs array/object
    const getFileRef = (shareholderId, docKey) => {
        const key = `${shareholderId}-${docKey}`;
        if (!shareholderFileInputRefs.current[key]) {
            shareholderFileInputRefs.current[key] = React.createRef();
        }
        return shareholderFileInputRefs.current[key];
    };
    const [isDocumentModalOpen, setIsDocumentModalOpen] = useState(false);
    const [documentModalData, setDocumentModalData] = useState({
        fileName: '',
        signedUrl: '',
        docKey: '',
        shareholderIndexForDoc: -1,
        isLocalBlob: false,
    });
    // --- START: ADDED HANDLER FUNCTION ---
    const handleBeneficialOwnerChange = (event) => {
        const answer = event.target.value; // 'yes' or 'no'
        setIsApplicantBeneficialOwner(answer);

        // Ensure mainUserData is loaded and shareholders array is not empty
        if (mainUserData && shareholders.length > 0) {
            const firstShareholderOriginal = shareholders[0];
            const firstShareholderId = firstShareholderOriginal.id;

            // Check if the first shareholder is a default one (not from DB)
            // A shareholder from DB has a 24-char hex ID. Temp IDs are different.
            const isFirstShareholderDefaultOrTemp = firstShareholderId && !String(firstShareholderId).match(/^[0-9a-fA-F]{24}$/);

            if (answer === 'yes') {
                if (isFirstShareholderDefaultOrTemp) {
                    const updatedShareholders = [...shareholders];
                    // Prefill the first shareholder with main user's data
                    // It keeps its original temp ID to maintain React key stability if already rendered
                    updatedShareholders[0] = {
                        ...getDefaultShareholder(firstShareholderId), // Get default structure, keep temp ID
                        firstName: mainUserData.firstName || '',
                        lastName: mainUserData.lastName || '',
                        email: mainUserData.email || '',
                        // Middle name is not directly available in the root of user object in the sample.
                        // Address pre-filling: user.kyc has addressLine1, city etc. which are null in sample
                        // or business address. Shareholder form has zone, streetNo. Mapping is not direct.
                        // So, address fields will take default empty values from getDefaultShareholder.
                        // Document file states also remain default empty.
                    };
                    setShareholders(updatedShareholders);
                }
            } else if (answer === 'no') {
                // If "No" is selected, and the first shareholder slot was the one prefilled by "Yes".
                if (isFirstShareholderDefaultOrTemp) {
                    // Check if the current data in the first shareholder matches the mainUserData,
                    // indicating it was a clean prefill by the system.
                    const isCurrentlyAPerfectPrefill =
                        firstShareholderOriginal.firstName === mainUserData.firstName &&
                        firstShareholderOriginal.lastName === mainUserData.lastName &&
                        firstShareholderOriginal.email === mainUserData.email &&
                        (firstShareholderOriginal.middleName === '' || firstShareholderOriginal.middleName === undefined) && // Check if other text fields are default
                        Object.values(firstShareholderOriginal.address).every(val => val === '' || val === '-') && // Check if address fields are default
                        ['passport', 'qid', 'proofOfAddress'].every(docKey => firstShareholderOriginal[docKey]?.status === 'empty'); // Check if docs are default

                    if (isCurrentlyAPerfectPrefill) {
                        const updatedShareholders = [...shareholders];
                        // Reset to a clean default shareholder, keeping the original temp ID
                        updatedShareholders[0] = getDefaultShareholder(firstShareholderId);
                        setShareholders(updatedShareholders);
                    }
                    // If it wasn't a perfect prefill (e.g., user had already edited it),
                    // selecting "No" will not wipe their manual changes to that slot.
                }
            }
        }
    };
    // --- END: ADDED HANDLER FUNCTION ---

    useEffect(() => {
        let isMounted = true;

        const fetchShareholderData = async () => {
            setIsLoading(true);
            setLoadError('');
            const userFromCache = SharedCache.get('user') || {};
            const userId = userFromCache._id || userFromCache.id || "";

            if (!userId) {
                if (isMounted) {
                    setLoadError("User ID not found. Cannot load data.");
                    setIsLoading(false);
                    const defaultSh = getDefaultShareholder();
                    setShareholders([defaultSh]);
                    setExpandedShareholders({ [defaultSh.id]: true }); // Expand first by default
                }
                return;
            }

            try {
                const response = await getKycInfo(userId);
                if (!isMounted) return;

                if (response?.success && response?.user) {
                    setMainUserData(response.user);
                    if (response.user.kyc && typeof response.user.kyc.isBeneficialOwner === 'boolean') {
                        setIsApplicantBeneficialOwner(response.user.kyc.isBeneficialOwner ? 'yes' : 'no');
                    }
                    if (response.user.kyc && response.user.kyc.verificationStatus) {
                        setKycStatus(response.user.kyc.verificationStatus);
                    } else {
                        setKycStatus(null);
                    }

                    const dbShareholders = response.user.shareholders || [];
                    const initialExpandedState = {};
                    let processedShareholdersData;

                    if (dbShareholders.length > 0) {
                        processedShareholdersData = dbShareholders.map((dbSh, index) => {
                            const shId = dbSh._id || `temp-id-${Date.now()}-${index}`; // Ensure consistent ID
                            initialExpandedState[shId] = index === 0; // Expand first by default
                            // ... (rest of your mapping logic from existing code)
                            const createFileStateFromDb = (docData) => {
                                if (docData?.filePath && docData?.signedUrl) {
                                    return { file: null, fileName: getFileNameFromPath(docData.filePath), status: 'uploaded', error: '', signedUrl: docData.signedUrl, verificationStatus: docData.verificationStatus || null };
                                }
                                return { file: null, fileName: '', status: 'empty', error: '', signedUrl: null, verificationStatus: null };
                            };
                            return {
                                id: shId, // Use the generated or DB ID
                                firstName: dbSh.firstName || '', middleName: dbSh.middleName || '', lastName: dbSh.lastName || '', email: dbSh.email || '',
                                address: { zone: dbSh.address?.zone || '', streetNo: dbSh.address?.streetNo || '', buildingNo: dbSh.address?.buildingNo || '', floorNo: dbSh.address?.floorNo || '', unitNo: dbSh.address?.unitNo || '', additionalLandmark: dbSh.address?.additionalLandmark || '-' },
                                passport: createFileStateFromDb(dbSh.passport),
                                qid: createFileStateFromDb(dbSh.qid),
                                proofOfAddress: createFileStateFromDb(dbSh.proofOfAddress),
                            };
                        });
                    } else {

                         // If no shareholders are saved, try to pre-populate from Commercial Registration data
                    const parties = response.user.commercialRegistration?.extractedFields?.associatedParties;
                    let prePopulatedShareholders = [];

                    if (parties && Array.isArray(parties) && parties.length > 0) {
                        prePopulatedShareholders = parties
                            .filter(party => party && party.partyShare && String(party.partyShare).trim() !== '')
                            .map(party => {
                                const newShareholder = getDefaultShareholder(); // Creates a shareholder with a unique ID
                                newShareholder.firstName = party.partyName || '';
                                // All other fields (lastName, email, address, etc.) will use the default empty values
                                return newShareholder;
                            });
                    }

                    if (prePopulatedShareholders.length > 0) {
                        // If we successfully pre-populated from CR data, use that list
                        processedShareholdersData = prePopulatedShareholders;
                    } else {
                        // Otherwise (no CR data or no valid shareholders in CR), add one default empty shareholder
                        processedShareholdersData = [getDefaultShareholder()];
                    }

                    // Always expand the first item in the list by default
                    if (processedShareholdersData.length > 0) {
                        initialExpandedState[processedShareholdersData[0].id] = true;
                    }
                        // const defaultSh = getDefaultShareholder(); // Ensure getDefaultShareholder assigns a unique ID
                        // processedShareholdersData = [defaultSh];
                        // initialExpandedState[defaultSh.id] = true; // Expand default one
                    }
                    setShareholders(processedShareholdersData);
                    setExpandedShareholders(initialExpandedState);

                } else {
                    setLoadError("Could not load existing shareholder data. Starting fresh.");
                    const defaultSh = getDefaultShareholder();
                    setShareholders([defaultSh]);
                    setExpandedShareholders({ [defaultSh.id]: true });
                }
            } catch (error) {
                if (isMounted) {
                    setLoadError(`Error loading data: ${error.message}. Starting fresh.`);
                    const defaultSh = getDefaultShareholder();
                    setShareholders([defaultSh]);
                    setExpandedShareholders({ [defaultSh.id]: true });
                }
            } finally {
                if (isMounted) setIsLoading(false);
            }
        };

        fetchShareholderData();
        return () => { isMounted = false; };
    }, []);

    // --- State Update Handlers (Keep existing: addShareholder, removeShareholder, handleInputChange, handleShareholderFileUpdate) ---
    const addShareholder = () => {
        if (shareholders.length < MAX_SHAREHOLDERS) {
            setShareholders(prev => [...prev, getDefaultShareholder()]);
        }
    };
    const removeShareholder = (idToRemove) => {
        setShareholders(prev => prev.filter(sh => sh.id !== idToRemove));
    };
    const handleInputChange = (index, field, value) => {
        const addressFields = ['zone', 'streetNo', 'buildingNo', 'floorNo', 'unitNo', 'additionalLandmark'];
        setShareholders(prev =>
            prev.map((sh, i) => {
                if (i === index) {
                    if (addressFields.includes(field)) {
                        return { ...sh, address: { ...sh.address, [field]: value } };
                    } else {
                        return { ...sh, [field]: value };
                    }
                }
                return sh;
            })
        );
        if (submitError) setSubmitError('');
    };
    const handleShareholderFileUpdate = useCallback((index, docKey, fileState) => {
        setShareholders(prev =>
            prev.map((sh, i) =>
                i === index ? { ...sh, [docKey]: fileState } : sh
            )
        );
        if (submitError) setSubmitError('');
    }, [submitError]);


    const validateForm = () => {
        let isValid = true;
        let activeShareholders = []; // To keep track of shareholders being filled out

        // First, identify which shareholders are being actively filled
        shareholders.forEach((sh, index) => {
            // A shareholder is considered "active" if any field has been touched
            const isPartiallyFilled =
                sh.firstName.trim() ||
                sh.lastName.trim() ||
                sh.email.trim() ||
                sh.middleName.trim() ||
                Object.values(sh.address).some(val => val.trim() && val.trim() !== '-') ||
                sh.passport.file ||
                sh.qid.file ||
                sh.proofOfAddress.file;

            if (isPartiallyFilled) {
                activeShareholders.push({ ...sh, originalIndex: index });
            }
        });

        // If there are no active shareholders, the form is valid (we'll just submit nothing)
        if (activeShareholders.length === 0) {
            setSubmitError('');
            return true;
        }

        // Now, validate only the active shareholders
        activeShareholders.forEach(sh => {
            if (!sh.firstName.trim() || !sh.lastName.trim()) {
                isValid = false;
                setSubmitError(`First and Last Name are required for Shareholder ${sh.originalIndex + 1}.`);
            }

            if (!sh.email.trim()) {
                isValid = false;
                setSubmitError(`Email, first name and the last name is required for Shareholder ${sh.originalIndex + 1}.`);
            } else {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(sh.email.trim())) {
                    isValid = false;
                    setSubmitError(`Invalid email format for Shareholder ${sh.originalIndex + 1}.`);
                }
            }
        });

        if (isValid) {
            // Check for duplicate emails among active shareholders
            const emailCounts = activeShareholders.reduce((acc, sh) => {
                const email = sh.email.trim().toLowerCase();
                if (email) {
                    acc[email] = (acc[email] || 0) + 1;
                }
                return acc;
            }, {});

            for (const email in emailCounts) {
                if (emailCounts[email] > 1) {
                    isValid = false;
                    setSubmitError(`The email "${email}" is used for more than one shareholder. Please use unique emails.`);
                    break;
                }
            }
        }

        if (isValid) {
            setSubmitError('');
        }

        return isValid;
    };


    const handleSubmit = async (e) => {
        e.preventDefault();
        setSubmitError('');

        // Filter out shareholders that are completely empty
        const shareholdersToSubmit = shareholders.filter(sh =>
            sh.firstName.trim() || sh.lastName.trim() || sh.email.trim()
        );

        if (!validateForm()) { // validateForm will now work on the filtered list implicitly via the shareholders state
            console.log("Frontend validation failed.");
            return;
        }

        // If after filtering there are no shareholders, you can just proceed without an error
        if (shareholdersToSubmit.length === 0) {
            console.log("No shareholder data to submit, proceeding.");
            if (onNext) onNext([]);
            return;
        }

        setIsSubmitting(true);

        const user = SharedCache.get('user') || {};
        const userId = user._id || user.id || "";

        if (!userId) {
            setSubmitError("User session invalid or ID missing. Please log in again.");
            setIsSubmitting(false);
            return;
        }
        console.log(`Proceeding with submission for userId: ${userId}`);

        const API_BASE_URL = config.apiUrl || "https://madadapi.fundfina.com";
        const SHAREHOLDER_API_PATH = `${API_BASE_URL}/ops/invoiceFinancing`;
        let textUpdateResult; // To store the result of text data update

        try {
            console.log("Preparing shareholder text data for API...");
            // Map local state to the format expected by the backend
            const shareholdersTextData = shareholdersToSubmit.map(sh => ({
                // Only send _id if it's a valid MongoDB ObjectId (existing shareholder)
                _id: sh.id && String(sh.id).match(/^[0-9a-fA-F]{24}$/) ? sh.id : undefined,
                firstName: sh.firstName,
                middleName: sh.middleName,
                lastName: sh.lastName,
                email: sh.email,
                address: {
                    zone: sh.address.zone,
                    streetNo: sh.address.streetNo,
                    buildingNo: sh.address.buildingNo,
                    floorNo: sh.address.floorNo,
                    unitNo: sh.address.unitNo,
                    additionalLandmark: sh.address.additionalLandmark,
                }
                // DO NOT send file data in the text update
            }));

            console.log(`Sending text data update to: ${SHAREHOLDER_API_PATH}/shareholders/updateTextData`);
            const textUpdateResponse = await fetch(`${SHAREHOLDER_API_PATH}/shareholders/updateTextData`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json', /* + Auth */ },
                body: JSON.stringify({ userId, shareholders: shareholdersTextData }),
            });
            textUpdateResult = await textUpdateResponse.json();
            if (!textUpdateResponse.ok || !textUpdateResult.success) {
                // Include backend errors if available
                const errorDetail = textUpdateResult.errors ? ` Errors: ${textUpdateResult.errors.join(', ')}` : '';
                throw new Error(textUpdateResult.message || `Failed to update text data (HTTP ${textUpdateResponse.status})${errorDetail}`);
            }
            console.log("Shareholder text data updated successfully.", textUpdateResult);

            // --- Prepare and Execute File Uploads ---
            const uploadPromises = [];
            const filesToUploadInfo = []; // To keep track of which file belongs to which upload promise/result

            // **Iterate over the LOCAL shareholders state to find files**
            shareholders.forEach((localSh, index) => {
                let shareholderIdForUpload = localSh.id; // Start with local ID (could be temp ID or DB ID)

                // If this was potentially a *new* shareholder (temp ID), find the corresponding
                // shareholder object in the backend response to get the *actual* database _id.
                if (!String(localSh.id).match(/^[0-9a-fA-F]{24}$/) && textUpdateResult?.shareholders?.[index]?._id) {
                    // Assuming order is preserved between local state and backend response for new items
                    shareholderIdForUpload = textUpdateResult.shareholders[index]._id;
                    console.log(`Mapped new shareholder at index ${index} (temp ID ${localSh.id}) to DB ID ${shareholderIdForUpload}`);
                } else if (!String(localSh.id).match(/^[0-9a-fA-F]{24}$/)) {
                    console.warn(`Could not map temp ID ${localSh.id} for shareholder at index ${index} to a DB ID from backend response. Upload might fail if backend requires DB ID.`);
                }

                if (!shareholderIdForUpload) {
                    console.error(`Critical: Could not determine an ID for shareholder at index ${index} for file uploads. Skipping files for this shareholder.`);
                    return; // Skip to next shareholder
                }


                ['passport', 'qid', 'proofOfAddress'].forEach(docKey => {
                    const fileState = localSh[docKey];

                    if (fileState?.file instanceof File && fileState?.status === 'uploaded') {
                        console.log(`Preparing upload for ${docKey}, Shareholder ID: ${shareholderIdForUpload}`);
                        const formData = new FormData();
                        formData.append('file', fileState.file, fileState.fileName);
                        formData.append('userId', userId);
                        formData.append('shareholderIdForDoc', shareholderIdForUpload);
                        formData.append('shareholderDocKey', docKey);

                        filesToUploadInfo.push({
                            originalIndex: index,
                            shareholderId: shareholderIdForUpload,
                            docKey: docKey,
                            fileName: fileState.fileName
                        });

                        uploadPromises.push(
                            fetch(`${SHAREHOLDER_API_PATH}/shareholders/uploadDoc`, {
                                method: 'POST',
                                headers: { /* Auth */ },
                                body: formData,
                            }).then(async (res) => {
                                const result = await res.json();
                                if (!res.ok || !result.success) {
                                    throw new Error(result.error || result.message || `Upload failed for ${docKey} (Shareholder ID ${shareholderIdForUpload})`);
                                }
                                return { ...result, originalIndex: index, docKey: docKey, shareholderId: shareholderIdForUpload };
                            }).catch(error => {
                                throw new Error(`Upload failed for ${docKey} (Shareholder ID ${shareholderIdForUpload}): ${error.message}`);
                            })
                        );
                    }
                });
            });

            // --- Process Upload Results ---
            let allUploadsSucceeded = true;
            let finalErrorMessages = [];
            let processedShareholders = textUpdateResult?.shareholders && Array.isArray(textUpdateResult.shareholders) ?
                JSON.parse(JSON.stringify(textUpdateResult.shareholders)) // Deep copy from backend result
                :
                JSON.parse(JSON.stringify(shareholders)); // Fallback deep copy from local state

            if (uploadPromises.length > 0) {
                console.log(`Waiting for ${uploadPromises.length} file upload(s)...`);
                const results = await Promise.allSettled(uploadPromises);
                console.log("File upload results (allSettled):", results);

                const fileStateUpdates = {}; // Store updates keyed by shareholderId-docKey

                results.forEach((result, i) => {
                    const uploadInfo = filesToUploadInfo[i];

                    if (result.status === 'rejected') {
                        allUploadsSucceeded = false;
                        const reason = result.reason?.message || 'Unknown upload error';
                        console.error(`Upload failed for Shareholder ${uploadInfo.shareholderId} - ${uploadInfo.docKey}: ${reason}`);
                        finalErrorMessages.push(`Shareholder ${uploadInfo.shareholderId} - ${uploadInfo.docKey} (${uploadInfo.fileName}): ${reason}`);
                        fileStateUpdates[`${uploadInfo.shareholderId}-${uploadInfo.docKey}`] = {
                            status: 'error',
                            error: reason,
                        };
                    } else if (result.status === 'fulfilled') {
                        const uploadResultData = result.value;
                        if (uploadResultData.success && uploadResultData.documentData) {
                            const backendDocData = uploadResultData.documentData;
                            fileStateUpdates[`${uploadInfo.shareholderId}-${uploadInfo.docKey}`] = {
                                file: null,
                                fileName: getFileNameFromPath(backendDocData.filePath) || uploadInfo.fileName,
                                status: 'uploaded',
                                error: '',
                                signedUrl: backendDocData.signedUrl,
                                verificationStatus: backendDocData.verificationStatus || 'SUBMITTED'
                            };
                        } else {
                            allUploadsSucceeded = false;
                            const reason = uploadResultData.error || uploadResultData.message || 'Upload successful but backend reported an issue.';
                            console.error(`Upload issue for Shareholder ${uploadInfo.shareholderId} - ${uploadInfo.docKey}: ${reason}`);
                            finalErrorMessages.push(`Shareholder ${uploadInfo.shareholderId} - ${uploadInfo.docKey} (${uploadInfo.fileName}): ${reason}`);
                            fileStateUpdates[`${uploadInfo.shareholderId}-${uploadInfo.docKey}`] = {
                                status: 'error',
                                error: reason,
                            };
                        }
                    }
                });

                if (Object.keys(fileStateUpdates).length > 0) {
                    processedShareholders = processedShareholders.map(sh => {
                        if (!sh || !sh._id) return sh;

                        let updatedSh = { ...sh };

                        ['passport', 'qid', 'proofOfAddress'].forEach(dk => {
                            const updateKey = `${sh._id.toString()}-${dk}`;
                            if (fileStateUpdates[updateKey]) {
                                const currentDocState = sh[dk] && typeof sh[dk] === 'object' ? sh[dk] : getDefaultShareholder().passport;
                                updatedSh[dk] = { ...currentDocState, ...fileStateUpdates[updateKey] };
                            }
                        });
                        return updatedSh;
                    });
                    setShareholders(processedShareholders);
                }
            } else {
                console.log("No new files required uploading.");
            }

            if (!allUploadsSucceeded) {
                const errorMsg = `Submission completed with file upload errors:\n${finalErrorMessages.join('\n')}`;
                setSubmitError(errorMsg);
            }

            console.log("File upload stage finished. Current shareholder state:", processedShareholders);

            // --- Initiate KYC for each shareholder ---
            if (processedShareholders && processedShareholders.length > 0) {
                console.log("Initiating KYC for shareholders...");
                const kycInitiateApiEndpoint = `${API_BASE_URL}/ops/invoiceFinancing/initiateShareholderKyc`;

                const kycPromises = processedShareholders.map((sh, index) => {
                    if (sh && sh._id && String(sh._id).match(/^[0-9a-fA-F]{24}$/) && sh.email) {

                        // --- START: API CALL COMMENTED OUT AS REQUESTED ---
                        /*
                        console.log(`Preparing KYC initiation for shareholder ID: ${sh._id} (User: ${userId})`);
                        return fetch(kycInitiateApiEndpoint, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json', }, // Add Auth headers if needed
                            body: JSON.stringify({ userId, shareholderId: sh._id }),
                        })
                            .then(async (kycInitResponse) => {
                                const kycInitResult = await kycInitResponse.json();
                                if (!kycInitResponse.ok || !kycInitResult.success) {
                                    throw new Error(`Failed to initiate KYC for ${sh.firstName || sh._id}: ${kycInitResult.message || kycInitResult.error || 'Unknown error'}`);
                                }
                                console.log(`Successfully initiated KYC for shareholder ${sh.firstName || sh._id}. Ref: ${kycInitResult.reference}`);
                                return { success: true, shareholderId: sh._id }; // Indicate success
                            })
                            .catch(kycError => {
                                // Catch fetch/parse errors or thrown errors from non-ok response
                                console.error(`Error calling KYC API for ${sh.firstName || sh._id}: ${kycError.message}`, kycError);
                                // Return an error object for Promise.allSettled
                                return { success: false, shareholderId: sh._id, error: kycError.message };
                            });
                        */
                        // --- END: API CALL COMMENTED OUT ---

                        // Return a resolved promise to allow the process to continue without erroring
                        console.log(`KYC initiation API call for shareholder ID ${sh._id} is commented out.`);
                        return Promise.resolve({ success: true, shareholderId: sh._id, message: 'KYC initiation skipped as per current configuration.' });

                    } else {
                        const missingInfoMsg = `Skipping KYC initiation for a shareholder due to missing data (ID: ${sh?._id}, Email: ${sh?.email}).`;
                        console.warn(missingInfoMsg, sh);
                        return Promise.resolve({ success: false, shareholderId: sh?._id, error: missingInfoMsg });
                    }
                });

                const kycResults = await Promise.allSettled(kycPromises);
                console.log("KYC initiation results (allSettled):", kycResults);

                kycResults.forEach(result => {
                    if (result.status === 'rejected') {
                        console.error("Unexpected rejection during KYC initiation:", result.reason);
                        finalErrorMessages.push(`Unexpected error during KYC initiation: ${result.reason?.message}`);
                    } else if (result.status === 'fulfilled' && result.value && !result.value.success) {
                        finalErrorMessages.push(result.value.error || `KYC initiation failed for Shareholder ${result.value.shareholderId}`);
                    }
                });
            }
            // --- End KYC Initiation ---

            // --- START: Update User's isBeneficialOwner Status ---
            if (isApplicantBeneficialOwner !== null) {
                const beneficialOwnerStatusPayload = isApplicantBeneficialOwner === 'yes' ? true : false;
                if (userId) {
                    console.log(`Attempting to update main user's (ID: ${userId}) 'isBeneficialOwner' status to: ${beneficialOwnerStatusPayload}`);
                    try {
                        const updateKycUrl = `${API_BASE_URL}/ops/invoiceFinancing/updateKyc`;
                        const kycUpdateApiPayload = {
                            userId: userId,
                            kyc: {
                                isBeneficialOwner: beneficialOwnerStatusPayload
                            }
                        };
                        const kycUpdateApiResponse = await fetch(updateKycUrl, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(kycUpdateApiPayload),
                        });

                        const kycUpdateApiResult = await kycUpdateApiResponse.json();

                        if (!kycUpdateApiResponse.ok || !kycUpdateApiResult.success) {
                            const errorMsg = kycUpdateApiResult.message || 'Failed to update your beneficial owner status.';
                            console.error("Error updating 'isBeneficialOwner' status:", errorMsg, kycUpdateApiResult);
                            finalErrorMessages.push(`Your beneficial owner status could not be saved: ${errorMsg}`);
                        } else {
                            console.log("'isBeneficialOwner' status updated successfully for the user.");
                        }
                    } catch (error) {
                        console.error("Network or other error while updating 'isBeneficialOwner' status:", error);
                        finalErrorMessages.push(`A network error occurred while saving your beneficial owner status: ${error.message}`);
                    }
                } else {
                    console.warn("Skipped updating 'isBeneficialOwner' status: userId is missing.");
                }
            }
            // --- END: Update User's isBeneficialOwner Status ---

            if (finalErrorMessages.length > 0) {
                setSubmitError(`Submission completed with errors:\n${finalErrorMessages.join('\n')}`);
            } else {
                setSubmitError(''); // Clear errors if all succeeded
                console.log("Shareholder data, files, and KYC initiation completed successfully!");
                if (onNext) onNext(processedShareholders);
            }

        } catch (error) {
            console.error('Error during shareholder submission process:', error);
            setSubmitError(error.message || 'An unexpected error occurred during submission.');
        } finally {
            setIsSubmitting(false);
        }
    };


    // --- Render Logic ---

    if (isLoading) {
        return <LoadingPopup />;
    }

    if (loadError) {
        return (
            <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
                <div className="p-4 border border-red-400 bg-red-50 text-red-700 rounded-md text-center">
                    <p><strong>Error Loading Data</strong></p>
                    <p>{loadError}</p>
                    {/* Optionally add a button to retry or start fresh */}
                </div>
            </div>
        );
    }

    // --- UI Handlers ---
    const toggleShareholderExpand = (shareholderId) => {
        setExpandedShareholders(prev => {
            const newState = {};
            // If the clicked shareholder is already expanded, collapse it.
            // Otherwise, expand only the clicked one and collapse all others.
            if (prev[shareholderId]) {
                newState[shareholderId] = false;
            } else {
                newState[shareholderId] = true;
            }
            return newState;
        });
    };

    const handleOpenDeleteModal = (shareholderId, index) => {
        const shareholder = shareholders.find(sh => sh.id === shareholderId);
        const name = shareholder ? `${shareholder.firstName} ${shareholder.lastName}`.trim() || `Shareholder ${index + 1}` : `Shareholder ${index + 1}`;
        setShareholderToDelete({ id: shareholderId, name });
        setShowDeleteModal(true);
    };

    const handleConfirmDelete = () => {
        if (shareholderToDelete) {
            // Call your existing removeShareholder function
            // This function should already be defined in your component
            setShareholders(prev => prev.filter(sh => sh.id !== shareholderToDelete.id));
            const newExpanded = { ...expandedShareholders };
            delete newExpanded[shareholderToDelete.id];
            setExpandedShareholders(newExpanded);
        }
        setShowDeleteModal(false);
        setShareholderToDelete(null);
    };

    const handleOpenDocumentViewModal = (shareholderIndex, docKey) => {
        const shareholder = shareholders[shareholderIndex];
        if (!shareholder) return;
        const fileState = shareholder[docKey];
        if (!fileState) return;

        let urlToView = '';
        let isBlob = false;
        if (fileState.file instanceof File) {
            urlToView = URL.createObjectURL(fileState.file);
            isBlob = true;
        } else if (fileState.signedUrl) {
            urlToView = fileState.signedUrl;
        }

        if (urlToView) {
            setDocumentModalData({
                fileName: fileState.fileName,
                signedUrl: urlToView,
                docKey: docKey,
                shareholderIndexForDoc: shareholderIndex,
                isLocalBlob: isBlob,
            });
            setIsDocumentModalOpen(true);
        } else {
            alert('No document available to view.');
        }
    };

    const handleCloseDocumentViewModal = () => {
        if (documentModalData.isLocalBlob && documentModalData.signedUrl) {
            URL.revokeObjectURL(documentModalData.signedUrl);
        }
        setIsDocumentModalOpen(false);
        setDocumentModalData({ fileName: '', signedUrl: '', docKey: '', shareholderIndexForDoc: -1, isLocalBlob: false });
    };

    const handleReplaceFromModal = (shIndex, dKey) => {
        handleCloseDocumentViewModal(); // Close modal first
        const shareholderId = shareholders[shIndex]?.id;
        if (!shareholderId) return;
        const refKey = `${shareholderId}-${dKey}`;
        const inputRef = shareholderFileInputRefs.current[refKey];
        if (inputRef && inputRef.current) {
            inputRef.current.click();
        } else {
            console.warn("Input ref not found for replace:", refKey);
        }
    };

    // Determine if the form fields and file uploads should be generally disabled
    // Example: Disable if KYC is already VERIFIED, REJECTED, etc., allowing only viewing
    const isFormDisabled = kycStatus !== "INITIATED" && kycStatus !== null; // Disable if status exists and is not INITIATED

    const commonFileProps = (index, docKey, shareholder) => ({
        setFileState: (dKey, updater) => handleShareholderFileUpdate(index, dKey, typeof updater === 'function' ? updater(shareholder[dKey]) : updater),
        onFileValidated: (dKey, file) => {
            const newUploadingState = { file: file, fileName: file.name, status: 'uploading', error: '', signedUrl: null, verificationStatus: null };
            handleShareholderFileUpdate(index, dKey, newUploadingState);
            setTimeout(() => { // Replace with actual API call
                const isSuccess = true; // Simulate success/failure
                if (isSuccess && file) {
                    const blobUrl = URL.createObjectURL(file);
                    handleShareholderFileUpdate(index, dKey, { ...newUploadingState, file: file, status: 'uploaded', signedUrl: blobUrl, verificationStatus: 'PENDING' });
                } else {
                    handleShareholderFileUpdate(index, dKey, { ...newUploadingState, file: null, status: 'error', error: 'Simulated upload failed.' });
                }
            }, 1000);
        },
        accept: ACCEPTED_FORMATS_STRING,
        disabled: isFormDisabled,
        inputRef: getFileRef(shareholder.id, docKey),
        onViewClick: () => handleOpenDocumentViewModal(index, docKey),
        MAX_FILE_SIZE_MB: MAX_FILE_SIZE_MB,
        ACCEPTED_FORMATS_DISPLAY: ACCEPTED_FORMATS_DISPLAY,
        LoadingSpinner: LoadingSpinner, // Pass components if not imported directly in ShareholderFileUploadField
        CheckCircleIcon: CheckCircleIcon,
        HeroUploadIcon: HeroUploadIcon,
        EyeIcon: EyeIcon,
        HeroArrowPathIcon: HeroArrowPathIcon,
    });

    return (
        <>
            {/* Conditionally render the full-page loader */}
            {isSubmitting && <UploadingPopup />} {/* Passing no fileName to use the generic message */}

            {/* === MAIN TWO-COLUMN CONTAINER === */}
            <div className="flex w-full min-h-screen bg-gray-50">

                {/* === LEFT SIDE (8/12) - THE FORM === */}
                <div className="w-full lg:w-8/12 p-4 md:p-8">
                    {/* Step Counter */}
                    <div className="text-right text-sm font-semibold text-gray-500 mb-4">Step 1 of 3</div>

                    {/* Main Form Container */}
                    <div className="rounded-lg">
                        {/* Header */}
                        <div className="px-4 sm:px-6 py-4 text-center justify-center border-b border-gray-200 rounded-t-lg">
                            <h1 className="text-xl sm:text-2xl font-bold text-gray-800">
                                Add Your Shareholders
                            </h1>
                            <p className="mt-1 text-sm text-gray-600">
                                Please add details for all beneficial owners. (Max {MAX_SHAREHOLDERS}).
                                {isFormDisabled && (
                                    <span className="block sm:inline ml-0 sm:ml-2 mt-1 sm:mt-0 text-sm font-medium text-orange-600">(Form is read-only as KYC status is {kycStatus})</span>
                                )}
                            </p>
                        </div>

                        {/* Form Body */}
                        <div className="p-3 sm:p-4 md:p-6">
                            <div className="mb-6 bg-white p-4 rounded-md shadow-sm border border-gray-200">
                                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-4">
                                    <h3 className="text-md font-medium text-gray-700 shrink-0 whitespace-nowrap">
                                        Are you a beneficial Owner of the Business?
                                        {(!isFormDisabled && !isLoading && mainUserData) && <span className="text-red-500 ml-1">*</span>}
                                    </h3>
                                    <div className="flex items-center space-x-4 sm:space-x-6 mt-2 sm:mt-0 flex-shrink-0">
                                        <label className="flex items-center space-x-2 cursor-pointer">
                                            <input
                                                type="radio" name="isBeneficialOwner" value="yes"
                                                checked={isApplicantBeneficialOwner === 'yes'}
                                                onChange={handleBeneficialOwnerChange}
                                                className="form-radio h-4 w-4 text-indigo-600"
                                                disabled={isFormDisabled || isLoading || !mainUserData}
                                            />
                                            <span className="text-sm text-gray-700">Yes</span>
                                        </label>
                                        <label className="flex items-center space-x-2 cursor-pointer">
                                            <input
                                                type="radio" name="isBeneficialOwner" value="no"
                                                checked={isApplicantBeneficialOwner === 'no'}
                                                onChange={handleBeneficialOwnerChange}
                                                className="form-radio h-4 w-4 text-indigo-600"
                                                disabled={isFormDisabled || isLoading || !mainUserData}
                                            />
                                            <span className="text-sm text-gray-700">No</span>
                                        </label>
                                    </div>
                                </div>
                                {isApplicantBeneficialOwner === null && !isFormDisabled && !isLoading && mainUserData && (
                                    <p className="text-xs text-yellow-600 mt-2 sm:text-left">Please select an option.</p>
                                )}
                            </div>

                            <form onSubmit={handleSubmit} noValidate>
                                <div className="space-y-5">
                                    {shareholders.map((shareholder, index) => (
                                        <div key={shareholder.id} className="bg-white rounded-md border border-gray-200 overflow-hidden shadow-sm">
                                            <div
                                                className="flex justify-between items-center p-3 hover:bg-gray-50 cursor-pointer"
                                                onClick={() => toggleShareholderExpand(shareholder.id)}
                                            >
                                                <h2 className="text-md font-medium text-gray-700">Shareholder {index + 1}</h2>
                                                <div className="flex items-center space-x-3">
                                                    {!isFormDisabled && shareholders.length > 1 && (
                                                        <button
                                                            type="button"
                                                            onClick={(e) => { e.stopPropagation(); handleOpenDeleteModal(shareholder.id, index); }}
                                                            className="text-gray-400 hover:text-red-500 p-1"
                                                            title="Remove Shareholder"
                                                        >
                                                            <HeroTrashIcon className="h-5 w-5" />
                                                        </button>
                                                    )}
                                                    {expandedShareholders[shareholder.id] ? <ChevronUpIcon className="h-5 w-5 text-gray-500" /> : <ChevronDownIcon className="h-5 w-5 text-gray-500" />}
                                                </div>
                                            </div>
                                            <div
                                                className={`transition-all duration-300 ease-in-out overflow-hidden ${expandedShareholders[shareholder.id] ? 'max-h-[2000px]' : 'max-h-0'}`}
                                            >
                                                <div className="p-3 md:p-4 border-t border-gray-200 bg-white space-y-4">
                                                    <div className="bg-[#eff7f7] p-3 md:p-4 rounded-md">
                                                        <h3 className="text-md font-semibold text-gray-700 mb-3">Personal Details</h3>
                                                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-3">
                                                            <div>
                                                                <label htmlFor={`firstName-${index}`} className="block text-xs font-medium text-gray-600 mb-0.5">First Name <span className="text-red-500">*</span></label>
                                                                <input type="text" id={`firstName-${index}`} name="firstName" value={shareholder.firstName} onChange={(e) => handleInputChange(index, 'firstName', e.target.value)} className="mt-1 block w-full border-gray-300 rounded-md shadow-sm py-1.5 px-3 text-sm focus:ring-indigo-500 focus:border-indigo-500 disabled:bg-gray-100" required disabled={isFormDisabled || (isApplicantBeneficialOwner === 'yes' && index === 0)} />
                                                            </div>
                                                            <div>
                                                                <label htmlFor={`middleName-${index}`} className="block text-xs font-medium text-gray-600 mb-0.5">Middle Name</label>
                                                                <input type="text" id={`middleName-${index}`} name="middleName" value={shareholder.middleName} onChange={(e) => handleInputChange(index, 'middleName', e.target.value)} className="mt-1 block w-full border-gray-300 rounded-md shadow-sm py-1.5 px-3 text-sm focus:ring-indigo-500 focus:border-indigo-500 disabled:bg-gray-100" disabled={isFormDisabled} />
                                                            </div>
                                                            <div>
                                                                <label htmlFor={`lastName-${index}`} className="block text-xs font-medium text-gray-600 mb-0.5">Last Name <span className="text-red-500">*</span></label>
                                                                <input type="text" id={`lastName-${index}`} name="lastName" value={shareholder.lastName} onChange={(e) => handleInputChange(index, 'lastName', e.target.value)} className="mt-1 block w-full border-gray-300 rounded-md shadow-sm py-1.5 px-3 text-sm focus:ring-indigo-500 focus:border-indigo-500 disabled:bg-gray-100" required disabled={isFormDisabled || (isApplicantBeneficialOwner === 'yes' && index === 0)} />
                                                            </div>
                                                            <div>
                                                                <label htmlFor={`email-${index}`} className="block text-xs font-medium text-gray-600 mb-0.5">Email Address <span className="text-red-500">*</span></label>
                                                                <input type="email" id={`email-${index}`} name="email" value={shareholder.email} onChange={(e) => handleInputChange(index, 'email', e.target.value)} className="mt-1 block w-full border-gray-300 rounded-md shadow-sm py-1.5 px-3 text-sm focus:ring-indigo-500 focus:border-indigo-500 disabled:bg-gray-100" required disabled={isFormDisabled || (isApplicantBeneficialOwner === 'yes' && index === 0)} />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="bg-[#eff7f7] p-3 md:p-4 rounded-md">
                                                        <h3 className="text-md font-semibold text-gray-700 mb-3">Address Details</h3>
                                                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-4 gap-y-3">
                                                            <div>
                                                                <label htmlFor={`zone-${index}`} className="block text-xs font-medium text-gray-600 mb-0.5">Zone</label>
                                                                <input type="text" id={`zone-${index}`} name="zone" value={shareholder.address.zone} onChange={(e) => handleInputChange(index, 'zone', e.target.value)} className="mt-1 block w-full border-gray-300 rounded-md shadow-sm py-1.5 px-3 text-sm disabled:bg-gray-100" disabled={isFormDisabled} />
                                                            </div>
                                                            <div>
                                                                <label htmlFor={`streetNo-${index}`} className="block text-xs font-medium text-gray-600 mb-0.5">Street No.</label>
                                                                <input type="text" id={`streetNo-${index}`} name="streetNo" value={shareholder.address.streetNo} onChange={(e) => handleInputChange(index, 'streetNo', e.target.value)} className="mt-1 block w-full border-gray-300 rounded-md shadow-sm py-1.5 px-3 text-sm disabled:bg-gray-100" disabled={isFormDisabled} />
                                                            </div>
                                                            <div>
                                                                <label htmlFor={`buildingNo-${index}`} className="block text-xs font-medium text-gray-600 mb-0.5">Building No.</label>
                                                                <input type="text" id={`buildingNo-${index}`} name="buildingNo" value={shareholder.address.buildingNo} onChange={(e) => handleInputChange(index, 'buildingNo', e.target.value)} className="mt-1 block w-full border-gray-300 rounded-md shadow-sm py-1.5 px-3 text-sm disabled:bg-gray-100" disabled={isFormDisabled} />
                                                            </div>
                                                            <div>
                                                                <label htmlFor={`floorNo-${index}`} className="block text-xs font-medium text-gray-600 mb-0.5">Floor No.</label>
                                                                <input type="text" id={`floorNo-${index}`} name="floorNo" value={shareholder.address.floorNo} onChange={(e) => handleInputChange(index, 'floorNo', e.target.value)} className="mt-1 block w-full border-gray-300 rounded-md shadow-sm py-1.5 px-3 text-sm disabled:bg-gray-100" disabled={isFormDisabled} />
                                                            </div>
                                                            <div className="sm:col-span-2 md:col-span-2">
                                                                <label htmlFor={`unitNo-${index}`} className="block text-xs font-medium text-gray-600 mb-0.5">Unit No. / Flat No.</label>
                                                                <input type="text" id={`unitNo-${index}`} name="unitNo" value={shareholder.address.unitNo} onChange={(e) => handleInputChange(index, 'unitNo', e.target.value)} className="mt-1 block w-full border-gray-300 rounded-md shadow-sm py-1.5 px-3 text-sm disabled:bg-gray-100" disabled={isFormDisabled} />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="bg-[#eff7f7] p-3 md:p-4 rounded-md">
                                                        <h3 className="text-md font-semibold text-gray-700 mb-3">Identity Proof</h3>
                                                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                                                            <ShareholderFileUploadField
                                                                label="Passport" description="(front and back)" docKey="passport"
                                                                fileState={shareholder.passport}
                                                                {...commonFileProps(index, 'passport', shareholder)}
                                                            />
                                                            <ShareholderFileUploadField
                                                                label="QID" description="(front and back)" docKey="qid"
                                                                fileState={shareholder.qid}
                                                                {...commonFileProps(index, 'qid', shareholder)}
                                                            />
                                                            <ShareholderFileUploadField
                                                                label="Proof of Address" description="(Utility bill/Bank Stmt < 3mo)" docKey="proofOfAddress"
                                                                fileState={shareholder.proofOfAddress}
                                                                {...commonFileProps(index, 'proofOfAddress', shareholder)}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                {!isFormDisabled && shareholders.length < MAX_SHAREHOLDERS && (
                                    <div className="mt-6 flex justify-center sm:justify-end">
                                        <button
                                            type="button" onClick={addShareholder}
                                            disabled={isSubmitting || isLoading}
                                            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                                                <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                                            </svg>
                                            Add Shareholder
                                        </button>
                                    </div>
                                )}
                                {submitError && (
                                    <div className="mt-6 bg-red-50 border border-red-300 text-red-700 px-4 py-3 rounded-md text-center" role="alert">
                                        <span className="block sm:inline whitespace-pre-wrap">{submitError}</span>
                                    </div>
                                )}
                            </form>
                        </div>

                        {/* Footer Buttons */}
                        <div className="px-4 md:px-6 py-4 border-t border-gray-200 rounded-b-lg flex flex-col sm:flex-row justify-center items-center space-y-3 sm:space-y-0 sm:space-x-3">
                            <button
                                type="button"
                                onClick={handleSubmit}
                                disabled={isSubmitting || isLoading || isFormDisabled}
                                className={`w-full sm:w-auto px-7 py-2 text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition duration-150 ease-in-out flex items-center justify-center
                                     ${isSubmitting || isLoading || isFormDisabled
                                        ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
                                        : 'bg-gray-800 hover:bg-gray-900 text-white focus:ring-gray-700'
                                    }`}
                            >
                                {isSubmitting && <LoadingSpinner />}
                                {isSubmitting ? 'Submitting...' : (isFormDisabled ? 'Saved' : 'Save and Continue')}
                            </button>
                        </div>
                    </div>

                    {/* Modals */}
                    <DeleteConfirmationModal
                        isOpen={showDeleteModal}
                        onClose={() => setShowDeleteModal(false)}
                        onConfirm={handleConfirmDelete}
                        shareholderName={shareholderToDelete?.name}
                    />
                    <ShareholderDocumentReviewModal
                        isOpen={isDocumentModalOpen}
                        onClose={handleCloseDocumentViewModal}
                        fileName={documentModalData.fileName}
                        signedUrl={documentModalData.signedUrl}
                        docKey={documentModalData.docKey}
                        shareholderIndexForDoc={documentModalData.shareholderIndexForDoc}
                        onReplace={!isFormDisabled ? handleReplaceFromModal : undefined}
                    />
                </div>

                {/* === RIGHT SIDE (4/12) - NEW INFORMATIONAL PANEL === */}
                <div className="hidden lg:flex w-4/12 bg-gradient-to-br from-[#E6F9F4] to-white p-10 flex-col items-center justify-center border-l">
                    <div className="max-w-md w-full">
                        <img src={require("../../../images/salor_telescope.png")} alt="Credit Line Application" className="w-48 h-auto mx-auto mb-6" />

                        <h2 className="text-2xl font-bold text-center text-[#003a39] mb-4">
                            Credit Line Application
                        </h2>

                        <p className="text-center text-gray-600 text-sm mb-8">
                            Unlock a pre-approved credit amount you can use anytime—no need to reapply for every invoice. Enjoy quick access to funds, improved cash flow, and the freedom to grow your business on your terms.
                        </p>

                        <div className="space-y-5 text-gray-700 text-left">
                            <div className="flex items-start">
                                <svg className="w-7 h-7 text-green-500 mr-3 flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                                <div>
                                    <h3 className="font-semibold text-gray-800">Add Shareholder details</h3>
                                    <p className="text-sm text-gray-600">
                                        Providing shareholder information helps us ensure transparency, assess business ownership, and comply with regulatory and lender requirements. It strengthens your credibility during verification and helps build trust with financial partners for faster funding approvals.
                                    </p>
                                </div>
                            </div>

                            <div className="flex items-start">
                                <svg className="w-7 h-7 text-green-500 mr-3 flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                                <div>
                                    <h3 className="font-semibold text-gray-800">Add Buyer details</h3>
                                    <p className="text-sm text-gray-600">
                                        Sharing your buyer details helps us assess the credibility of your invoices and connect you with the right lending partners. It ensures faster verification, accurate risk evaluation, and better funding offers tailored to your business transactions.
                                    </p>
                                </div>
                            </div>

                            <div className="flex items-start">
                                <svg className="w-7 h-7 text-green-500 mr-3 flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                                <div>
                                    <h3 className="font-semibold text-gray-800">eKYC</h3>
                                    <p className="text-sm text-gray-600">
                                        eKYC helps us verify your identity and business credentials quickly and securely. It's a mandatory step to comply with regulatory guidelines and ensures a smooth, trusted process for accessing funding through our platform.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default ShareholderAdditions;