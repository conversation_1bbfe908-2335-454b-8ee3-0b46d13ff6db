import React, { useState, useCallback, useRef, useEffect } from 'react';
import SharedCache from '../../../sharedCache'; // For User ID
import config from '../../../config.json'; // For API URL
// --- Import the API functions ---
import { getKycInfo, updateKyc } from '../../../api/kyc'; // Import relevant API functions
import * as XLSX from 'xlsx'; // Import xlsx library for Excel handling
import { shortenDocumentName } from '../../../components/utils';
import { sendBuyerInvitationEmail } from '../../../api/registration'; // Adjust path as necessary
// --- Import Heroicons ---
import {
    PlusIcon,
    ArrowUpTrayIcon,      // Upload
    ArrowDownTrayIcon,    // Download
    PencilSquareIcon,     // Edit
    TrashIcon,
    XMarkIcon,             // Close
    ArrowUpOnSquareStackIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import LoadingModal from '../../Reusable/Loading';

// --- Constants ---
const MIN_BUYERS_RECOMMENDED = 3;

// --- Default State for a new Buyer ---
const getDefaultBuyer = () => ({
    frontendId: Date.now() + Math.random(),
    _id: null,
    buyerName: '',
    registrationNumber: '', // Corresponds to CR Number
    contactPerson: '',
    contactPhone: '',
    contactEmail: '',
});

// --- Helper to extract filename ---
const getFileNameFromPath = (path) => {
    if (!path) return '';
    try {
        const decodedPath = decodeURIComponent(path);
        return decodedPath.split('/').pop() || '';
    } catch (e) {
        console.error("Error decoding or splitting path:", path, e);
        const parts = path.split('/');
        return parts.length > 0 ? parts[parts.length - 1] : '';
    }
};

const LoadingPopup = ({ fileName }) => {
    return <LoadingModal message='Please wait a moment while we load your journey data.' />;
};

const SavingModal = ({ fileName }) => {
    return <LoadingModal />;
};


// --- Main AddBuyers Component ---
const AddBuyers = ({ onNext, onBack }) => {
    const [buyers, setBuyers] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [loadError, setLoadError] = useState('');
    const [isSaving, setIsSaving] = useState(false);
    const [saveError, setSaveError] = useState('');
    const [kycStatus, setKycStatus] = useState(null);
    // Modal States
    const [isAddEditModalOpen, setIsAddEditModalOpen] = useState(false);
    const [isBulkUploadModalOpen, setIsBulkUploadModalOpen] = useState(false);
    const [editingBuyer, setEditingBuyer] = useState(null);
    const [bulkUploadError, setBulkUploadError] = useState('');
    const bulkFileInputRef = useRef(null);
    const [msmeUserEmail, setMsmeUserEmail] = useState('');
    const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
    // --- Fetch Initial Data ---
    useEffect(() => {
        let isMounted = true;
        const fetchData = async () => {
            setIsLoading(true);
            setLoadError('');
            setBuyers([]); // Clear previous state

            const user = SharedCache.get('user') || {};
            const userId = user._id || user.id || "";

            if (!userId) {
                setLoadError("User ID not found. Cannot load buyers.");
                setIsLoading(false);
                if (isMounted) setBuyers([]); // Initialize empty
                return;
            }

            if (user.email) {
                setMsmeUserEmail(user.email);
            } else {
                console.warn("MSME user email not found in SharedCache.");
                // Potentially set an error or fetch it if critical and not present
            }

            try {
                console.log(`Workspaceing KYC info for userId: ${userId} to load buyers`);
                const response = await getKycInfo(userId);

                if (!isMounted) return;

                if (response?.success && response?.user?.kyc) {
                    const existingBuyers = response.user.kyc.buyers || [];
                    console.log("Existing buyers from API:", existingBuyers);
                    if (response.user.kyc.verificationStatus) {
                        setKycStatus(response.user.kyc.verificationStatus);
                    } else {
                        setKycStatus(null); // Or a default value
                    }
                    setBuyers(existingBuyers.map(b => ({
                        ...getDefaultBuyer(),
                        _id: b._id,
                        buyerName: b.buyerName || '',
                        registrationNumber: b.registrationNumber || '',
                        contactPerson: b.contactPerson || '',
                        contactPhone: b.contactPhone || '',
                        contactEmail: b.contactEmail || '',
                    })));
                } else {
                    setLoadError("Could not retrieve existing buyer data.");
                    setBuyers([]);
                }
            } catch (error) {
                if (!isMounted) return;
                console.error("Error fetching buyer data:", error);
                setLoadError(`Error loading data: ${error.message}.`);
                setBuyers([]);
            } finally {
                if (isMounted) setIsLoading(false);
            }
        };

        fetchData();
        return () => { isMounted = false; };
    }, []);

    // --- Modal Controls ---
    const openAddModal = () => {
        setEditingBuyer(getDefaultBuyer());
        setIsAddEditModalOpen(true);
    };
    const openEditModal = (buyerToEdit) => {
        setEditingBuyer({ ...buyerToEdit });
        setIsAddEditModalOpen(true);
    };
    const openBulkUploadModal = () => {
        setBulkUploadError('');
        if (bulkFileInputRef.current) bulkFileInputRef.current.value = '';
        setIsBulkUploadModalOpen(true);
    };
    const closeModal = () => {
        setIsAddEditModalOpen(false);
        setIsBulkUploadModalOpen(false);
        setEditingBuyer(null);
        setBulkUploadError('');
    };

    // --- Add/Edit/Delete Handlers ---
    const handleModalInputChange = (e) => {
        const { name, value } = e.target;
        setEditingBuyer(prev => ({ ...prev, [name]: value }));
    };
    const handleSaveBuyer = (e) => {
        e.preventDefault();
        if (!editingBuyer?.buyerName?.trim()) {
            alert("Buyer Name is required.");
            return;
        }

        // Validate contact email (required field)
        if (!editingBuyer?.contactEmail?.trim()) {
            alert("Contact Email is required.");
            return;
        }

        // Optional: Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(editingBuyer.contactEmail.trim())) {
            alert("Please enter a valid email address.");
            return;
        }

        setBuyers(prev => {
            const existingIndex = prev.findIndex(b => (b._id && b._id === editingBuyer._id) || b.frontendId === editingBuyer.frontendId);
            if (existingIndex > -1) {
                const updatedBuyers = [...prev];
                updatedBuyers[existingIndex] = editingBuyer;
                return updatedBuyers;
            } else {
                return [...prev, editingBuyer];
            }
        });
        closeModal();
    };
    const handleDeleteBuyer = (idToDelete) => {
        if (window.confirm("Are you sure you want to delete this buyer?")) {
            setBuyers(prev => prev.filter(b => (b._id ? b._id !== idToDelete : b.frontendId !== idToDelete)));
        }
    };

    // --- Bulk Upload Handlers ---
    const downloadSampleTemplate = () => {
        const headers = ["Buyer Name", "CR Number", "Contact Person", "Contact Number", "Contact Email"];
        const sampleData = [{ "Buyer Name": "Example Buyer Inc.", "CR Number": "123456", "Contact Person": "John Doe", "Contact Number": "+974 1234 5678", "Contact Email": "<EMAIL>" }];
        const ws = XLSX.utils.json_to_sheet(sampleData, { header: headers, skipHeader: false });
        ws['!cols'] = [{ wch: 30 }, { wch: 15 }, { wch: 20 }, { wch: 20 }, { wch: 30 }];
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "Buyers Template");
        XLSX.writeFile(wb, "Buyers_Upload_Template.xlsx");
    };
    const handleBulkUpload = (event) => {
        const file = event.target.files?.[0];
        if (!file) return;
        setBulkUploadError('');
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = e.target.result;
                const workbook = XLSX.read(data, { type: 'array' });
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: ["buyerName", "registrationNumber", "contactPerson", "contactPhone", "contactEmail"], range: 1 });
                const newBuyers = [];
                const errors = [];
                jsonData.forEach((row, rowIndex) => {
                    // Check for required Buyer Name
                    if (!row.buyerName || !String(row.buyerName).trim()) {
                        errors.push(`Row ${rowIndex + 2}: Buyer Name is missing.`);
                        return;
                    }

                    // Check for required Contact Email
                    if (!row.contactEmail || !String(row.contactEmail).trim()) {
                        errors.push(`Row ${rowIndex + 2}: Contact Email is missing.`);
                        return;
                    }

                    // Validate email format
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(String(row.contactEmail).trim())) {
                        errors.push(`Row ${rowIndex + 2}: Invalid email format.`);
                        return;
                    }

                    newBuyers.push({
                        ...getDefaultBuyer(),
                        buyerName: String(row.buyerName || '').trim(),
                        registrationNumber: String(row.registrationNumber || '').trim(),
                        contactPerson: String(row.contactPerson || '').trim(),
                        contactPhone: String(row.contactPhone || '').trim(),
                        contactEmail: String(row.contactEmail || '').trim()
                    });
                });
                if (errors.length > 0) { setBulkUploadError(`Errors found:\n- ${errors.join('\n- ')}`); if (bulkFileInputRef.current) bulkFileInputRef.current.value = ''; return; }
                if (newBuyers.length === 0) { setBulkUploadError("No valid data found."); if (bulkFileInputRef.current) bulkFileInputRef.current.value = ''; return; }
                setBuyers(prev => [...prev, ...newBuyers]);
                closeModal();
                toast.success('Buyers added successfully.');

            } catch (error) { console.error("Excel parse error:", error); setBulkUploadError(`Failed to process: ${error.message}.`); if (bulkFileInputRef.current) bulkFileInputRef.current.value = ''; }
        };
        reader.onerror = (err) => { console.error("FileReader error:", err); setBulkUploadError("Could not read file."); };
        reader.readAsArrayBuffer(file);
    };

    const confirmAndSave = async () => {
        closeModal(); // Close the confirmation modal
        setIsConfirmationModalOpen(false);
        setIsAddEditModalOpen(false);
        setIsBulkUploadModalOpen(false);
        setIsSaving(true);
        const user = SharedCache.get('user') || {};
        const userId = user._id || user.id || "";
        const currentMsmeEmail = user.email || msmeUserEmail;

        if (!userId) { setSaveError("User session invalid."); setIsSaving(false); return; }
        if (!currentMsmeEmail) {
            setSaveError("MSME user email not found. Cannot send invitations.");
            setIsSaving(false);
            toast.error("Could not retrieve your email to send invitations.");
            console.warn("MSME email is missing, invitations will not be sent.");
        }

        const buyersPayload = buyers.map(b => ({
            ...(b._id && { _id: b._id }),
            buyerName: b.buyerName,
            registrationNumber: b.registrationNumber,
            contactPerson: b.contactPerson,
            contactPhone: b.contactPhone,
            contactEmail: b.contactEmail,
        }));

        const updatePayload = {
            userId: userId,
            kyc: { buyers: buyersPayload }
        };

        const API_BASE_URL = config.apiUrl || "YOUR_FALLBACK_API_URL";
        const API_ENDPOINT = `${API_BASE_URL}/ops/invoiceFinancing/updateKyc`;

        try {
            const response = await fetch(API_ENDPOINT, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updatePayload),
            });
            const result = await response.json();

            if (!response.ok || !result.success) {
                throw new Error(result?.message || "Failed to save buyers.");
            }

            toast.success("Buyers saved successfully!");

            const updatedBuyersFromBackend = result.user?.kyc?.buyers || [];
            setBuyers(updatedBuyersFromBackend.map(b => ({
                ...getDefaultBuyer(),
                _id: b._id,
                buyerName: b.buyerName || '',
                registrationNumber: b.registrationNumber || '',
                contactPerson: b.contactPerson || '',
                contactPhone: b.contactPhone || '',
                contactEmail: b.contactEmail || '',
            })));

            if (currentMsmeEmail && updatedBuyersFromBackend.length > 0) {
                // toast.info("Sending invitation emails to buyers...");
                let emailsSentSuccessfully = 0;
                let emailSendErrors = 0;

                for (const buyer of updatedBuyersFromBackend) {
                    if (buyer.contactEmail) {
                        // try {
                        //     await sendBuyerInvitationEmail(currentMsmeEmail, buyer.contactEmail);
                        //     console.log(`Invitation sent to ${buyer.contactEmail}`);
                        //     emailsSentSuccessfully++;
                        // } catch (emailError) {
                        //     console.error(`Failed to send invitation to ${buyer.contactEmail}:`, emailError);
                        //     toast.error(`Failed to send invitation to ${buyer.contactEmail}: ${emailError.message}`);
                        //     emailSendErrors++;
                        // }
                        console.log(buyer.contactEmail);
                    }
                }
                if (emailsSentSuccessfully > 0) {
                    toast.success(`${emailsSentSuccessfully} invitation(s) sent successfully.`);
                }
                if (emailSendErrors > 0) {
                    toast.warning(`${emailSendErrors} invitation(s) could not be sent. Check console for details.`);
                }

            } else if (!currentMsmeEmail) {
                toast.warn("Could not send invitations: MSME email is missing.");
            }

            if (onNext) onNext(result.user);

        } catch (error) {
            console.error("Error saving buyers or sending invitations:", error);
            setSaveError(`Failed to save: ${error.message}`);
            toast.error(`Save operation failed: ${error.message}`);
        } finally {
            setIsSaving(false);
        }
    };

    // --- Save All Buyers to Backend ---
    const handleSaveChanges = async () => {

        // if (buyers.length < 1) {
        //     setSaveError("Please add at least one buyer before proceeding.");
        //     toast.error("At least one buyer is required before continuing.");
        //     return;
        // }
        if (buyers.length === 0) {
            console.log("No buyers to save, proceeding to next step.");
            if (onNext) onNext(); // Assuming onNext doesn't require a user object here
            return;
        }

        // --- START: Add Duplicate Buyer Name & Email Check (existing logic) ---
        if (buyers.length > 1) {
            const seenCombinations = new Map();
            let duplicateFound = false;
            for (let i = 0; i < buyers.length; i++) {
                const buyer = buyers[i];
                const normalizedName = buyer.buyerName?.trim().toLowerCase();
                const normalizedEmail = buyer.contactEmail?.trim().toLowerCase();
                if (normalizedName && normalizedEmail) {
                    const combinationKey = `${normalizedName}|||${normalizedEmail}`;
                    if (seenCombinations.has(combinationKey)) {
                        duplicateFound = true;
                        const firstIndex = seenCombinations.get(combinationKey);
                        const errorMessage = `Duplicate entry: The combination of Buyer Name "${buyer.buyerName}" and Email "${buyer.contactEmail}" is used more than once (e.g., Buyer ${firstIndex + 1} and Buyer ${i + 1}). Please ensure each combination is unique.`;
                        setSaveError(errorMessage);
                        toast.error(errorMessage);
                        break;
                    } else {
                        seenCombinations.set(combinationKey, i);
                    }
                }
            }
            if (duplicateFound) {
                setIsSaving(false);
                return;
            }
        }
        // --- END: Add Duplicate Buyer Name & Email Check ---

        setSaveError('');
        // Add this condition here
        if (buyers.length < MIN_BUYERS_RECOMMENDED) {
            setIsConfirmationModalOpen(true); // Open the confirmation modal
            return; // Stop the save process for now
        }
        setIsSaving(true);
        const user = SharedCache.get('user') || {}; // Re-fetch or use state for msmeUserEmail
        const userId = user._id || user.id || "";
        const currentMsmeEmail = user.email || msmeUserEmail; // Use the email from current user session

        if (!userId) { setSaveError("User session invalid."); setIsSaving(false); return; }
        if (!currentMsmeEmail) {
            setSaveError("MSME user email not found. Cannot send invitations.");
            setIsSaving(false);
            toast.error("Could not retrieve your email to send invitations.");
            // Still proceed to save buyers if desired, but invitations will fail.
            // Or, you could prevent saving entirely if email is critical.
            // For now, we'll let it proceed to save but log a warning.
            console.warn("MSME email is missing, invitations will not be sent.");
        }


        const buyersPayload = buyers.map(b => ({
            ...(b._id && { _id: b._id }),
            buyerName: b.buyerName,
            registrationNumber: b.registrationNumber,
            contactPerson: b.contactPerson,
            contactPhone: b.contactPhone,
            contactEmail: b.contactEmail,
        }));

        const updatePayload = {
            userId: userId,
            kyc: { buyers: buyersPayload }
        };

        const API_BASE_URL = config.apiUrl || "YOUR_FALLBACK_API_URL";
        const API_ENDPOINT = `${API_BASE_URL}/ops/invoiceFinancing/updateKyc`;

        try {
            const response = await fetch(API_ENDPOINT, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updatePayload),
            });
            const result = await response.json();

            if (!response.ok || !result.success) {
                throw new Error(result?.message || "Failed to save buyers.");
            }

            toast.success("Buyers saved successfully!"); // Notify user of save success

            // Update local state with backend data
            const updatedBuyersFromBackend = result.user?.kyc?.buyers || [];
            setBuyers(updatedBuyersFromBackend.map(b => ({
                ...getDefaultBuyer(),
                _id: b._id,
                buyerName: b.buyerName || '',
                registrationNumber: b.registrationNumber || '',
                contactPerson: b.contactPerson || '',
                contactPhone: b.contactPhone || '',
                contactEmail: b.contactEmail || '',
            })));

            // --- START: Send Invitation Emails ---
            if (currentMsmeEmail && updatedBuyersFromBackend.length > 0) {
                // toast.info("Sending invitation emails to buyers...");
                let emailsSentSuccessfully = 0;
                let emailSendErrors = 0;

                for (const buyer of updatedBuyersFromBackend) {
                    if (buyer.contactEmail) {
                        // try {
                        //     // We only need msmeEmail and buyerEmail for the send-invitation API
                        //     // as the API itself looks up the buyer's name from the DB.
                        //     await sendBuyerInvitationEmail(currentMsmeEmail, buyer.contactEmail);
                        //     console.log(`Invitation sent to ${buyer.contactEmail}`);
                        //     // toast.success(`Invitation sent to ${buyer.contactEmail}`, { autoClose: 2000 }); // Can be too noisy
                        //     emailsSentSuccessfully++;
                        // } catch (emailError) {
                        //     console.error(`Failed to send invitation to ${buyer.contactEmail}:`, emailError);
                        //     toast.error(`Failed to send invitation to ${buyer.contactEmail}: ${emailError.message}`);
                        //     emailSendErrors++;
                        // }
                        console.log(buyer.contactEmail);
                    }
                }
                if (emailsSentSuccessfully > 0) {
                    toast.success(`${emailsSentSuccessfully} invitation(s) sent successfully.`);
                }
                if (emailSendErrors > 0) {
                    toast.warning(`${emailSendErrors} invitation(s) could not be sent. Check console for details.`);
                }

            } else if (!currentMsmeEmail) {
                toast.warn("Could not send invitations: MSME email is missing.");
            }
            // --- END: Send Invitation Emails ---

            if (onNext) onNext(result.user);

        } catch (error) {
            console.error("Error saving buyers or sending invitations:", error);
            setSaveError(`Failed to save: ${error.message}`);
            toast.error(`Save operation failed: ${error.message}`);
        } finally {
            setIsSaving(false);
        }
    };


    // --- Render Logic ---
    if (isLoading) {
        return <LoadingPopup />;
    }
    if (loadError) {
        return (
            <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
                <div className="p-4 border border-red-400 bg-red-50 text-red-700 rounded-md text-center">
                    <p><strong>Error Loading Data</strong></p>
                    <p>{loadError}</p>
                </div>
            </div>
        );
    }

    return (
        <>
            {/* All modals now live inside the main component fragment but outside the layout divs for proper stacking context. */}
            {isSaving && <SavingModal />}
            {isConfirmationModalOpen && (
                <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-sm relative text-center">
                        <button onClick={() => setIsConfirmationModalOpen(false)} className="absolute top-3 right-3 text-gray-400 hover:text-gray-600">
                            <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                        </button>
                        <img className="w-28 h-28 mx-auto mb-4" alt='Sailor' src={require("../../../images/salor_telescope.png")} />
                        <h3 className="text-lg font-semibold mb-2">Fewer Buyers Added</h3>
                        <p className="text-sm text-gray-600 mb-6">
                            You have added fewer than {MIN_BUYERS_RECOMMENDED} buyers. We recommend adding 3-5 or more for better financing options. Do you want to proceed?
                        </p>
                        <div className="flex justify-center space-x-4">
                            <button type="button" onClick={() => setIsConfirmationModalOpen(false)} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                Cancel
                            </button>
                            <button type="button" onClick={confirmAndSave} className="px-4 py-2 text-sm font-medium text-white bg-[#004141] rounded-md hover:bg-[#005a5a]">
                                Proceed Anyway
                            </button>
                        </div>
                    </div>
                </div>
            )}
            {isAddEditModalOpen && (
                <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-lg relative">
                        <button onClick={closeModal} className="absolute top-3 right-3 text-gray-400 hover:text-gray-600">
                            <XMarkIcon className="h-6 w-6" />
                        </button>
                        <h3 className="text-lg font-medium mb-4">{editingBuyer?._id ? 'Edit Buyer' : 'Add New Buyer'}</h3>
                        <form onSubmit={handleSaveBuyer}>
                            <div className="space-y-3">
                                <div>
                                    <label htmlFor="buyerName" className="block text-sm font-medium text-gray-700">Buyer Name <span className="text-red-500">*</span></label>
                                    <input type="text" name="buyerName" id="buyerName" value={editingBuyer?.buyerName || ''} onChange={handleModalInputChange} required className="mt-1 block w-full border-gray-300 rounded-md shadow-sm py-1.5 px-3 text-sm" />
                                </div>
                                <div>
                                    <label htmlFor="registrationNumber" className="block text-sm font-medium text-gray-700">CR Number</label>
                                    <input type="text" name="registrationNumber" id="registrationNumber" value={editingBuyer?.registrationNumber || ''} onChange={handleModalInputChange} className="mt-1 block w-full border-gray-300 rounded-md shadow-sm py-1.5 px-3 text-sm" />
                                </div>
                                <div>
                                    <label htmlFor="contactPerson" className="block text-sm font-medium text-gray-700">Contact Person</label>
                                    <input type="text" name="contactPerson" id="contactPerson" value={editingBuyer?.contactPerson || ''} onChange={handleModalInputChange} className="mt-1 block w-full border-gray-300 rounded-md shadow-sm py-1.5 px-3 text-sm" />
                                </div>
                                <div>
                                    <label htmlFor="contactPhone" className="block text-sm font-medium text-gray-700">Contact Number</label>
                                    <input type="tel" name="contactPhone" id="contactPhone" value={editingBuyer?.contactPhone || ''} onChange={handleModalInputChange} className="mt-1 block w-full border-gray-300 rounded-md shadow-sm py-1.5 px-3 text-sm" />
                                </div>
                                <div>
                                    <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700">Contact Email <span className="text-red-500">*</span></label>
                                    <input type="email" name="contactEmail" id="contactEmail" value={editingBuyer?.contactEmail || ''} onChange={handleModalInputChange} required className="mt-1 block w-full border-gray-300 rounded-md shadow-sm py-1.5 px-3 text-sm" />
                                </div>
                            </div>
                            <div className="mt-5 sm:mt-6 flex justify-end space-x-3">
                                <button type="button" onClick={closeModal} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">Cancel</button>
                                <button type="submit" className="px-4 py-2 text-sm font-medium text-white bg-[#004141] rounded-md hover:bg-[#005a5a]">Save Buyer</button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
            {isBulkUploadModalOpen && (
                <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-lg relative">
                        <button onClick={closeModal} className="absolute top-3 right-3 text-gray-400 hover:text-gray-600"><XMarkIcon className="h-6 w-6" /></button>
                        <h3 className="text-lg font-medium mb-4">Bulk Upload Buyers</h3>
                        <div className="space-y-4 text-sm text-gray-600">
                            <p>The file should have columns for: <strong>Buyer Name*</strong>, CR Number, Contact Person, Contact Number, and <strong>Contact Email*</strong>.</p>
                            {bulkUploadError && <p className="text-red-600 whitespace-pre-wrap">{bulkUploadError}</p>}
                            <div className="flex flex-col sm:flex-row justify-between items-center pt-4 gap-4">
                                <button type="button" onClick={downloadSampleTemplate} className="inline-flex items-center px-3 py-2 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50">
                                    <ArrowDownTrayIcon className="h-4 w-4 mr-2" /> Download Sample
                                </button>
                                <label htmlFor="bulk-upload-input" className="cursor-pointer inline-flex items-center px-3 py-2 border border-transparent text-xs font-medium rounded text-white bg-[#004141] hover:bg-[#005a5a]">
                                    <ArrowUpTrayIcon className="h-4 w-4 mr-2" /> Choose Excel File
                                </label>
                                <input id="bulk-upload-input" ref={bulkFileInputRef} type="file" className="hidden" accept=".xlsx, .xls" onChange={handleBulkUpload} />
                            </div>
                        </div>
                    </div>
                </div>
            )}

            <div className="flex w-full min-h-screen">
                {/* === LEFT SIDE (Content & Centering Wrapper) === */}
                <div className="w-full lg:w-8/12 flex flex-col items-center justify-center p-4 md:p-8 bg-gray-50">
                    <div className="w-full max-w-4xl">
                        <div className='rounded-lg overflow-hidden'>
                            <div className="px-6  py-4 border-gray-200">
                                <h1 className="text-2xl font-bold text-gray-800 text-center">
                                    Add Buyers
                                </h1>
                                <p className="mt-1 text-sm text-gray-600 text-center">
                                    Add 3-5 or more top buyers to whom you supply and would like to be approved for invoices.
                                </p>
                            </div>
                            <div className="p-4 bg-gray-200 rounded-lg sm:p-6 lg:p-8">
                                {buyers.length === 0 && !isLoading ? (
                                    <div className="text-center py-6">
                                        <img className="w-32 h-auto mx-auto mb-4" alt='Add Buyers' src={require("../../../images/sailor.png")} />
                                        <p className="text-lg text-gray-600 mb-5">
                                            No buyers added yet.
                                        </p>
                                        <div className="flex justify-center space-x-4">
                                            <button
                                                type="button"
                                                onClick={openAddModal}
                                                disabled={isSaving || isLoading || (kycStatus !== "INITIATED" && kycStatus !== null)}
                                                className="inline-flex items-center justify-center px-4 py-2 border shadow-sm text-sm font-medium rounded-md text-white bg-[#1b9c4e] hover:bg-[#168f46] disabled:bg-gray-400 transition"
                                            >
                                                <PlusIcon className="h-5 w-5 mr-2" /> Add New Buyer
                                            </button>
                                            <button
                                                type="button"
                                                disabled={isSaving || isLoading || (kycStatus !== "INITIATED" && kycStatus !== null)}
                                                onClick={openBulkUploadModal}
                                                className="inline-flex items-center justify-center px-4 py-2 border border-gray-400 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:text-gray-400 disabled:border-gray-300 transition"
                                            >
                                                <ArrowUpOnSquareStackIcon className="h-5 w-5 mr-2" /> Bulk Upload
                                            </button>
                                        </div>
                                    </div>
                                ) : (
                                    <>
                                        {buyers.length > 0 && (
                                            <div className="overflow-x-auto mb-6">
                                                <table className="min-w-full divide-y divide-gray-300">
                                                    <thead className="bg-gray-100">
                                                        <tr>
                                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase">Buyer Name</th>
                                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase">CR Number</th>
                                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase">Contact Person</th>
                                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase">Contact Email</th>
                                                            <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-600 uppercase">Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody className="bg-white divide-y divide-gray-200">
                                                        {buyers.map((buyer) => (
                                                            <tr key={buyer._id || buyer.frontendId}>
                                                                <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">{buyer.buyerName}</td>
                                                                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{buyer.registrationNumber || '-'}</td>
                                                                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{buyer.contactPerson || '-'}</td>
                                                                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{buyer.contactEmail}</td>
                                                                <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium space-x-4">
                                                                    {!(kycStatus !== "INITIATED" && kycStatus !== null) && (
                                                                        <>
                                                                            <button onClick={() => openEditModal(buyer)} className="text-indigo-600 hover:text-indigo-800" title="Edit"><PencilSquareIcon className="w-5 h-5" /></button>
                                                                            <button onClick={() => handleDeleteBuyer(buyer._id || buyer.frontendId)} className="text-red-600 hover:text-red-800" title="Delete"><TrashIcon className="w-5 h-5" /></button>
                                                                        </>
                                                                    )}
                                                                </td>
                                                            </tr>
                                                        ))}
                                                    </tbody>
                                                </table>
                                            </div>
                                        )}
                                    </>
                                )}
                            </div>
                        </div>
                        {/* Navigation buttons */}
                        <div className="mt-6 flex justify-center items-center gap-4">
                            {/* <button type="button" onClick={onBack} className="px-8 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold rounded-md shadow-sm">
                                Previous Step
                            </button> */}
                            <button
                                type="button"
                                onClick={handleSaveChanges}
                                disabled={isSaving || isLoading || (kycStatus !== "INITIATED" && kycStatus !== null)}
                                className={`px-10 py-3 text-base font-semibold rounded-md shadow-md text-white transition-colors ${isSaving || isLoading || (kycStatus !== "INITIATED" && kycStatus !== null) ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#003a39] hover:bg-[#005554]'}`}
                            >
                                {isSaving ? 'Saving...' : 'Save and Continue'}
                            </button>
                        </div>
                    </div>
                </div>

                {/* === RIGHT SIDE (4/12) - INFORMATIONAL PANEL === */}
                <div className="hidden lg:flex w-4/12 bg-gradient-to-br from-[#E6F9F4] to-white p-10 flex-col items-center justify-center border-l">
                    <div className="max-w-md w-full">
                        <img src={require("../../../images/salor_telescope.png")} alt="Credit Line Application" className="w-48 h-auto mx-auto mb-6" />
                        <h2 className="text-2xl font-bold text-center text-[#003a39] mb-4">
                            Credit Line Application
                        </h2>
                        <p className="text-center text-gray-600 text-sm mb-8">
                            Unlock a pre-approved credit amount you can use anytime—no need to reapply for every invoice. Enjoy quick access to funds, improved cash flow, and the freedom to grow your business on your terms.
                        </p>
                        <div className="space-y-5 text-gray-700 text-left">
                            <div className="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-7 h-7 text-green-500 mr-3 flex-shrink-0 mt-1">
                                    <path fillRule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clipRule="evenodd" />
                                </svg>
                                <div>
                                    <h3 className="font-semibold text-gray-800">Add Shareholder details</h3>
                                    <p className="text-sm text-gray-600">
                                        Providing shareholder information helps us ensure transparency, assess business ownership, and comply with regulatory and lender requirements. It strengthens your credibility during verification and helps build trust with financial partners for faster funding approvals.
                                    </p>
                                </div>
                            </div>
                            <div className="flex items-start">
                                <svg className="w-7 h-7 text-green-500 mr-3 flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                                <div>
                                    <h3 className="font-semibold text-gray-800">Add Buyer details</h3>
                                    <p className="text-sm text-gray-600">
                                        Sharing your buyer details helps us assess the credibility of your invoices and connect you with the right lending partners. It ensures faster verification, accurate risk evaluation, and better funding offers tailored to your business transactions.
                                    </p>
                                </div>
                            </div>
                            <div className="flex items-start">
                                <svg className="w-7 h-7 text-green-500 mr-3 flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                                <div>
                                    <h3 className="font-semibold text-gray-800">eKYC</h3>
                                    <p className="text-sm text-gray-600">
                                        eKYC helps us verify your identity and business credentials quickly and securely. It's a mandatory step to comply with regulatory guidelines and ensures a smooth, trusted process for accessing funding through our platform.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default AddBuyers;