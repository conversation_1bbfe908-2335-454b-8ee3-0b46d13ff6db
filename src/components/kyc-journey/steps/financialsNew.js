import React, { useState, useCallback, useRef, useEffect, } from 'react';
import { useHistory } from 'react-router-dom';
import SharedCache from '../../../sharedCache';
// Import API functions (make sure paths are correct)
import { getKycInfo, updateKyc, uploadKycDocument } from '../../../api/kyc'; // Keep getKycInfo and updateKyc
import axios from 'axios'; // Import axios for direct API call
import config from "../../../config.json"; // Import config for API URL
import { shortenDocumentName } from '../../../components/utils';
import { ArrowPathIcon, EyeIcon } from '@heroicons/react/24/outline';
import LoadingModal from '../../Reusable/Loading';
// --- Constants ---
const MAX_FILE_SIZE_MB = 20;
const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;
const ACCEPTED_FORMATS = [
    'application/pdf',
    'application/vnd.ms-excel', // .xls
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/msword', // .doc
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'text/csv', // .csv
];
const ACCEPTED_FORMATS_STRING = '.pdf, .jpg, jpeg, .png';
const ACCEPTED_FORMATS_DISPLAY = 'PDF, JPEG, PNG, JPG';

const fieldConfigs = {
    bankStatement: { label: "Business Bank Statement *", description: "(Last 6 months)", isRequired: false, apiDocType: 'bankStatement' },
    auditedFinancialReport: { label: "Audited Financial Report", description: "(Not older than 12 months)", isRequired: false, apiDocType: 'auditedFinancialReport' },
    cashFlowLedger: { label: "Statement of Account", description: "(Last 12 months)", isRequired: false, apiDocType: 'cashFlowLedger' },
    commercialCreditReport: { label: "Qatar Credit Bureau Report *", description: "(Last 12 months)", isRequired: false, apiDocType: 'commercialCreditReport' },
    articleOfAssociation: { label: "Article of Association", description: "(Last 12 months)", isRequired: false, apiDocType: 'articleOfAssociation' },
};


// --- Helper Icons --- (Keep as is)
const UploadIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-1 inline-block text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 16.5V9.75m0 0l3 3m-3-3l-3 3M6.75 19.5a4.5 4.5 0 01-1.41-8.775 5.25 5.25 0 0110.33-2.33 4.5 4.5 0 013.75 8.355H6.75z" />
    </svg>
);
const CheckIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1 inline-block text-green-500" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
    </svg>
);


const ClockIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
);

// Icon for Rejected (X Circle - Solid)
const XCircleIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
    </svg>
);

// Icon for Submitted (Info Circle - Solid)
const InformationCircleIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
    </svg>
);

// Icon for Verified (Check Circle - Solid)
const CheckCircleIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20" >
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
    </svg>
);

// --- START: UI Components and Icons to ADD/REPLACE from BusinessDocuments.js ---

// Uploading Popup (from BusinessDocuments.js)
const UploadingPopup = ({ fileName }) => {
    return (
        <LoadingModal
            message={
                `We are uploading your file${fileName ? ` (${shortenDocumentName(fileName, 15)})` : ''} and preparing it for your review. This may take a moment—longer documents take more time to process. Thanks for your patience!`
            }
        />
    );
};


const LoadingPopup = ({ fileName }) => {
    return <LoadingModal message='Please wait a moment while we load your journey data.' />;
};

// CloseIcon for Modal (from BusinessDocuments.js)
const CloseIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
    </svg>
);

// ArrowPathIcon for Modal's Replace button (from Heroicons, as used in BusinessDocuments.js)
// Ensure you have ArrowPathIcon imported from '@heroicons/react/24/outline' at the top of FinancialDocuments.js
// If not, add: import { ArrowPathIcon } from '@heroicons/react/24/outline';

// VerifyIcon for Modal (from BusinessDocuments.js)
const VerifyIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
    </svg>
);

// DocumentReviewModal (from BusinessDocuments.js)
const DocumentReviewModal = ({
    isOpen,
    onClose,
    docKey, // Keep docKey for context if needed by onReplace/onVerify
    fileName,
    signedUrl,
    extractedData, // This will be specific to Bank Statement in FinancialDocuments
    onReplace,
    onVerify, // This might be "Confirm" or "Use Data" for bank statements
    displayLabels
}) => {
    if (!isOpen) return null;

    const handleReplace = () => {
        if (onReplace) {
            onReplace(docKey); // Pass docKey to identify which file to replace
        }
    };

    const handleVerify = () => {
        if (onVerify) {
            // For FinancialDocuments, extractedData is only for bank statements.
            // The onVerify for other docs might just be a confirm/close action.
            onVerify(docKey, extractedData);
        }
    };

    const dataEntries = extractedData ? Object.entries(extractedData) : [];

    return (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[9998] p-4" onClick={onClose}>
            <div className="bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[90vh] flex flex-col" onClick={(e) => e.stopPropagation()}>
                <div className="flex justify-between items-center p-4 border-b border-gray-200">
                    <h2 className="text-xl font-semibold text-gray-800">Review Document: {shortenDocumentName(fileName, 15)}</h2>
                    <button onClick={onClose} className="text-gray-500 hover:text-gray-700 p-1">
                        <CloseIcon />
                    </button>
                </div>
                <div className="flex-grow flex md:flex-row flex-col overflow-hidden">
                    <div className="md:w-2/3 w-full p-1 bg-gray-100 md:border-r border-gray-200 overflow-auto">
                        {signedUrl ? (
                            <iframe
                                src={signedUrl}
                                title={fileName || 'Document Preview'}
                                className="w-full h-full min-h-[300px] md:min-h-[500px]"
                                frameBorder="0"
                            />
                        ) : (
                            <div className="flex items-center justify-center h-full">
                                <p className="text-gray-500">Document preview not available.</p>
                            </div>
                        )}
                    </div>
                    <div className="md:w-1/3 w-full p-4 overflow-auto bg-gray-50">
                        <h3 className="text-base font-semibold mb-3 text-gray-700 border-b pb-2">Extracted Details</h3>
                        {(docKey === 'bankStatement' && extractedData && Object.keys(extractedData).length > 0) ? (
                            <div className="space-y-1 text-xs">
                                <div className="flex justify-between py-1.5 border-b border-gray-200">
                                    <span className="font-medium text-gray-500 w-2/5">File:</span>
                                    <span className="text-gray-800 text-right w-3/5 truncate" title={fileName}>
                                        {fileName ? shortenDocumentName(fileName, 15) : 'N/A'}
                                    </span>
                                </div>
                                {['iban', 'account_number', 'bank_address'].map((key) => (
                                    <div key={key} className="flex justify-between py-1.5 border-b border-gray-200">
                                        <span className="font-medium text-gray-500 w-2/5">
                                            {displayLabels?.[key] || key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:
                                        </span>
                                        <span className="text-gray-800 text-right w-3/5 break-words">
                                            {String(extractedData?.[key] || '-') || '-'}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <p className="text-gray-500 italic mt-2 text-sm">
                                {docKey === 'bankStatement' ? 'No details extracted or data is not available for this document.' : 'Details are not extracted for this document yet.'}
                            </p>
                        )}
                    </div>
                </div>
                <div className="p-4 border-t border-gray-200 flex justify-end space-x-3 bg-gray-100">
                    <button
                        onClick={onClose}
                        className="px-4 py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-200 transition-colors"
                    >
                        Close
                    </button>
                    <button
                        onClick={handleReplace}
                        className="px-6 py-2 text-sm text-white bg-[#214D4B] rounded-md hover:bg-[#1A3C3A] transition-colors flex items-center justify-center"
                    >
                        <ArrowPathIcon className="h-5 w-5 mr-2" /> Replace
                    </button>
                    {/* Only show Verify/Confirm for bank statements if data was extracted, or always show for other docs */}
                    {(docKey === 'bankStatement' && extractedData && Object.keys(extractedData).length > 0) || docKey !== 'bankStatement' ? (
                        <button
                            onClick={handleVerify}
                            className="px-6 py-2 text-sm text-white bg-[#5CB85C] rounded-md hover:bg-[#4CAF50] transition-colors flex items-center justify-center ml-4"
                        >
                            <VerifyIcon /> <span className="ml-2">{docKey === 'bankStatement' && extractedData ? 'Verify' : 'Confirm Upload'}</span>
                        </button>
                    ) : null}
                </div>
            </div>
        </div>
    );
};

// LoadingSpinner for FileUploadField (from BusinessDocuments.js)
const LoadingSpinner = () => (
    <svg className="animate-spin h-5 w-5 mr-1 inline-block text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
);

// REPLACE the existing UploadIcon in FinancialDocuments.js with this one from BusinessDocuments.js for consistency
// const UploadIcon = () => ( ... from FinancialDocuments.js - remove this )
// Add this new UploadIcon:
const UploadIconFromBizDocs = () => ( // Renamed to avoid conflict if you still have the old one temporarily
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1 inline-block text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
    </svg>
);

// --- END: UI Components and Icons to ADD/REPLACE ---

// --- Helper Function --- (Keep as is)
const getFileNameFromPath = (path) => {
    if (!path) return '';
    try {
        const decodedPath = decodeURIComponent(path);
        return decodedPath.split('/').pop() || '';
    } catch (e) {
        console.error("Error decoding or splitting path:", path, e);
        const parts = path.split('/');
        return parts.length > 0 ? parts[parts.length - 1] : '';
    }
};

const FileUploadField = (props) => {
    const {
        label,
        description, // You were already passing this
        docKey,
        isRequired,
        fileState = {}, // Default to empty object
        setFileState,
        onFileValidated, // This is the prop your FinancialDocuments.js uses
        accept,
        inputRef,
        disabled,
        // NEW prop to handle triggering the modal for viewing
        onViewClick,
    } = props;

    const handleFileChange = (event) => {
        const file = event.target.files?.[0];
        if (!file) return;

        // Basic validation (already in your FinancialDocuments FileUploadField, kept here)
        let error = '';
        // Ensure these constants are accessible here, either passed as props or defined globally/module-level
        // For this example, assuming they are defined at the top of FinancialDocuments.js
        if (file.size > MAX_FILE_SIZE_BYTES) { // MAX_FILE_SIZE_BYTES should be defined
            error = `File exceeds maximum size of ${MAX_FILE_SIZE_MB} MB.`;
        } else {
            const extension = file.name.split('.').pop()?.toLowerCase();
            const allowedExtensions = accept.split(',').map(ext => ext.trim().substring(1).toLowerCase());
            // Use ACCEPTED_FORMATS if more precise, or stick to extension check. 
            // BusinessDocuments uses a combined check.
            if (!ACCEPTED_FORMATS.includes(file.type) && (!extension || !allowedExtensions.includes(extension))) {
                error = `Invalid file format. Accepted: ${ACCEPTED_FORMATS_DISPLAY}`;
            }
        }

        if (error) {
            // Use setFileState if available (from FinancialDocuments logic), otherwise onFileValidated could also set error status
            if (setFileState) {
                setFileState(docKey, (prevState) => ({
                    ...prevState,
                    file: null, fileName: file.name, // Keep fileName for context on error
                    status: 'error', error: error,
                    signedUrl: prevState?.signedUrl, // Preserve old URL if replacing and new fails validation
                    extractedData: prevState?.extractedData
                }));
            }
        } else {
            if (onFileValidated) {
                onFileValidated(docKey, file); // Notify parent (FinancialDocuments) for upload trigger
            }
        }

        if (inputRef.current) {
            inputRef.current.value = '';
        }
    };

    const handleReplace = () => {
        if (!disabled) {
            inputRef.current?.click();
        }
    };

    const handleView = () => {
        if (onViewClick) {
            onViewClick(docKey); // Call parent's handler to open modal
        } else {
            // Fallback or error if onViewClick is not provided
            console.warn("onViewClick not provided to FileUploadField for docKey:", docKey);
            // You could add the old direct window.open(fileState?.signedUrl) here as a fallback
            // but the goal is to use the modal.
            if (fileState?.signedUrl) {
                window.open(fileState.signedUrl, '_blank', 'noopener,noreferrer');
            } else {
                alert("No document to view.");
            }
        }
    };

    // --- Helper Function for Status Badge with Icons ---
    const renderVerificationStatus = (status) => {
        // Don't render status if file is not uploaded yet
        if (fileState?.status !== 'uploaded') {
            return null;
        }

        let bgColor = 'bg-blue-100';    // Default blue for Submitted
        let textColor = 'text-blue-700'; // Default blue text
        let IconComponent = InformationCircleIcon; // Default icon
        let text = 'Submitted'; // Default text

        switch (status) {
            case 'PENDING':
                bgColor = 'bg-yellow-100';
                textColor = 'text-yellow-700';
                IconComponent = ClockIcon;
                text = 'Pending';
                break;
            case 'VERIFIED':
                bgColor = 'bg-green-100';
                textColor = 'text-green-700';
                IconComponent = CheckCircleIcon; // Use specific verified icon
                text = 'Verified';
                break;
            case 'REJECTED':
                bgColor = 'bg-red-100';
                textColor = 'text-red-700';
                IconComponent = XCircleIcon;
                text = 'Rejected';
                break;
            case 'SKIPPED':
                bgColor = 'bg-gray-100'; // Neutral gray for skipped
                textColor = 'text-gray-700';
                IconComponent = InformationCircleIcon; // Info icon for skipped
                text = 'Skipped';
                break;
            case 'SUBMITTED':
            default:
                // Use defaults defined above (Blue)
                break;
        }

        return (
            <div className="z-10">
                <span className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium ${bgColor} ${textColor}`}>
                    <IconComponent className="h-4 w-4" />
                    {text}
                </span>
            </div>
        );

    };
    // --- End Helper ---




    return (
        <div className="relative border border-gray-200 rounded-lg p-3 bg-white shadow-sm flex flex-col justify-between w-full max-w-full overflow-hidden">
            <div className="flex-1">
                <div className="flex flex-wrap items-center justify-between mb-1 gap-2">
                    <label className="text-sm font-medium text-gray-700">
                        {label} {isRequired && <span className="text-red-500">*</span>}
                    </label>
                    {fileState?.status === 'uploaded' && (
                        <span className="text-sm text-green-600 font-semibold flex items-center shrink-0">
                            <CheckIcon className="h-4 w-4 mr-1" /> Uploaded
                        </span>
                    )}
                </div>

                {fileState?.status === 'uploading' ? (
                    <div className="text-sm text-blue-600 font-semibold flex flex-col items-center text-center gap-1">
                        <LoadingSpinner />
                        <span>Uploading...</span>
                        {fileState.fileName && (
                            <span className="text-xs text-gray-500 truncate max-w-full break-all">
                                {shortenDocumentName(fileState.fileName, 15)}
                            </span>
                        )}
                    </div>
                ) : fileState?.status === 'uploaded' ? (
                    <div>
                        <div className="bg-[#eff7f7] border-2 border-dashed border-gray-300 rounded-md p-3 text-center overflow-hidden">
                            <span
                                className="text-sm font-medium text-gray-700 truncate break-words block"
                                title={fileState.fileName}
                            >
                                {fileState.fileName ? shortenDocumentName(fileState.fileName, 15) : 'File uploaded'}
                            </span>
                        </div>
                        {fileState.error && (
                            <p className="mt-1 text-xs text-orange-500 text-center break-words">
                                Note: {fileState.error}
                            </p>
                        )}
                    </div>
                ) : (
                    <div>
                        <button
                            type="button"
                            onClick={() => !disabled && inputRef.current?.click()}
                            className={`bg-[#eff7f7] w-full flex flex-col items-center justify-center px-3 py-2 border-2 border-dashed rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#004141] group
              ${disabled ? 'border-gray-300 bg-gray-100 text-gray-400 cursor-not-allowed'
                                    : fileState?.error ? 'border-red-400 text-red-600 hover:bg-red-50'
                                        : 'border-gray-300 text-gray-500 hover:border-gray-400 hover:bg-gray-100'}`}
                            disabled={disabled}
                        >
                            <div className="flex items-center space-x-2">
                                <UploadIcon />
                                <span>Upload</span>
                            </div>
                            <p className="text-xs text-gray-500 text-center">Max filesize {MAX_FILE_SIZE_MB}MB</p>
                        </button>

                        <p className="mt-1 text-xs text-center text-gray-500">Formats: {ACCEPTED_FORMATS_DISPLAY}</p>
                        {fileState?.error && fileState.status === 'error' && (
                            <p className="mt-1 text-xs text-red-500 text-center break-words">{fileState.error}</p>
                        )}
                    </div>
                )}
            </div>

            {fileState?.status === 'uploaded' && (
                <div className="mt-3 flex flex-wrap items-center justify-between gap-y-2">
                    <div className="flex gap-4 flex-wrap">
                        {(fileState.signedUrl || fileState.file) && (
                            <button
                                type="button"
                                onClick={handleView}
                                className="flex items-center text-xs text-black hover:underline font-medium"
                            >
                                <EyeIcon className="h-4 w-4 mr-1" /> View
                            </button>
                        )}
                        {!disabled && (
                            <button
                                type="button"
                                onClick={handleReplace}
                                className="flex items-center text-xs text-black hover:underline font-medium"
                            >
                                <ArrowPathIcon className="h-4 w-4 mr-1" /> Replace
                            </button>
                        )}
                    </div>
                    <div className="shrink-0">{renderVerificationStatus(fileState?.verificationStatus)}</div>
                </div>
            )}

            <input
                ref={inputRef}
                type="file"
                className="hidden"
                onChange={handleFileChange}
                accept={accept}
                disabled={disabled}
            />
        </div>
    );


};


// --- Main Financial Documents Component ---
const FinancialDocuments = ({ onNext, onBack }) => {
    // Initial state for the files (no change)
    // REPLACE with this block
    // DYNAMICALLY build initial state from fieldConfigs to prevent errors
    const initialFileState = {};
    Object.keys(fieldConfigs).forEach(key => {
        initialFileState[key] = { file: null, fileName: '', status: 'empty', error: '', signedUrl: null, extractedData: null };
    });
    const history = useHistory(); //  <-- ADD THIS LINE

    const [files, setFiles] = useState(initialFileState);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitError, setSubmitError] = useState('');
    const [isLoading, setIsLoading] = useState(true);
    const [loadError, setLoadError] = useState('');
    const [bankDetails, setBankDetails] = useState({ accountNumber: '', ifscCode: '' });
    const [businessAddress, setBusinessAddress] = useState({
        businessAddressLine1: '',
        businessAddressLine2: '',
        businessCity: '',
        businessCountry: ''
    });
    const [isUploadingGlobal, setIsUploadingGlobal] = useState(false); // For the UploadingPopup
    const [currentUploadingFileLabel, setCurrentUploadingFileLabel] = useState(''); // For UploadingPopup
    const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false); // For DocumentReviewModal
    const [modalData, setModalData] = useState({ // For DocumentReviewModal
        docKey: null,
        fileName: '',
        signedUrl: null,
        extractedData: null, // Will be specific to bank statements here
        verificationStatus: 'SUBMITTED',
    });
    // Add this at the top of your FinancialDocuments component
    const [stage, setStage] = useState(1);

    const displayLabels = {
        'account_number': 'Account Number',
        'iban': 'IBAN', // Assuming 'iban' is the key from bank statement extraction
        // Add other relevant keys if your bank statement extraction provides more
    };
    const [businessAddressVerification, setBusinessAddressVerification] = useState({});
    const [kycStatus, setKycStatus] = useState(null);
    const inputRefs = useRef({});
    Object.keys(files).forEach(key => {
        inputRefs.current[key] = inputRefs.current[key] ?? React.createRef();
    });
    const isUploading = Object.values(files).some(fileState => fileState.status === 'uploading');

   const handleStage1Submit = (e) => {
        e.preventDefault();
        // Remove the validation block that checks if all docs are uploaded
        // const requiredDocs = ['auditedFinancialReport', 'cashFlowLedger', 'commercialCreditReport', 'articleOfAssociation'];
        // const allDocsUploaded = requiredDocs.every(key => files[key]?.status === 'uploaded');

        // if (allDocsUploaded) { // Remove this if condition
            setSubmitError('');
            setStage(2); // Always move to stage 2
        // } else { // Remove this else block
        //     setSubmitError('Please upload all four required documents to proceed.');
        // }
    };
    const handleStage2Submit = async (e) => {
        e.preventDefault();
        if (files.bankStatement?.status !== 'uploaded' || !bankDetails.accountNumber || !bankDetails.ifscCode) {
            setSubmitError('Please upload a bank statement and confirm the account details.');
            return;
        }
        // All validation passed, call the main submit handler
        await handleSubmit();
    };

    // Callback to update the state for a specific file field (modified slightly)
    const setFileState = useCallback((key, state) => {
        // Use functional update to prevent state inconsistencies
        setFiles(prev => ({
            ...prev,
            [key]: typeof state === 'function' ? state(prev[key]) : state
        }));
        // Clear general submission error when user interacts
        setSubmitError('');
    }, []); // Removed formErrors dependency as it's not used here

    // --- Function to call updateKyc API for Bank Details (Keep as is, but ensure it updates bankDetails state) ---
    const callUpdateKycBankDetails = (extractedData) => {
        const accountNumber = extractedData?.account_number;
        const ifscCode = extractedData?.iban;
        const address = extractedData?.bank_address; // Assuming 'bank_address' is the key

        // Update local state with extracted details
        if (accountNumber) setBankDetails(prev => ({ ...prev, accountNumber }));
        if (ifscCode) setBankDetails(prev => ({ ...prev, ifscCode }));

        // Simple logic to parse address - adjust if format is different
        if (address) {
            const addressParts = address.split(/, | /); // Split by comma or space
            setBusinessAddress(prev => ({
                ...prev,
                businessAddressLine1: addressParts[0] || '',
                businessCity: addressParts[addressParts.length - 2] || '',
                businessCountry: addressParts[addressParts.length - 1] || ''
            }));
        }
        console.log("Local state updated with extracted bank details.");
    };

    // --- NEW: Generic Document Upload Function ---
    const uploadDocument = async (docKey, file) => {
        const userId = SharedCache.get("user")?._id || SharedCache.get("user")?.id;
        if (!userId) {
            setFileState(docKey, { status: 'error', error: 'User ID not found.', file: null, fileName: file.name });
            return;
        }
        const fieldConfig = fieldConfigs[docKey]; // Get config to access label
        setCurrentUploadingFileLabel(fieldConfig?.label || docKey);
        setIsUploadingGlobal(true);

        const formData = new FormData();
        formData.append('file', file);
        formData.append('userId', userId);
        formData.append('documentType', docKey); // Use the dynamic docKey

        const uploadUrl = `${config.apiUrl}/ops/invoiceFinancing/uploadKycDocument`;
        const token = SharedCache.get("token") || "placeholdertoken";
        console.log(`--- Axios Upload: Calling ${uploadUrl} for ${docKey}`);

        try {
            const response = await axios.post(uploadUrl, formData, { headers: { 'x-auth-token': token } });
            console.log(`--- Axios Upload: Success for ${docKey}:`, response.data);

            const responseData = response.data;
            if (!responseData || !responseData.success) {
                throw new Error(responseData.message || responseData.error || 'Upload failed at backend');
            }

            const newSignedUrl = responseData?.signedUrl || null;
            const newFilePath = responseData?.filePath || '';
            const newFileName = newFilePath ? getFileNameFromPath(newFilePath) : file.name;
            // Extract data only if it's the bank statement
            const realExtractedData = (docKey === 'bankStatement' && responseData?.extractedFields) ? responseData.extractedFields : null;
            const verificationStatus = responseData?.verificationStatus || files[docKey]?.verificationStatus || 'SUBMITTED';

            // Update file state to 'uploaded'
            setFileState(docKey, {
                file: null, // Clear the local file object
                fileName: newFileName,
                status: 'uploaded',
                error: '', // Clear any previous error
                signedUrl: newSignedUrl,
                extractedData: realExtractedData, // Store extracted data if applicable
            });

            // --- ADDED: Open the modal after successful upload ---
            setModalData({
                docKey: docKey,
                fileName: newFileName,
                signedUrl: newSignedUrl,
                extractedData: realExtractedData, // Only bank statements will have this for now
                verificationStatus: verificationStatus,
            });
            setIsDetailsModalOpen(true);
            // --- END ADDED ---

            // Trigger bank detail update IF it was the bank statement AND data was extracted
            // REPLACE with this line
            if (docKey === 'bankStatement' && realExtractedData) {
                callUpdateKycBankDetails(realExtractedData);
            }

        } catch (axiosError) {
            console.error(`--- Axios Upload: Error uploading ${docKey}:`, axiosError);
            const errorMessage = axiosError.response?.data?.error || axiosError.response?.data?.message || axiosError.message || 'Upload failed.';
            // Update file state to 'error'
            setFileState(docKey, {
                file: null, // Clear file object on error too
                fileName: file.name, // Keep the original filename for context
                status: 'error',
                error: errorMessage,
                signedUrl: null, // Clear signedUrl on error
                extractedData: null, // Clear extracted data
            });
        } finally {
            // --- ADDED: Hide global uploading popup ---
            setIsUploadingGlobal(false);
            setCurrentUploadingFileLabel('');
            // --- END ADDED ---
        }
    };

    const openModalForView = (docKeyToView) => {
        const fileToView = files[docKeyToView];
        if (fileToView && fileToView.status === 'uploaded') {
            setModalData({
                docKey: docKeyToView,
                fileName: fileToView.fileName,
                signedUrl: fileToView.signedUrl,
                extractedData: fileToView.extractedData, // This will be null for non-bank statements here
                verificationStatus: fileToView.verificationStatus,
            });
            setIsDetailsModalOpen(true);
        } else {
            alert("Document not ready for viewing or no viewable URL.");
        }
    };

    const handleReplaceInModal = (docKeyToReplace) => {
        setIsDetailsModalOpen(false); // Close modal
        if (inputRefs.current[docKeyToReplace] && inputRefs.current[docKeyToReplace].current) {
            inputRefs.current[docKeyToReplace].current.click(); // Trigger file input
        } else {
            console.error("Input ref not found for:", docKeyToReplace);
        }
    };

    const handleVerifyInModal = (docKeyFromModal, modalExtractedData) => {
        console.log("Confirming/Verifying document from modal:", docKeyFromModal);
        // If it's a bank statement and data was extracted and needs to be re-applied to form fields (if editable)
        if (docKeyFromModal === 'bankStatement' && modalExtractedData) {
            // Your existing callUpdateKycBankDetails already updates the bankDetails state.
            // You might not need to do much more here other than close the modal,
            // or set a specific verification status if your backend supports manual verification trigger.
            // For now, let's assume it updates the local bankDetails state if they were editable.
            // This part's functionality should NOT change based on your primary constraint.
            // If callUpdateKycBankDetails was already called post-upload, this might just be a "Confirm & Close".
            const userId = SharedCache.get("user")?._id || SharedCache.get("user")?.id;
            if (userId && modalExtractedData) {
                // Potentially re-call to ensure details are fresh, or rely on initial call.
                // callUpdateKycBankDetails(userId, modalExtractedData); 
            }
        }
        // For other documents, this might just be confirming the upload.
        setFileState(docKeyFromModal, prev => ({ ...prev, verificationStatus: 'SUBMITTED' })); // Example
        setIsDetailsModalOpen(false);
    };

    // --- NEW: Handler called by FileUploadField when validation passes ---
    const handleFileValidated = (docKey, file) => {
        console.log(`File validated for ${docKey}, triggering upload...`);
        // Set status to 'uploading' immediately
        setFileState(docKey, (prevState) => ({
            ...prevState, // Keep potential existing signedUrl/extractedData while uploading new version? Or clear? Let's clear.
            file: file, // Keep file object temporarily for potential viewing during upload
            fileName: file.name,
            status: 'uploading',
            error: '',
            signedUrl: null,
            extractedData: null,
        }));
        // Call the upload function
        uploadDocument(docKey, file);
    };

    // --- Load existing data (Ensure bankDetails are also loaded) ---
    useEffect(() => {
        let isMounted = true;
        const fetchUserData = async () => {
            const userId = SharedCache.get("user")?._id || SharedCache.get("user")?.id;
            if (!userId) { console.warn('User ID not found'); setIsLoading(false); return; }
            setIsLoading(true); setLoadError('');
            try {
                const response = await getKycInfo(userId);
                if (!isMounted || !response?.success || !response?.user) {
                    console.warn("Could not fetch user data or component unmounted.");
                    setLoadError("Could not retrieve current document status.");
                    setIsLoading(false);
                    return;
                };
                const userData = response.user;
                const updates = {};
                const initialBankDetails = { accountNumber: '', ifscCode: '' };
                if (userData.kyc && userData.kyc.verificationStatus) {
                    setKycStatus(userData.kyc.verificationStatus);
                } else {
                    setKycStatus(null); // Or some default value if not available
                }
                // Populate file states
                Object.keys(files).forEach(key => {
                    const docData = userData[key]; // Assumes backend returns keys matching 'files' state keys
                    if (docData?.filePath) {
                        updates[key] = {
                            file: null, fileName: getFileNameFromPath(docData.filePath), status: 'uploaded', error: '',
                            signedUrl: docData.signedUrl || null,
                            extractedData: key === 'bankStatement' ? (docData.extractedFields || null) : null,
                            verificationStatus: docData.verificationStatus || 'SUBMITTED' // Add this line
                        };
                    } else { updates[key] = { file: null, fileName: '', status: 'empty', error: '', extractedData: null, signedUrl: null }; }
                });
                setFiles(updates);

                // Populate bank details from KYC info
                initialBankDetails.accountNumber = userData.kyc?.incomeDetails?.accountNumber || '';
                initialBankDetails.ifscCode = userData.kyc?.incomeDetails?.ifscCode || '';
                setBankDetails(initialBankDetails);
                setBusinessAddress({
                    businessAddressLine1: userData.kyc?.businessDetails?.businessAddressLine1 || '',
                    businessAddressLine2: userData.kyc?.businessDetails?.businessAddressLine2 || '',
                    businessCity: userData.kyc?.businessDetails?.businessCity || '',
                    businessCountry: userData.kyc?.businessDetails?.businessCountry || ''
                });
                setBusinessAddressVerification(userData.kyc?.businessDetails?.businessAddressVerification);

            } catch (error) { if (isMounted) { console.error('Error fetching docs:', error); setLoadError(`Failed load: ${error.message || 'Unknown error'}`); } }
            finally { if (isMounted) setIsLoading(false); }
        };
        fetchUserData();
        return () => { isMounted = false; };
    }, []); // Empty dependency array means this runs once on mount


    // --- Form Validation (Checks files AND bank details) ---
    const validateForm = () => {
        let isValid = true;
        const currentFormErrors = {}; // Store specific errors

        // --- File Validation Loop ---
        Object.keys(fieldConfigs).forEach(key => {
            const config = fieldConfigs[key];
            const state = files[key]; // Access files state directly

            if (config.isRequired && state.status !== 'uploaded') {
                isValid = false;
                currentFormErrors[key] = 'This document is required.';
                // Ensure error state is set if validation fails on submit (e.g., user never uploaded)
                if (state.status !== 'error' && state.status !== 'uploading') {
                    setFileState(key, { ...state, status: 'error', error: 'This document is required.' });
                }
            } else if (state.status === 'error' && state.error) {
                // Allow proceeding if the error is just a non-blocking bank detail update failure
                // This assumes such errors are prefixed appropriately. Adjust if needed.
                if (!state.error.startsWith('Bank detail update failed')) {
                    isValid = false; // Consider it invalid only for critical file errors
                    currentFormErrors[key] = state.error;
                }
            } else if (state.status === 'uploading') {
                isValid = false; // Cannot submit while uploading
                currentFormErrors[key] = 'Upload in progress.';
            }
            // No need to clear errors here, let interaction handle that via setFileState callback
        });

        // --- NEW: Bank Details Validation ---
        // Check if bank account number is empty or only whitespace
        if (!bankDetails.accountNumber || !bankDetails.accountNumber.trim()) {
            isValid = false;
            currentFormErrors.accountNumber = 'Account Number is required.';
            // NOTE: You might want to add state/logic here to visually show this error near the input field.
            // Example: setBankDetailsErrors(prev => ({...prev, accountNumber: 'Required'}));
        }

        // Check if IBAN (stored in ifscCode state) is empty or only whitespace
        if (!bankDetails.ifscCode || !bankDetails.ifscCode.trim()) {
            isValid = false;
            currentFormErrors.ifscCode = 'IBAN is required.';
            // NOTE: You might want to add state/logic here to visually show this error near the input field.
            // Example: setBankDetailsErrors(prev => ({...prev, ifscCode: 'Required'}));
        }

        if (!businessAddress.businessAddressLine1 || !businessAddress.businessAddressLine1.trim()) {
            isValid = false;
            currentFormErrors.businessAddressLine1 = 'Business Address Line 1 is required.';
        }
        if (!businessAddress.businessCity || !businessAddress.businessCity.trim()) {
            isValid = false;
            currentFormErrors.businessCity = 'Business City is required.';
        }
        if (!businessAddress.businessCountry || !businessAddress.businessCountry.trim()) {
            isValid = false;
            currentFormErrors.businessCountry = 'Business Country is required.';
        }
        // --- END: Bank Details Validation ---


        // Optional: Update a state variable holding all form errors if needed for detailed display elsewhere
        // setFormErrors(currentFormErrors);
        console.log("Validation Result:", isValid, "Errors:", currentFormErrors); // Log validation result for debugging
        return isValid;
    };

    // --- MODIFIED handleSubmit ---
    const handleSubmit = async () => {
        setSubmitError('');
        setIsSubmitting(true);

        const userId = SharedCache.get("user")?._id || SharedCache.get("user")?.id;
        if (!userId) {
            setSubmitError('User ID not found.');
            setIsSubmitting(false);
            return;
        }

        // Prepare the final payload with all details and the new application status
        const finalPayload = {
            id: userId,
            kyc: {
                incomeDetails: {
                    accountNumber: bankDetails.accountNumber,
                    ifscCode: bankDetails.ifscCode,
                },
                businessDetails: {
                    businessAddressLine1: businessAddress.businessAddressLine1,
                    businessAddressLine2: businessAddress.businessAddressLine2,
                    businessCity: businessAddress.businessCity,
                    businessCountry: businessAddress.businessCountry,
                },
                applicationStatus: {
                    registration: { status: "Submitted" },
                    creditEvaluation: { status: "Submitted" },
                    creditLine: { status: "Pending" }
                }
            }
        };

        console.log("Calling final updateKyc with payload:", JSON.stringify(finalPayload, null, 2));

        try {
            const updateResponse = await updateKyc(finalPayload);
            if (!updateResponse.success) {
                throw new Error(updateResponse.message || "Failed to save financial details.");
            }

            console.log("Successfully saved all financial details and updated status.");
            setStage(3); // Move to the success screen

        } catch (error) {
            console.error('Error during final submission:', error);
            setSubmitError(`An error occurred: ${error.message || 'Unknown error'}.`);
        } finally {
            setIsSubmitting(false);
        }
    };

    // Configuration for each file field (no change)

    // --- Render Logic --- (Loading/Error states remain the same)
    if (isLoading) {
        return <LoadingPopup />;
    }

    return (
        <div className="flex w-full min-h-screen">
            {/* === LEFT SIDE (8/12) - DYNAMIC STAGED CONTENT === */}
            <div className="w-full lg:w-8/12 p-4 md:p-8 flex flex-col">
                {/* Modals remain at the top level */}
                {isDetailsModalOpen && (
                    <DocumentReviewModal
                        isOpen={isDetailsModalOpen}
                        onClose={() => setIsDetailsModalOpen(false)}
                        docKey={modalData.docKey}
                        fileName={modalData.fileName}
                        signedUrl={modalData.signedUrl}
                        extractedData={modalData.extractedData}
                        onReplace={handleReplaceInModal}
                        onVerify={handleVerifyInModal}
                        displayLabels={displayLabels}
                    />
                )}

                {/* Main content switches based on stage */}
                <div className="flex-grow flex flex-col">

                    {/* STAGE 1: FOUR DOCUMENTS */}
                    {stage === 1 && (
                        <>
                            <div className="text-right text-sm font-semibold text-gray-500 mb-4">Step 1 of 2</div>
                            <div className="text-center">
                                <h1 className="text-3xl font-bold text-gray-800">Upload your Financial Documents</h1>
                                <p className="mt-2 text-md text-gray-600 max-w-2xl mx-auto">Please upload the relevant documents required for credit evaluation. Ensure all documents are clear and legible.</p>
                            </div>
                            <div className="bg-[#f0f0f0] p-4 sm:p-6 rounded-lg my-8">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                                    {['auditedFinancialReport', 'cashFlowLedger', 'commercialCreditReport', 'articleOfAssociation'].map((key) => {
                                        const config = fieldConfigs[key];
                                        return (
                                            <FileUploadField key={key} {...config} docKey={key} fileState={files[key]} setFileState={setFileState} onFileValidated={handleFileValidated} accept={ACCEPTED_FORMATS_STRING} inputRef={inputRefs.current[key]} disabled={false} onViewClick={openModalForView} />
                                        );
                                    })}
                                </div>
                            </div>
                            <div className="flex justify-center items-center pt-4">
                                {submitError && <p className="text-red-600 text-sm mr-4">{submitError}</p>}
                                <button onClick={handleStage1Submit} disabled={isUploading} className="px-10 py-3 text-base font-medium rounded-md shadow-sm bg-[#003a39] text-white hover:bg-gray-700 disabled:bg-gray-400">
                                    Save and Continue
                                </button>
                            </div>
                        </>
                    )}

                    {stage === 2 && (
                        <>
                            <div className="text-right text-sm font-semibold text-gray-500 mb-4">Step 2 of 2</div>

                            <div className="w-full max-w-2xl mx-auto">
                                <div className="bg-gray-50 border border-gray-200 rounded-xl p-6 sm:p-8 space-y-6">

                                    <div className="text-center">
                                        <h2 className="text-lg font-semibold text-gray-800">
                                            Bank Statement <span className="text-red-500">*</span>
                                        </h2>
                                        <p className="text-sm text-gray-500">Last 6 months statement</p>
                                    </div>
                                    <FileUploadField
                                        {...fieldConfigs.bankStatement}
                                        docKey="bankStatement"
                                        fileState={files.bankStatement}
                                        setFileState={setFileState}
                                        onFileValidated={handleFileValidated}
                                        accept={ACCEPTED_FORMATS_STRING}
                                        inputRef={inputRefs.current.bankStatement}
                                        disabled={false}
                                        onViewClick={openModalForView}
                                    />

                                    {files.bankStatement.status === 'uploaded' && (
                                        <>
                                            <hr className="border-gray-200" />
                                            <div>
                                                <h3 className="text-lg font-semibold text-gray-800">
                                                    Bank Statement*
                                                </h3>
                                                <p className="mt-1 text-sm text-gray-500">
                                                    Primary operational bank account details. This will be used for disbursal of loans and repayments. These may be auto-filled from your bank statements but can be edited. Edits will be saved when you click "Save and Continue".
                                                </p>
                                                <div className="mt-4 space-y-4">
                                                    <div>
                                                        <label htmlFor="accountNumber" className="block text-sm font-medium text-gray-700">Account Number *</label>
                                                        <input
                                                            type="text"
                                                            id="accountNumber"
                                                            value={bankDetails.accountNumber}
                                                            onChange={(e) => setBankDetails(prev => ({ ...prev, accountNumber: e.target.value }))}
                                                            className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm"
                                                            placeholder="Enter Account Number"
                                                        />
                                                    </div>
                                                    <div>
                                                        <label htmlFor="ifscCode" className="block text-sm font-medium text-gray-700">IBAN *</label>
                                                        <input
                                                            type="text"
                                                            id="ifscCode"
                                                            value={bankDetails.ifscCode}
                                                            onChange={(e) => setBankDetails(prev => ({ ...prev, ifscCode: e.target.value }))}
                                                            className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm"
                                                            placeholder="Enter IBAN"
                                                        />
                                                    </div>

                                                    {/* Business Address - Modified Layout */}
                                                    <div className="bg-white p-4 rounded-lg border">
                                                        <h4 className="text-sm font-medium text-gray-700 mb-3">Business Address</h4>

                                                        {/* Address Line 1 and Line 2 in one row */}
                                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                                            <div>
                                                                <label htmlFor="businessAddressLine1" className="block text-sm font-medium text-gray-700">Address Line 1 *</label>
                                                                <input
                                                                    type="text"
                                                                    id="businessAddressLine1"
                                                                    value={businessAddress.businessAddressLine1}
                                                                    onChange={(e) => setBusinessAddress(prev => ({ ...prev, businessAddressLine1: e.target.value }))}
                                                                    className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm"
                                                                    placeholder="Enter Address Line 1"
                                                                />
                                                            </div>
                                                            <div>
                                                                <label htmlFor="businessAddressLine2" className="block text-sm font-medium text-gray-700">Address Line 2</label>
                                                                <input
                                                                    type="text"
                                                                    id="businessAddressLine2"
                                                                    value={businessAddress.businessAddressLine2}
                                                                    onChange={(e) => setBusinessAddress(prev => ({ ...prev, businessAddressLine2: e.target.value }))}
                                                                    className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm"
                                                                    placeholder="Enter Address Line 2"
                                                                />
                                                            </div>
                                                        </div>

                                                        {/* City and Country in one row */}
                                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div>
                                                                <label htmlFor="businessCity" className="block text-sm font-medium text-gray-700">City *</label>
                                                                <input
                                                                    type="text"
                                                                    id="businessCity"
                                                                    value={businessAddress.businessCity}
                                                                    onChange={(e) => setBusinessAddress(prev => ({ ...prev, businessCity: e.target.value }))}
                                                                    className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm"
                                                                    placeholder="Enter City"
                                                                />
                                                            </div>
                                                            <div>
                                                                <label htmlFor="businessCountry" className="block text-sm font-medium text-gray-700">Country *</label>
                                                                <input
                                                                    type="text"
                                                                    id="businessCountry"
                                                                    value={businessAddress.businessCountry}
                                                                    onChange={(e) => setBusinessAddress(prev => ({ ...prev, businessCountry: e.target.value }))}
                                                                    className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm"
                                                                    placeholder="Enter Country"
                                                                />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </>
                                    )}
                                </div>

                                {/* --- Buttons Section --- */}
                                <div className="flex-grow"></div>
                                <div className="flex justify-center items-center pt-8">
                                    {submitError && <p className="text-red-600 text-sm mr-4">{submitError}</p>}
                                    <button
                                        onClick={handleStage2Submit}
                                        disabled={isUploading || isSubmitting}
                                        className="px-10 py-3 text-base font-medium rounded-md shadow-sm bg-[#003a39] text-white hover:bg-gray-700 disabled:bg-gray-400"
                                    >
                                        {isSubmitting ? 'Processing...' : 'Save and Continue'}
                                    </button>
                                </div>
                            </div>
                        </>
                    )}

                    {/* STAGE 3: SUCCESS MESSAGE */}
                    {/* STAGE 3: SUCCESS MESSAGE */}
                    {stage === 3 && (
                        <div className="flex-grow flex flex-col items-center justify-center text-center">
                            <img src={require("../../../images/hurray_woman.png")} alt="Submission Successful" className="w-64 h-auto mx-auto mb-8" />
                            <h1 className="text-4xl font-bold text-green-600 mb-4">Great!</h1>
                            <p className="text-lg text-gray-700 max-w-lg mx-auto">
                                You have successfully completed the Madad credit evaluation process. Our team will evaluate your documents and update you shortly.
                            </p>
                            <button
                                type="button"
                                onClick={() => history.push('/dashboard')}
                                className="mt-8 px-8 py-3 bg-[#003a39] text-white font-semibold rounded-md shadow-sm hover:bg-green-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150 ease-in-out"
                            >
                                Go to Dashboard
                            </button>
                        </div>
                    )}
                </div>
            </div>

            {/* === RIGHT SIDE (4/12) - STATIC GRAPHICAL PANEL === */}
            <div className="hidden lg:flex w-4/12 bg-gradient-to-br from-[#E6F9F4] to-white p-10 flex-col items-center justify-center border-l">
                <div className="max-w-md w-full">
                    <img src={require("../../../images/salor_telescope.png")} alt="Welcome Aboard" className="w-48 h-auto mx-auto mb-6" />

                    <h2 className="text-3xl font-bold text-center text-green-600 mb-4">Madad Credit Evaluation</h2>
                    <p className="text-center text-gray-600 text-sm mb-8">
                        Get a comprehensive credit evaluation that reflects your business's financial health—just like a credit score. Use it to build trust with lenders, secure better financing offers, and strengthen your credibility with buyers, suppliers, and partners.
                    </p>
                    <div className="space-y-4 text-gray-700">
                        <div className="flex items-start">
                            <svg className="w-6 h-6 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                            <span>Audited Financial Report</span>
                        </div>
                        <div className="flex items-start">
                            <svg className="w-6 h-6 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                            <span>Statement Of Account</span>
                        </div>
                        <div className="flex items-start">
                            <svg className="w-6 h-6 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                            <span>Qatar Credit Bureau Report</span>
                        </div>
                        <div className="flex items-start">
                            <svg className="w-6 h-6 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                            <span>Article of Association</span>
                        </div>
                        <div className="flex items-start">
                            <svg className="w-6 h-6 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                            <span>Business Bank Statement</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default FinancialDocuments;