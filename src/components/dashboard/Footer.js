// src/components/NewFooter.js
import React from 'react';
import logoFooter from '../../images/logo_footer.png';
import linkedinIcon from '../../images/linkedin.png';
import instagramIcon from '../../images/Instagram_icon.png';

const NewFooter = () => {
  return (
    <footer className="bg-[#004141] text-white text-sm leading-5 w-full">
      <div className="border-t border-gray-500 w-full mx-auto"></div>

      <div className="flex flex-col md:flex-row items-center justify-between py-6 w-full px-6 mx-auto">
        {/* Logo */}
        <img
          src={logoFooter}
          alt="logo_footer"
          style={{ width: '100px', height: '100px' }}
        />

        {/* Address section */}
        <div className="text-center mt-4 md:mt-0 text-base">
          <p className="mb-2 ">Madad FinTech.</p>
          <p>Address: 201-42, Kate Business Center, Al Bustan Building,<br />Al Sadd, Doha, Qatar</p>
        </div>

        {/* Social links */}
        <div className="flex gap-4 items-center justify-center mt-4 md:mt-0 mr-8">
          <a href="https://www.linkedin.com/company/madad-financial-technology/posts/?feedView=all" target="_blank" rel="noopener noreferrer">
            <img
              src={linkedinIcon}
              alt="linkedin"
              className="w-16 h-16"
            />
          </a>
          <a href="https://www.instagram.com/madad.fintech?igsh=MWV0d24xbXp0djFqNA%3D%3D&amp;utm_source=qr" target="_blank" rel="noopener noreferrer">
            <img
              src={instagramIcon}
              alt="instagram"
              className="w-10 h-10"
            />
          </a>
        </div>
      </div>

      <div className="border-t border-gray-500 w-11/12 mx-auto my-4 border-2"></div>

      <div className="text-center mt-8 text-[#ffffffcc] pb-24 text-base">
        <p>© <strong>All rights reserved by Madad Financial Technologies 2025</strong></p>
      </div>
    </footer>
  );
};

export default NewFooter;