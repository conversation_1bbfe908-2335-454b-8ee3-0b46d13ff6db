import React, { useState, useEffect, useMemo, useCallback } from 'react';
import axios from 'axios'; // Using axios as per reference, can be replaced with fetch
import config from "../../config.json"; // Adjust path if needed
import * as XLSX from 'xlsx'; // Import xlsx library
import { saveAs } from 'file-saver'; // Import file-saver library
import { ChevronDownIcon } from '@heroicons/react/24/outline';
import SharedCache from '../../sharedCache';
import LoadingModal from '../Reusable/Loading';

// --- Helper: Get User ID ---
// Adapting from the reference code's SharedCache logic
const getUserIdFromStorage = () => {
  try {
    // Attempt to retrieve from localStorage directly first
    const storedUserString = localStorage.getItem('user');
    if (storedUserString) {
      const storedUser = JSON.parse(storedUserString);
      // Ensure you return the ID, whether it's stored as _id or id
      return storedUser._id || storedUser.id || null;
    }
    // Fallback logic if needed (e.g., SharedCache)
  } catch (e) {
    console.error("Error retrieving user info from storage:", e);
    return null;
  }
  return null; // Default return if nothing found
};


// --- Helper Functions (Reused & Adapted) ---

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  try {
    const dateValue = typeof dateString === 'number' ? dateString : Date.parse(dateString);
    if (isNaN(dateValue)) return 'Invalid Date';
    const date = new Date(dateValue);
    return date.toLocaleDateString('en-CA'); // FormatfirstSending-MM-DD
  } catch (e) {
    console.error("Error formatting date:", dateString, e);
    return 'Invalid Date';
  }
};

// Helper to format date specifically for Excel (YYYY-MM-DD or leave as Date object)
const formatDateForExcel = (dateString) => {
  if (!dateString) return null; // Return null for empty cells
  try {
    const dateValue = typeof dateString === 'number' ? dateString : Date.parse(dateString);
    if (isNaN(dateValue)) return null;
    // Return as Date object - SheetJS handles this well with cellDates:true option if needed,
    // but often just writing the standard string format is fine too. Let's use Date object.
    return new Date(dateValue);
  } catch (e) {
    return null;
  }
};

// Helper function to safely parse currency strings to numbers for Excel export
const parseCurrencyForExcel = (value) => {
  if (value === null || value === undefined) return null; // Return null for empty cells
  if (typeof value === 'number') return value;
  if (typeof value === 'string') {
    const num = parseFloat(value.replace(/[^0-9.-]+/g, ""));
    return isNaN(num) ? null : num;
  }
  return null; // Fallback for other types
};


const formatCurrency = (value, currency = 'QAR') => {
  const numValue = parseCurrencyForExcel(value); // Use the parser helper
  if (numValue === null) return 'N/A';
  return `${currency} ${Number(numValue).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};

// Combined getStatusClass for Offer, Invoice, and EMI statuses
const getStatusClass = (status) => {
  status = status?.toUpperCase() || 'UNKNOWN';
  switch (status) {
    // Offer Statuses
    case 'PENDING': return 'bg-orange-100 text-orange-800'; // Offer Pending
    case 'ACCEPTED': // Merchant accepted offer
    case 'LOAN_CONTRACT_ACCEPTED': return 'bg-blue-100 text-blue-800';
    case 'READY_FOR_DISBURSAL': return 'bg-yellow-100 text-yellow-800';
    case 'INITIATED_FUND_TRANSFER': return 'bg-cyan-100 text-cyan-800';
    case 'LOAN_IN_PROGRESS': return 'bg-indigo-100 text-indigo-800';
    case 'PAID': return 'bg-green-100 text-green-800'; // Loan Paid
    case 'REJECTED': // Offer Rejected
    case 'LOAN_CANCELLED': return 'bg-red-100 text-red-800';
    case 'EXPIRED':
    case 'WRITTEN_OFF': return 'bg-gray-400 text-gray-800';
    case 'DEFAULTED': return 'bg-red-200 text-red-900';
    case 'OVERDUE': return 'bg-amber-100 text-amber-800'; // Loan Overdue

    // Invoice Statuses (as seen by merchant)
    case 'VERIFICATION_PENDING_ANCHOR':
    case 'VERIFICATION_PENDING_LENDER': return 'bg-gray-100 text-gray-500'; // Pending Verification
    case 'VERIFIED_ANCHOR':
    case 'ACCEPTED_LENDER': return 'bg-lime-100 text-lime-800'; // Verified/Accepted for offer
    case 'DISBURSED': return 'bg-teal-100 text-teal-800'; // Invoice Disbursed (synced with offer)
    case 'REJECTED_ANCHOR':
    case 'REJECTED_LENDER': return 'bg-pink-100 text-pink-800'; // Invoice Rejected
    case 'MORE_INFO_NEEDED_ANCHOR':
    case 'MORE_INFO_NEEDED_LENDER': return 'bg-yellow-100 text-yellow-600'; // More Info Needed

    // EMI Statuses
    // case 'PENDING': return 'bg-orange-100 text-orange-800'; // Already covered
    case 'PARTIAL': return 'bg-purple-100 text-purple-800';
    case 'WAIVED': return 'bg-gray-300 text-gray-700';
    // case 'PAID': return 'bg-green-100 text-green-800'; // Already covered
    // case 'OVERDUE': return 'bg-amber-100 text-amber-800'; // Already covered

    default: return 'bg-gray-200 text-gray-700'; // Default fallback
  }
};


// --- Main Component ---
export default function MerchantPaymentsDashboard() {
  const [allOffersData, setAllOffersData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [creditLineError, setCreditLineError] = useState(null);
  const [creditLine, setCreditLine] = useState({})
  const [acceptedLenderDetails, setAcceptedLenderDetails] = useState({})
  const [activeSection, setActiveSection] = useState('disbursals'); // 'disbursals' or 'repayments'
  const [showEmiModal, setShowEmiModal] = useState(false);
  const [selectedOfferForModal, setSelectedOfferForModal] = useState(null);
  const [merchantId, setMerchantId] = useState(null);
  const [feedbackMessage, setFeedbackMessage] = useState({ type: '', text: '' }); // State for feedback

  // Effect to load merchantId from storage
  const user = SharedCache.get("user")
  useEffect(() => {
    const userId = getUserIdFromStorage();
    if (userId) {
      setMerchantId(userId);
    } else {
      console.error("Merchant ID not found in storage. Cannot fetch data.");
      setError("Merchant ID not found. Please ensure you are logged in.");
      setLoading(false);
    }
  }, []);

  const fetchLenderDetails = async (lenderId, token) => {
    try {
      const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/lenders/${lenderId}`, {
        headers: { 'x-auth-token': token }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching lender details for ID ${lenderId}:`, error);
      return null;
    }
  };

  const fetchLender = async (lenderIdFromCreditLine) => { // Renamed param for clarity
    console.log(`MyBuyersPage: Attempting to fetch lender details for ID: ${lenderIdFromCreditLine}`);
    const lenderData = await fetchLenderDetails(lenderIdFromCreditLine, "");
    if (lenderData) {
      setAcceptedLenderDetails(lenderData);
      console.log("MyBuyersPage: Successfully fetched and set acceptedLenderDetails:", lenderData);
    } else {
      setAcceptedLenderDetails(null);
      console.log("MyBuyersPage: fetchLenderDetails returned null or empty data.");
    }
  };

  const fetchCreditLine = async () => {
    try {
      const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/fetchCreditLine/${merchantId}`);
      setCreditLine(response.data);
      if (response.data) {
        console.log("MyBuyersPage: creditLine exists.");
        if (response.data.lenderId) { // Add this check!
          console.log("MyBuyersPage: creditLine.lenderInfo is NOT present, but lenderId IS:", response.data.lenderId);
          fetchLender(response.data.lenderId); // If lenderInfo is missing but lenderId exists, proceed
        } else {
          console.log("MyBuyersPage: creditLine.lenderInfo and creditLine.lenderId are missing or null.");
          setAcceptedLenderDetails(null);
        }
      } else {
        console.log("MyBuyersPage: creditLine is null or undefined.");
        setAcceptedLenderDetails(null);
      }
    } catch (error) {
      console.error('Error fetching credit line:', error);
      setCreditLineError(error.response?.data?.message || 'Failed to fetch credit line.');
      setCreditLine(null);
    }
  };

  // Fetch Data Function using the new single endpoint
  const fetchData = useCallback(async () => {
    if (!merchantId) return; // Don't fetch if merchantId is not set

    console.log(`Workspaceing dashboard data for merchant: ${merchantId}`);
    setLoading(true);
    setError(null);
    setAllOffersData([]); // Clear previous data
    setFeedbackMessage({ type: '', text: '' }); // Clear feedback on fetch

    try {
      const response = await axios.get(
        // Ensure the API URL is correct for your setup
        `${config.apiUrl}/ops/invoiceFinancing/${merchantId}/dashboard-data`
      );

      if (response.data && response.data.success && Array.isArray(response.data.data)) {
        const processedData = response.data.data.map(item => ({
          ...item,
          clientId: item._id || Date.now() + Math.random() // Use actual _id if available
        }));
        setAllOffersData(processedData);
        console.log("Fetched Merchant Dashboard Data:", processedData);
      } else {
        console.warn("API response failure or data not an array:", response.data);
        setError(response.data?.message || "Failed to fetch data properly. API response format unexpected.");
        setAllOffersData([]);
      }
    } catch (err) {
      console.error('Error fetching merchant dashboard data:', err);
      let errorMsg = "An unexpected error occurred while fetching your data.";
      if (err.response) {
        errorMsg = `Error ${err.response.status}: ${err.response.data?.message || err.message}`;
      } else if (err.request) {
        errorMsg = "Could not connect to the server. Please check your network.";
      } else {
        errorMsg = err.message;
      }
      setError(errorMsg);
      setAllOffersData([]);
    } finally {
      setLoading(false);
    }
  }, [merchantId]); // Dependency: fetchData updates when merchantId changes


  // Effect to trigger fetch when merchantId is available
  useEffect(() => {
    if (merchantId) {
      fetchCreditLine();
      fetchData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [merchantId]); // Trigger fetch only when merchantId is set/changed

  // Effect to clear feedback message
  useEffect(() => {
    if (feedbackMessage.text) {
      const timer = setTimeout(() => setFeedbackMessage({ type: '', text: '' }), 5000);
      return () => clearTimeout(timer);
    }
  }, [feedbackMessage]);

  // --- Filtering and Sorting Logic ---

  // Memoized data for Disbursals section
  const filteredSortedDisbursalData = useMemo(() => {
    return allOffersData
      .filter(item =>
        item.disbursementInfo &&
        ['LOAN_IN_PROGRESS', 'PAID', 'OVERDUE', 'DEFAULTED', 'WRITTEN_OFF'].includes(item.status?.toUpperCase())
      )
      .sort((a, b) => {
        const dateA = a.disbursementInfo?.disbursedOn ? new Date(a.disbursementInfo.disbursedOn).getTime() : 0;
        const dateB = b.disbursementInfo?.disbursedOn ? new Date(b.disbursementInfo.disbursedOn).getTime() : 0;
        if (dateB !== dateA) return dateB - dateA; // Most recent disbursal date first
        return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(); // Secondary sort
      });
  }, [allOffersData]);

  // Memoized data for Repayments section (offers with EMIs)
  const filteredSortedRepaymentData = useMemo(() => {
    return allOffersData
      .filter(item =>
        ['READY_FOR_DISBURSAL', 'LOAN_IN_PROGRESS', 'PAID', 'OVERDUE', 'DEFAULTED', 'WRITTEN_OFF'].includes(item.status?.toUpperCase()) &&
        item.emiDetails && item.emiDetails.length > 0
      )
      .sort((a, b) => {
        // Sort by latest offer update time
        return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
      });
  }, [allOffersData]);

  // --- Download Handlers ---

  const handleDownloadDisbursals = useCallback(() => {
    if (!filteredSortedDisbursalData || filteredSortedDisbursalData.length === 0) {
      setFeedbackMessage({ type: 'error', text: 'No disbursal data available to download.' });
      return;
    }

    setFeedbackMessage({ type: 'info', text: 'Preparing disbursal data for download...' });

    try {
      const header = [
        "Offer ID",
        "Invoice #",
        "Invoice Date",
        "Invoice Amount",
        "Amount Disbursed",
        "Disbursal Date",
        "Loan Status",
        "Invoice Status",
        "Lender",
        "UTR",
        "Disbursal Uploaded On" // Added based on schema
      ];

      const dataRows = filteredSortedDisbursalData.map(item => [
        item._id ?? 'N/A',
        item.invoiceId?.invoiceNumber ?? 'N/A',
        formatDateForExcel(item.invoiceId?.invoiceDate), // Use Excel date helper
        parseCurrencyForExcel(item.invoiceId?.totalAmount), // Use number parser
        parseCurrencyForExcel(item.disbursementInfo?.disbursedAmount),
        formatDateForExcel(item.disbursementInfo?.disbursedOn),
        item.status ?? 'N/A',
        item.invoiceId?.status ?? 'N/A',
        item.lenderId?.businessName ?? `${item.lenderId?.firstName ?? ''} ${item.lenderId?.lastName ?? ''}`.trim() ?? 'N/A',
        item.disbursementInfo?.utr ?? 'N/A',
        formatDateForExcel(item.disbursementInfo?.uploadedOn)
      ]);

      const worksheet = XLSX.utils.aoa_to_sheet([header, ...dataRows]);

      // Adjust column widths (optional, based on typical content length)
      worksheet['!cols'] = [
        { wch: 25 }, // Offer ID
        { wch: 20 }, // Invoice #
        { wch: 12 }, // Invoice Date
        { wch: 15 }, // Invoice Amount
        { wch: 15 }, // Amount Disbursed
        { wch: 12 }, // Disbursal Date
        { wch: 20 }, // Loan Status
        { wch: 25 }, // Invoice Status
        { wch: 25 }, // Lender
        { wch: 25 }, // UTR
        { wch: 15 }  // Disbursal Uploaded On
      ];


      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Disbursal Report");

      // Generate buffer
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

      // Create Blob
      const dataBlob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8" });

      // Trigger download
      saveAs(dataBlob, `merchant_disbursal_report_${formatDate(new Date())}.xlsx`);

      setFeedbackMessage({ type: 'success', text: 'Disbursal report downloaded successfully.' });

    } catch (downloadError) {
      console.error("Error generating disbursal Excel:", downloadError);
      setFeedbackMessage({ type: 'error', text: 'Failed to generate disbursal report.' });
    }
  }, [filteredSortedDisbursalData]); // Depend on the filtered data


  const handleDownloadAllEmis = useCallback(() => {
    // Use allOffersData to get ALL EMIs, not just those currently filtered for display
    const offersWithEmis = allOffersData.filter(item => item.emiDetails && item.emiDetails.length > 0);

    if (offersWithEmis.length === 0) {
      setFeedbackMessage({ type: 'error', text: 'No EMI data available to download.' });
      return;
    }

    setFeedbackMessage({ type: 'info', text: 'Preparing EMI data for download...' });

    try {
      const header = [
        "Offer ID",
        "Invoice #",
        "Lender",
        "Loan Status",
        "EMI #",
        "Due Date",
        "Amount Due",
        "Principal",
        "Service Fee",
        "EMI Status",
        "Paid Date",
        "Amount Paid",
        "Penalty", // Added from schema
        // "Payment UTR" // Optional: Add if relevant for merchant
      ];

      // Flatten all EMIs from all relevant offers into a single array
      const allEmiRows = offersWithEmis.flatMap(offer => {
        // Skip if no emiDetails (already filtered, but double-check)
        if (!offer.emiDetails || offer.emiDetails.length === 0) {
          return [];
        }
        // Map each EMI detail to a row array
        return offer.emiDetails.map(emi => [
          offer._id ?? 'N/A', // Offer ID
          offer.invoiceId?.invoiceNumber ?? 'N/A',
          offer.lenderId?.businessName ?? `${offer.lenderId?.firstName ?? ''} ${offer.lenderId?.lastName ?? ''}`.trim() ?? 'Dummy Tech',
          offer.status ?? 'N/A', // Loan Status
          emi.emiNumber ?? null,
          formatDateForExcel(emi.rePaymentDate), // Use Excel date helper
          parseCurrencyForExcel(emi.rePaymentAmount), // Use number parser
          parseCurrencyForExcel(emi.principalRecovered),
          parseCurrencyForExcel(emi.interestAmount),
          emi.rePaymentStatus ?? 'N/A',
          formatDateForExcel(emi.rePaymentActualDate),
          parseCurrencyForExcel(emi.paidAmount),
          parseCurrencyForExcel(emi.penalty)
          // emi.paymentUTR ?? 'N/A' // Optional UTR
        ]);
      });

      const worksheet = XLSX.utils.aoa_to_sheet([header, ...allEmiRows]);

      // Adjust column widths (optional)
      worksheet['!cols'] = [
        { wch: 25 }, // Offer ID
        { wch: 20 }, // Invoice #
        { wch: 25 }, // Lender
        { wch: 20 }, // Loan Status
        { wch: 8 },  // EMI #
        { wch: 12 }, // Due Date
        { wch: 15 }, // Amount Due
        { wch: 15 }, // Principal
        { wch: 15 }, // Interest
        { wch: 15 }, // EMI Status
        { wch: 12 }, // Paid Date
        { wch: 15 }, // Amount Paid
        { wch: 15 }  // Penalty
        //  { wch: 25 }  // Payment UTR (if added)
      ];

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "EMI Report");

      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const dataBlob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8" });

      saveAs(dataBlob, `merchant_emi_report_${formatDate(new Date())}.xlsx`);

      setFeedbackMessage({ type: 'success', text: 'Full EMI report downloaded successfully.' });

    } catch (downloadError) {
      console.error("Error generating EMI Excel:", downloadError);
      setFeedbackMessage({ type: 'error', text: 'Failed to generate EMI report.' });
    }
  }, [allOffersData]); // Depend on the complete offers data


  // --- Modal Control ---
  const openEmiModal = (offer) => {
    setSelectedOfferForModal(JSON.parse(JSON.stringify(offer)));
    setShowEmiModal(true);
  };

  const closeEmiModal = () => {
    setShowEmiModal(false);
    setSelectedOfferForModal(null);
  };

  // Error State
  if (error) {
    return (
      <div className="p-6 bg-red-100 text-red-800 rounded-lg shadow-md max-w-lg mx-auto mt-10">
        <h2 className="text-xl font-semibold mb-3">Error Loading Data</h2>
        <p className="mb-4">{error}</p>
        <button
          onClick={fetchData}
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition duration-150 disabled:opacity-50"
          disabled={!merchantId || loading}
        >
          Retry
        </button>
      </div>
    );
  }

  if (loading) {
    return <LoadingModal />
  }

  // Main component render
  return (
    <div className="min-h-screen bg-gray-100">
      <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 pt-4 flex flex-col sm:flex-row justify-between items-center gap-3">
        <h1 className="text-xl font-bold text-gray-800 mb-3 sm:mb-0">Welcome, {user.firstName}</h1>
        <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
          <button className="bg-white text-gray-700 px-4 py-2 rounded-md shadow hover:bg-gray-100 transition-colors flex items-center justify-center text-sm font-medium w-full sm:w-auto">
            Export
            <ChevronDownIcon className="w-5 h-5 ml-2" />
          </button>
        </div>
      </div>
      {/* Feedback Area */}
      {feedbackMessage.text && (
        <div className={`p-3 mb-4 rounded-md text-sm shadow ${feedbackMessage.type === 'error' ? 'bg-red-100 text-red-700' : feedbackMessage.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-blue-100 text-blue-700'}`}>
          {feedbackMessage.text}
        </div>
      )}


      {/* Section Tabs */}
      <div className="mb-6 mt-4 flex space-x-2 border-b border-gray-300">
        <button
          className={`py-2 px-4 font-medium text-sm md:text-base focus:outline-none ${activeSection === 'disbursals' ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-500 hover:text-gray-700 hover:border-gray-400'}`}
          onClick={() => setActiveSection('disbursals')}
        >
          Invoice Loan Disbursals
        </button>
        <button
          className={`py-2 px-4 font-medium text-sm md:text-base focus:outline-none ${activeSection === 'repayments' ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-500 hover:text-gray-700 hover:border-gray-400'}`}
          onClick={() => setActiveSection('repayments')}
        >
          Invoice Loan Repayments
        </button>
      </div>

      {/* Content Area */}
      <div className="bg-white p-4 md:p-6 rounded-lg shadow-lg">

        {/* ========================= */}
        {/* === Disbursals Section === */}
        {/* ========================= */}
        {activeSection === 'disbursals' && (
          <div>
            {/* Header with Title and Download Button */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-700 mb-2 sm:mb-0">
                Disbursed Loans Overview
              </h2>
              <button
                onClick={handleDownloadDisbursals}
                className="w-full sm:w-auto px-4 py-2 bg-green-600 text-white rounded shadow hover:bg-green-700 transition duration-150 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={filteredSortedDisbursalData.length === 0}
              >
                Download Disbursal Report (.xlsx)
              </button>
            </div>

            <div className="overflow-x-auto mt-4">
              <table className="min-w-full divide-y divide-gray-200 border border-gray-200">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Invoice #</th>
                    <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Invoice Date</th>
                    <th className="px-4 py-3 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Invoice Amount</th>
                    <th className="px-4 py-3 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Amount Disbursed</th>
                    <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Disbursal Date</th>
                    <th className="px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Loan Status</th>
                    <th className="px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Invoice Status</th>
                    <th className="px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Contract</th>
                    <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Disbursal Info (UTR)</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredSortedDisbursalData.length > 0 ? filteredSortedDisbursalData.map((item) => (
                    <tr key={item.clientId} className="hover:bg-blue-50 text-sm">
                      <td className="px-4 py-4 whitespace-nowrap text-gray-700">{item.invoiceId?.invoiceNumber || 'N/A'}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-gray-500">{formatDate(item.invoiceId?.invoiceDate)}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-gray-900 font-semibold text-right">{formatCurrency(item.invoiceId?.totalAmount)}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-blue-700 font-bold text-right">{formatCurrency(item.disbursementInfo?.disbursedAmount)}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-gray-500 font-medium">{formatDate(item.disbursementInfo?.disbursedOn)}</td>
                      <td className="px-4 py-4 whitespace-nowrap text-center">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClass(item.status)}`}>
                          {item.status || 'N/A'}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-center">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClass(item.invoiceId?.status)}`}>
                          {item.invoiceId?.status || 'N/A'}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-center">
                        {item.invoiceContract?.signedUrl ? (
                          <a href={item.invoiceContract.signedUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline text-xs font-medium">
                            View
                          </a>
                        ) : (
                          <span className="text-xs text-gray-400">N/A</span>
                        )}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-xs text-gray-500" title={item.disbursementInfo?.utr || ''}>
                        UTR: {item.disbursementInfo?.utr || 'N/A'}
                      </td>
                    </tr>
                  )) : (
                    <tr><td colSpan="10" className="text-center py-5 text-gray-500">No disbursed loans found.</td></tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* ========================= */}
        {/* === Repayments Section === */}
        {/* ========================= */}
        {activeSection === 'repayments' && (
          <div>
            {/* Header with Title and Download Button */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-700 mb-2 sm:mb-0">
                Loan Repayment Schedule
              </h2>
              <button
                onClick={handleDownloadAllEmis}
                className="w-full sm:w-auto px-4 py-2 bg-green-600 text-white rounded shadow hover:bg-green-700 transition duration-150 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                // Disable if no offers with EMIs exist in the raw data
                disabled={!allOffersData.some(o => o.emiDetails && o.emiDetails.length > 0)}
              >
                Download Full EMI Report (.xlsx)
              </button>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 border border-gray-200">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Invoice # / Offer ID</th>
                    <th className="px-4 py-3 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Loan Amount</th>
                    <th className="px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Loan Status</th>
                    <th className="px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Total EMIs</th>
                    <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Next Due Date</th>
                    <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Last Updated</th>
                    <th className="px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">EMI Details</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredSortedRepaymentData.length > 0 ? filteredSortedRepaymentData.map((item) => {
                    const sortedEmis = item.emiDetails?.sort((a, b) => a.emiNumber - b.emiNumber) || [];
                    const nextPendingEmi = sortedEmis.find(emi => ['PENDING', 'OVERDUE', 'PARTIAL'].includes(emi.rePaymentStatus?.toUpperCase()));
                    const displayId = item.invoiceId?.invoiceNumber ? `Inv: ${item.invoiceId.invoiceNumber}` : `Offer: ${item._id}`;
                    const displayTitle = item.invoiceId?.invoiceNumber ? `Offer ID: ${item._id}` : `Offer ID: ${item._id}`; // Show OfferID always in title

                    return (
                      <tr key={item.clientId} className="hover:bg-blue-50 text-sm">
                        <td className="px-4 py-4 whitespace-nowrap font-medium text-gray-700" title={displayTitle}>{displayId}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-gray-900 font-semibold text-right">{formatCurrency(item.disbursementInfo?.disbursedAmount)}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-center">
                          <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClass(item.status)}`}>
                            {item.status || 'N/A'}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{sortedEmis.length || 0}</td>
                        <td className={`px-4 py-4 whitespace-nowrap text-sm font-medium ${nextPendingEmi ? 'text-red-600' : 'text-gray-500'}`}>
                          {nextPendingEmi ? formatDate(nextPendingEmi.rePaymentDate) : (item.status?.toUpperCase() === 'PAID' ? 'Completed' : 'N/A')}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">{formatDate(item.updatedAt)}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-center">
                          <button
                            onClick={() => openEmiModal(item)}
                            className="px-3 py-1 bg-purple-100 text-purple-700 rounded hover:bg-purple-200 disabled:opacity-50 disabled:cursor-not-allowed transition duration-150 text-xs font-medium"
                            disabled={!item.emiDetails || item.emiDetails.length === 0}
                          >
                            View Details
                          </button>
                        </td>
                      </tr>
                    );
                  }) : (
                    <tr><td colSpan="7" className="text-center py-5 text-gray-500">No active or completed loans with repayment schedules found.</td></tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div> {/* End Content Area Div */}


      {/* EMI Details Modal (Identical structure, no functional changes needed here) */}
      {showEmiModal && selectedOfferForModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75 flex justify-center items-center z-50 p-4 overflow-y-auto transition-opacity duration-300 ease-out" style={{ opacity: 1 }}>
          <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col transform transition-all duration-300 ease-out scale-100">
            {/* Modal Header */}
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white z-10">
              <h2 className="text-lg font-semibold text-gray-800">
                EMI Schedule for Offer: <span className="font-mono text-sm">{selectedOfferForModal._id}</span>
                <span className="block text-sm text-gray-500 font-normal">
                  Invoice: {selectedOfferForModal.invoiceId?.invoiceNumber || 'N/A'} | Lender: {selectedOfferForModal.lenderId?.businessName || `${selectedOfferForModal.lenderId?.firstName || ''} ${selectedOfferForModal.lenderId?.lastName || ''}`.trim() || 'N/A'}
                </span>
              </h2>
              <button onClick={closeEmiModal} className="text-gray-400 hover:text-gray-600"> <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" /></svg> </button>
            </div>
            {/* Modal Body */}
            <div className="flex-grow overflow-y-auto p-6">
              <div className="overflow-x-auto border rounded-md">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="px-3 py-2 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">EMI #</th>
                      <th className="px-3 py-2 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Due Date</th>
                      <th className="px-3 py-2 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Amount Due</th>
                      <th className="px-3 py-2 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Principal</th>
                      <th className="px-3 py-2 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Service Fee</th>
                      <th className="px-3 py-2 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                      <th className="px-3 py-2 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Paid Date</th>
                      <th className="px-3 py-2 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Amount Paid</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {Array.isArray(selectedOfferForModal.emiDetails) && selectedOfferForModal.emiDetails.length > 0 ?
                      [...selectedOfferForModal.emiDetails].sort((a, b) => a.emiNumber - b.emiNumber).map(emi => (
                        <tr key={emi._id || emi.emiNumber} className="hover:bg-blue-50 text-xs">
                          <td className="px-3 py-2 whitespace-nowrap font-medium text-gray-700">{emi.emiNumber}</td>
                          <td className="px-3 py-2 whitespace-nowrap text-gray-600">{formatDate(emi.rePaymentDate)}</td>
                          <td className="px-3 py-2 whitespace-nowrap text-gray-800 font-medium text-right">{formatCurrency(emi.rePaymentAmount)}</td>
                          <td className="px-3 py-2 whitespace-nowrap text-gray-600 text-right">{formatCurrency(emi.principalRecovered, '')}</td>
                          <td className="px-3 py-2 whitespace-nowrap text-gray-600 text-right">{formatCurrency(emi.interestAmount, '')}</td>
                          <td className="px-3 py-2 whitespace-nowrap text-center">
                            <span className={`px-2 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClass(emi.rePaymentStatus)}`}>
                              {emi.rePaymentStatus || 'N/A'}
                            </span>
                          </td>
                          <td className="px-3 py-2 whitespace-nowrap text-gray-600">{formatDate(emi.rePaymentActualDate)}</td>
                          <td className="px-3 py-2 whitespace-nowrap text-green-700 font-semibold text-right">{formatCurrency(emi.paidAmount)}</td>
                        </tr>
                      )) : (
                        <tr><td colSpan="9" className="text-center py-4 text-gray-500 text-sm">No EMI details available for this loan.</td></tr>
                      )}
                  </tbody>
                </table>
              </div>
            </div>
            {/* Modal Footer */}
            <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-end sticky bottom-0">
              <button onClick={closeEmiModal} className="px-4 py-2 bg-gray-300 text-gray-800 rounded shadow hover:bg-gray-400 transition duration-150 font-medium">
                Close
              </button>
            </div>
          </div>
        </div>
      )}

    </div> // End Main Div
  );
}