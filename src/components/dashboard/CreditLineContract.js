import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useHistory, Link } from 'react-router-dom';
import axios from 'axios';
import config from '../../config.json';
import SharedCache from '../../sharedCache';
import {
    CheckCircleIcon,
    DocumentTextIcon,
    ShieldCheckIcon,
    CurrencyDollarIcon,
    ClockIcon,
    XMarkIcon,
    BuildingLibraryIcon // For fallback lender icon
} from '@heroicons/react/24/outline';
import Modal from 'react-modal';
import LoadingModal from '../Reusable/Loading';

Modal.setAppElement('#root'); // Set the app element for accessibility

// Helper function to fetch lender details
// Place it outside the component for better separation or keep inside useEffect if preferred
const fetchLenderDetails = async (lenderId, token) => {
    if (!lenderId) return null; // No ID, no fetch
    try {
        // Using the /lenders/:id endpoint discussed for the dashboard
        const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/lenders/${lenderId}`, {
            headers: { 'x-auth-token': token }
        });
        // Assuming response.data directly contains the lender object like:
        // { _id, lenderName, logoUrl, ... }
        return response.data;
    } catch (error) {
        console.error(`Error fetching lender details for ID ${lenderId}:`, error);
        // Return null if a lender can't be fetched to handle gracefully
        return null;
    }
};

const CreditLineContract = () => {
    const { financialInstitutionId } = useParams(); // This is the OFFER ID from the route
    const history = useHistory();
    const [offerDetails, setOfferDetails] = useState(null);
    // REMOVED: const [selectedInstitution, setSelectedInstitution] = useState(null);
    const [lenderDetails, setLenderDetails] = useState(null); // State for fetched lender data
    const [agreedAccurate, setAgreedAccurate] = useState(false);
    const [readTerms, setReadTerms] = useState(false);
    const [readFinancialConsent, setReadFinancialConsent] = useState(false);
    const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const user = SharedCache.get('user');
    const token = SharedCache.get('token');
    const [modalIsOpen, setModalIsOpen] = useState(false);
    const [contractPdfUrl, setContractPdfUrl] = useState(''); // Default URL, will be updated dynamically

    const TERMS_CONDITIONS_URL = '/terms-and-conditions';
    const PRIVACY_POLICY_URL = '/privacy-policy';

    // --- Modal States ---
    const [isDocViewerOpen, setIsDocViewerOpen] = useState(false);
    const [docViewerUrl, setDocViewerUrl] = useState('');
    const [isConsentModalOpen, setIsConsentModalOpen] = useState(false);

    // --- Modal Control Functions ---
    const openDocumentViewer = (url) => {
        setDocViewerUrl(url);
        setIsDocViewerOpen(true);
    };

    const closeDocumentViewer = () => {
        setIsDocViewerOpen(false);
        setDocViewerUrl(''); // Clear URL when closing
    };

    const openConsentModal = () => {
        setIsConsentModalOpen(true);
    };

    const closeConsentModal = () => {
        setIsConsentModalOpen(false);
    };

    // --- Link Click Handlers ---
    const handleOpenTerms = (e) => {
        e.preventDefault(); // Prevent default link behavior if using <a>
        openDocumentViewer(TERMS_CONDITIONS_URL);
    };

    const handleOpenPrivacy = (e) => {
        e.preventDefault();
        openDocumentViewer(PRIVACY_POLICY_URL);
    };

    const handleOpenFinancialConsent = (e) => {
        e.preventDefault();
        openConsentModal();
    };

    // REMOVED: Hardcoded Financial Institutions Data (financialInstitutions object)

    useEffect(() => {
        // Defined fetchLenderDetails helper function inside or outside useEffect

        const fetchDetails = async () => {
            setLoading(true);
            setError(null);
            setOfferDetails(null);
            setLenderDetails(null); // Reset lender details on fetch

            try {
                // 1. Fetch Offer Details using the ID from the route (which is the offer ID)
                const offerResponse = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/offer/${financialInstitutionId}`, {
                    headers: {
                        'x-auth-token': token,
                    },
                });

                if (!offerResponse.data?.offer) {
                    throw new Error("Offer not found");
                }
                const fetchedOffer = offerResponse.data.offer;
                setOfferDetails(fetchedOffer);

                // 2. Fetch Lender Details using lenderId from the fetched offer
                if (fetchedOffer.lenderId) {
                    const fetchedLender = await fetchLenderDetails(fetchedOffer.lenderId, token);
                    setLenderDetails(fetchedLender); // Store fetched lender details
                    if (!fetchedLender) {
                        console.warn(`Could not fetch details for lender ID: ${fetchedOffer.lenderId}. Using fallbacks.`);
                        // No need to setError here, the UI will use fallbacks
                    }
                } else {
                    console.warn("Offer details do not contain a lenderId. Lender info will be missing.");
                    // No need to setError, just lender info will be missing
                }

                // REMOVED: Logic setting selectedInstitution based on hardcoded data

            } catch (err) {
                console.error('Error fetching contract details:', err);
                setError(`Failed to fetch contract details: ${err.message}`);
                // Set state based on error (e.g., clear offerDetails)
                setOfferDetails(null);
                setLenderDetails(null);
            } finally {
                setLoading(false);
            }
        };

        fetchDetails();
    }, [financialInstitutionId, token]); // Dependencies for the effect


    const handleSignContract = async () => {
        setIsGeneratingPdf(true); // <<< ADD THIS LINE to show PDF generation loading modal
        try {
            // Generate the PDF before showing the modal
            const agreementFlags = {
                agreedAccurate,
                readTerms,
                readFinancialConsent
            };

            const result = await generateRevolvingCreditPdf(
                offerDetails,
                user,
                lenderDetails,
                token,
                config,
                agreementFlags
            );

            if (result.success && result.signedUrl) {
                setContractPdfUrl(result.signedUrl);
                console.log("Contract PDF URL updated:", result.signedUrl);
            } else {
                console.error("Failed to generate contract PDF:", result.error);
                // You can optionally show an error message to the user here
                // setError(result.error || "Failed to generate contract PDF document."); // Example
            }
        } catch (error) {
            console.error("Error generating contract PDF:", error);
            // setError("An unexpected error occurred while generating the contract document."); // Example
        } finally {
            setIsGeneratingPdf(false); // <<< ADD THIS LINE to hide PDF generation loading modal
            setModalIsOpen(true); // Open the main e-sign modal (even if PDF generation failed)
        }
    };

    const confirmSignContract = async () => {
        setModalIsOpen(false);
        setLoading(true); // Show loading during the entire process
        setError(null);   // Clear previous errors
        try {
            const userId = user?._id || user?.id;
            console.log(userId);
            if (!userId) throw new Error('User not logged in.');
            if (!offerDetails?._id) throw new Error('Offer details are missing.');
            if (!offerDetails.lenderId) throw new Error('Lender information is missing from the offer.');

            // --- Step 1: Call the new endpoint to mark the offer as ACCEPTED ---
            console.log(`Attempting to accept offer ${offerDetails._id} via new route...`);
            const acceptOfferResponse = await axios.post(
                `${config.apiUrl}/ops/invoiceFinancing/offers/${offerDetails._id}/accept`,
                {}, // Sending empty body, unless backend expects comments, etc.
                {
                    headers: {
                        'x-auth-token': token // Merchant's auth token
                    }
                }
            );

            // Check if the acceptance call was successful
            if (acceptOfferResponse.status !== 200) {
                const errorMsg = acceptOfferResponse.data?.message || `Status: ${acceptOfferResponse.status}`;
                console.error("Accept Offer via new route Failed Response:", acceptOfferResponse.data || acceptOfferResponse.statusText);
                throw new Error(`Failed to accept the offer. ${errorMsg}`);
            }
            console.log("Offer acceptance via new route successful:", acceptOfferResponse.data);
            // Optional: You might want to update the local offerDetails state if the response returns the updated offer
            // setOfferDetails(acceptOfferResponse.data.offer);


            // --- Step 2: Proceed to create/update the credit line ---
            // The payload remains largely the same, ensuring acceptedOfferId is included
            const creditLinePayload = {
                userId: userId,
                creditLineData: {
                    creditLimit: offerDetails.creditLimit,
                    tenure: offerDetails.tenureDays,
                    interestRate: parseFloat(offerDetails.interestRate),
                    processingFee: offerDetails.processingFee?.value || 0, // Extract the 'value'
                    processingFeeType: offerDetails.processingFee?.type || 'flat', // Extract the 'type'
                    currency: offerDetails.currency || 'QAR',
                    creditLineStatus: 'APPROVED',
                    offerAccepted: true,
                    acceptedOfferId: offerDetails._id,
                    lenderId: offerDetails.lenderId,
                },
                reviewedBy: userId,
            };

            console.log("Sending payload to create/update credit line:", JSON.stringify(creditLinePayload, null, 2));
            const creditLineResponse = await axios.post(
                `${config.apiUrl}/ops/invoiceFinancing/creditLineCreateOrUpdate`,
                creditLinePayload,
                { headers: { 'Content-Type': 'application/json', 'x-auth-token': token } }
            );

            if (creditLineResponse.status !== 200 && creditLineResponse.status !== 201) {
                const errorMsg = creditLineResponse.data?.message || `Status: ${creditLineResponse.status}`;
                console.error("Credit Line Update/Create Failed Response:", creditLineResponse.data || creditLineResponse.statusText);
                // INFO: At this point, the offer might be 'ACCEPTED' but credit line creation failed.
                // Consider backend mechanisms or frontend messages for potential inconsistencies if needed.
                throw new Error(`Failed to create/update Credit Line after accepting offer. ${errorMsg}`);
            }
            console.log("Credit Line Update/Create Success:", creditLineResponse.data);

            // --- Step 3: Store the offer ID in local storage for fallback ---
            try {
                // Store the offer ID in local storage as a fallback mechanism
                localStorage.setItem('acceptedOfferId', offerDetails._id);
                console.log("Stored accepted offer ID in local storage:", offerDetails._id);
            } catch (storageError) {
                console.warn("Failed to store offer ID in local storage:", storageError);
                // Continue even if storage fails
            }

            // --- Step 4: Navigate on full success ---
            console.log("Contract finalized successfully, navigating to dashboard.");
            history.push('/dashboard');

        } catch (error) {
            console.error('Error during contract confirmation process:', error);
            let displayError = `Failed to finalize contract: ${error.message}. Please try again.`;
            if (axios.isAxiosError(error) && error.response?.data?.message) {
                displayError = `Failed to finalize contract: ${error.response.data.message}`;
            } else if (axios.isAxiosError(error) && error.response?.status === 401) {
                displayError = "Authentication failed. Please log in again.";
            }
            setError(displayError);
            // alert(displayError); // Avoid alert if possible
        } finally {
            setLoading(false); // Ensure loading stops
        }
    };


    const handleGoBack = () => {
        history.push('/dashboard');
    };

    // --- Render Logic ---

    // Define a consistent theme color (can be moved to config or theme context later)
    const themeColor = "#004141";

    if (error) {
        // Display error state prominently
        return (
            <div className="p-6 text-red-600 bg-red-50 border border-red-300 rounded-md max-w-2xl mx-auto mt-10 text-center">
                <h2 className="font-semibold mb-2">Error Loading Contract</h2>
                <p>{error}</p>
                <button
                    onClick={handleGoBack}
                    className="mt-4 bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 transition-colors"
                >
                    Go to Dashboard
                </button>
            </div>
        );
    }
    // Helper function for currency formatting (optional)
    const formatCurrency = (amount, currency = 'QAR') => {
        const num = Number(amount);
        return !isNaN(num) ? `${currency} ${num.toLocaleString()}` : 'N/A';
    };

    // This block handles the general loading state (e.g., initial data fetch, final contract submission)
    if (loading) { // This is the original 'loading' state
        return <LoadingModal title="Please wait!" message="Please wait a moment while we load your credit offer data." />;
    }

    // Check if essential offer details are missing after loading and no error
    if (!offerDetails) {
        return (
            <div className="p-6 text-center text-gray-600">
                <p>Offer details could not be loaded or found. Please go back and try again.</p>
                <button
                    onClick={handleGoBack}
                    className="mt-4 bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 transition-colors"
                >
                    Go to Dashboard
                </button>
            </div>
        );
    }

    const GeneralLoadingModal = ({ title, message }) => {
        return <LoadingModal />
    }
    return (
        <div className="min-h-screen flex flex-col bg-gray-50">
            {/* EXISTING HEADER - UNCHANGED AS REQUESTED */}
            {isGeneratingPdf && (
                <GeneralLoadingModal
                    title="Please wait!"
                    message="We are generating your credit facility contract. Please wait!"
                />
            )}
            <header
                className="shadow p-4 flex items-center justify-between sticky top-0 z-10"
                style={{ backgroundColor: themeColor }} // Assuming themeColor is defined in your component
            // setCurrentPage={() => { }} // This prop was in your original, ensure it's handled if needed
            >
                <div className="flex items-center min-w-0">
                    {lenderDetails?.logoUrl ? (
                        <img
                            src={lenderDetails.logoUrl}
                            alt={`${lenderDetails.lenderName || 'Lender'} Logo`}
                            className="h-10 w-10 mr-3 rounded-md object-contain bg-white p-1 flex-shrink-0"
                        />
                    ) : (
                        <div className="h-10 w-10 mr-3 rounded-md bg-white flex items-center justify-center flex-shrink-0">
                            <BuildingLibraryIcon className="h-6 w-6 text-gray-400" /> {/* Ensure BuildingLibraryIcon is imported */}
                        </div>
                    )}
                    <h1 className="text-lg md:text-xl lg:text-2xl font-bold text-white truncate">
                        {lenderDetails?.lenderName || 'Financial Institution'} - Credit Line Agreement
                    </h1>
                </div>
                <button
                    onClick={handleGoBack}
                    className="bg-white text-[#208039] px-3 md:px-4 py-2 rounded-md hover:bg-gray-100 transition-colors text-sm ml-2 flex-shrink-0"
                >
                    Back to Dashboard
                </button>
            </header>

            {/* MODIFIED MAIN CONTENT AREA */}
            <main className="flex-1 p-4 md:p-8">

                {/* "Credit Line Agreement" Page Title - Outside the main card structure */}
                <h1 className="text-2xl md:text-3xl font-semibold text-gray-800 text-center mb-6">
                    Credit Line Agreement
                </h1>

                {/* Main container for all contract sections below the page title */}
                <div className="max-w-4xl mx-auto">

                    {/* Section 1: Offer Summary */}
                    <div className="bg-white border border-gray-200 rounded-lg shadow-md overflow-hidden">
                        <div className="p-6">
                            <h2 className="text-xl md:text-2xl font-semibold text-gray-800 mb-4">Offer summary</h2>
                            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                {/* Credit Limit with special background #e1f2f5 */}
                                <div className="p-4 border border-gray-200 rounded-md" style={{ backgroundColor: '#e1f2f5' }}>
                                    <p className="text-sm text-gray-700">Credit Limit</p>
                                    <p className="text-xl font-bold text-gray-900">
                                        {offerDetails?.creditLimit ? formatCurrency(offerDetails.creditLimit, offerDetails.currency) : 'QAR 35,000'}
                                    </p>
                                </div>
                                {/* Service Fee % - on white background */}
                                <div className="p-4 border border-gray-200 rounded-md bg-white"> {/* Explicitly white if needed, though parent is white */}
                                    <p className="text-sm text-gray-600">Service Fee %</p>
                                    <p className="text-xl font-bold text-gray-900">
                                        {offerDetails?.interestRate ? `${offerDetails.interestRate}%` : '10%'}
                                    </p>
                                </div>
                                {/* Processing Fee - on white background */}
                                <div className="p-4 border border-gray-200 rounded-md bg-white"> {/* Explicitly white */}
                                    <p className="text-sm text-gray-600">Processing Fee</p>
                                    <p className="text-xl font-bold text-gray-900">
                                        {offerDetails?.processingFee.type === 'percentage'
                                            ? `${offerDetails.processingFee.value}%`
                                            : (offerDetails?.processingFee?.value ? formatCurrency(offerDetails.processingFee?.value, offerDetails.currency) : 'QAR 100')}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Area below Offer Summary with bg-gray-100 */}
                    <div className="bg-gray-100 p-4 md:p-6 rounded-lg space-y-6">

                        {/* Invoice Financing Consent - Rounded div with white BG */}
                        <div className="bg-white p-4 rounded-lg shadow-md">
                            <h2 className="text-lg font-semibold text-[#3eaf7d] mb-2">Invoice Financing Consent</h2>
                            <div className="text-sm text-gray-700 space-y-2">
                                <p>
                                    Please review the official contract document for the complete terms and conditions associated with this credit line offer provided by{' '}
                                    <strong className="text-gray-800">{lenderDetails?.lenderName || 'Qatar Islamic Bank Ltd.'}</strong>.
                                </p>
                                <p>Key aspects typically include:</p>
                                <ul className="list-disc pl-5 space-y-1 marker:text-gray-500">
                                    <li>
                                        <strong>Repayment Schedule:</strong> Details on frequency ({offerDetails?.emiRepaymentFrequency || 'MONTHLY'}) and calculation of repayments.
                                    </li>
                                    <li>
                                        <strong>Fees:</strong> Information on potential late payment fees, commitment fees, or other charges.
                                    </li>
                                    <li>
                                        <strong>Covenants:</strong> Any requirements the borrower must adhere to while the credit line is active.
                                    </li>
                                    <li>
                                        <strong>Default Conditions:</strong> Circumstances under which the credit line may be considered in default.
                                    </li>
                                    <li>
                                        <strong>Governing Law:</strong> The jurisdiction whose laws govern the agreement.
                                    </li>
                                </ul>
                                <p className="pt-1">
                                    Ensure you download and read the full contract linked in the signing confirmation step.
                                </p>
                            </div>
                        </div>

                        {/* General Information - Rounded div with white BG */}
                        <div className="bg-white p-4 rounded-lg shadow-md">
                            <h2 className="text-lg font-semibold text-[#3eaf7d] mb-2">General Information</h2>
                            <p className="text-sm text-gray-700">
                                This credit line is designed to support your business operations. Please manage your utilization responsibly and adhere to the agreed repayment terms.
                            </p>
                            {/* Important Notice */}
                            <div className="bg-[#fff1d2] p-4 rounded-lg border mt-6 border-[#fff1d2">
                                <h3 className="text-md font-semibold text-yellow-800 mb-1">Important Notice</h3>
                                <p className="text-sm text-yellow-700">
                                    This summary provides key details of the offer. The legally binding terms are contained within the Official Credit Line Agreement document. By proceeding, you confirm you will review the full agreement before signing.
                                </p>
                            </div>
                        </div>


                        {/* Key Fact Summary (Bottom) - Rounded div with white BG */}
                        <div className="bg-white p-4 rounded-lg shadow-md">
                            <h2 className="text-lg font-semibold text-[#3eaf7d] mb-3">Key Fact Summary</h2>
                            <div className="border border-gray-200 p-4 rounded-md grid grid-cols-2 md:grid-cols-4 gap-x-6 gap-y-4">
                                <div>
                                    <p className="text-sm text-gray-600">Credit Limit</p>
                                    <p className="text-lg font-bold text-gray-900">
                                        {offerDetails?.creditLimit ? formatCurrency(offerDetails.creditLimit, offerDetails.currency) : 'QAR 35,000'}
                                    </p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">Tenure</p>
                                    <p className="text-lg font-bold text-gray-900">
                                        {offerDetails?.tenureDays ? `${offerDetails.tenureDays} Days` : '10%'}
                                    </p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">Processing Fee</p>
                                    <p className="text-lg font-bold text-gray-900">
                                        {offerDetails?.processingFee?.type === 'percentage'
                                            ? `${offerDetails.processingFee.value}%`
                                            : (offerDetails?.processingFee.value ? formatCurrency(offerDetails.processingFee.value, offerDetails.currency) : 'QAR 100')}
                                    </p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">Service Fee %</p>
                                    <p className="text-lg font-bold text-gray-900">
                                        {offerDetails?.interestRate ? `${offerDetails.interestRate}%` : '10%'}
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* Agreement Checkboxes and Proceed Button - in a final white card */}
                        <div className="bg-white p-6 rounded-lg shadow-md">
                            <div className="space-y-4 mb-6">
                                <div className="flex items-start">
                                    <input
                                        id="agreeAccurateMainContent"
                                        name="agreeAccurate"
                                        type="checkbox"
                                        checked={agreedAccurate}
                                        onChange={(e) => setAgreedAccurate(e.target.checked)}
                                        className="h-4 w-4 text-blue-600 border-gray-300 rounded mt-1 focus:ring-2 focus:ring-blue-500"
                                    />
                                    <label htmlFor="agreeAccurateMainContent" className="ml-3 block text-sm text-gray-700">
                                        I confirm accuracy and completeness.
                                    </label>
                                </div>
                                <div className="flex items-start">
                                    <input
                                        id="readTermsMainContent"
                                        name="readTerms"
                                        type="checkbox"
                                        checked={readTerms}
                                        onChange={(e) => setReadTerms(e.target.checked)}
                                        className="h-4 w-4 text-blue-600 border-gray-300 rounded mt-1 focus:ring-2 focus:ring-blue-500"
                                    />
                                    <label htmlFor="readTermsMainContent" className="ml-3 block text-sm text-gray-700">
                                        I agree to the{' '}
                                        <Link
                                            to={TERMS_CONDITIONS_URL}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="font-medium text-[#daf3d8] hover:text-blue-700 underline"
                                        >
                                            T&Cs
                                        </Link>
                                        {' '}and{' '}
                                        <Link
                                            to={PRIVACY_POLICY_URL}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="font-medium text-[#daf3d8] hover:text-blue-700 underline"
                                        >
                                            Privacy Policy
                                        </Link>.
                                    </label>
                                </div>
                                <div className="flex items-start">
                                    <input
                                        id="readFinancialConsentMainContent"
                                        name="readFinancialConsent"
                                        type="checkbox"
                                        checked={readFinancialConsent}
                                        onChange={(e) => setReadFinancialConsent(e.target.checked)}
                                        className="h-4 w-4 text-blue-600 border-gray-300 rounded mt-1 focus:ring-2 focus:ring-blue-500"
                                    />
                                    <label htmlFor="readFinancialConsentMainContent" className="ml-3 block text-sm text-gray-700">
                                        I agree to the{' '}
                                        <a
                                            href="#"
                                            onClick={(e) => {
                                                e.preventDefault();
                                                openConsentModal();
                                            }}
                                            className="font-medium text-[#daf3d8] hover:text-blue-700 underline"
                                        >
                                            Financial Data & Credit Bureau Report Consent
                                        </a>.
                                    </label>
                                </div>
                            </div>

                            <button
                                onClick={handleSignContract}
                                disabled={!(agreedAccurate && readTerms && readFinancialConsent) || loading}
                                className={`w-full py-3 px-4 text-base font-semibold rounded-md text-white transition-colors duration-150 flex items-center justify-center 
                                ${(agreedAccurate && readTerms && readFinancialConsent && !loading)
                                        ? `bg-[${themeColor}] hover:opacity-90 shadow-md hover:shadow-lg` // Using themeColor for consistency with original button
                                        : 'bg-gray-400 cursor-not-allowed opacity-70' // Adjusted disabled state from original
                                    }`}
                            >
                                {loading && modalIsOpen ? (
                                    <>
                                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Processing...
                                    </>
                                ) : (
                                    'Proceed to E-Sign Contract'
                                )}
                            </button>
                        </div>
                    </div> {/* End of bg-gray-100 container */}
                </div> {/* End of max-w-4xl container */}
            </main>

            {/* EXISTING MODALS AND STYLES - UNCHANGED */}
            <Modal
                isOpen={isConsentModalOpen}
                onRequestClose={closeConsentModal}
                contentLabel="Financial Data Consent"
                style={{
                    overlay: { backgroundColor: 'rgba(0, 0, 0, 0.75)', zIndex: 1050 },
                    content: { top: '50%', left: '50%', transform: 'translate(-50%, -50%)', width: '90%', maxWidth: '700px', maxHeight: '80vh', padding: '0', border: 'none', borderRadius: '8px', overflow: 'hidden', display: 'flex', flexDirection: 'column' }
                }}
                ariaHideApp={false}
            >
                <div className="flex justify-between items-center p-4 bg-gray-100 border-b rounded-t-lg flex-shrink-0">
                    <h2 className="text-lg font-semibold">Financial Data & Credit Bureau Report Consent</h2>
                    <button onClick={closeConsentModal} className="text-gray-500 hover:text-gray-800">
                        <XMarkIcon className="h-6 w-6" /> {/* Ensure XMarkIcon is imported */}
                    </button>
                </div>
                {/* Ensure this modal's content matches your existing code, this is a placeholder structure */}
                <div className="p-6 bg-white overflow-y-auto flex-1">
                    {/* Copied from your original provided code, assuming this is the content */}
                    <h2 className="text-lg font-semibold mb-4">Financial Data & Credit Bureau Report Consent</h2>
                    <div className="prose max-w-none text-sm">
                        <p>
                            I hereby submit voluntarily at my own discretion, the soft copy of the document, to Fundfina and its partnering lenders for the purpose of establishing my identity, address proof and credit worthiness. I authorise Fundfina and the lending partner to fetch my data from Protean/NSDL, GST, Credit Bureau, Banks and Account Aggregators.
                        </p>
                        <p>
                            I also hereby authorise Fundfina and its partnering lenders to utilise the information provided by me and to share the same with other financial institutions, credit information companies, statutory bodies, etc. as may be required for the purpose of evaluating my creditworthiness and for other related purposes.
                        </p>
                        <p>
                            I understand that the submission of this document does not guarantee the approval of my credit line application and that the decision to approve or reject my application is at the sole discretion of Fundfina and its partnering lenders.
                        </p>
                        <p>
                            I confirm that I have read and understood the above and agree to the terms and conditions outlined herein.
                        </p>
                    </div>
                </div>
                <div className="p-4 bg-gray-50 border-t flex justify-end rounded-b-lg flex-shrink-0">
                    <button onClick={closeConsentModal} className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm font-medium">
                        Close
                    </button>
                </div>
            </Modal>

            <Modal
                isOpen={modalIsOpen}
                onRequestClose={() => !loading && setModalIsOpen(false)}
                className="modal-content"
                overlayClassName="modal-overlay"
                contentLabel="Confirm Contract Signing Modal"
            >
                <h2 className="text-xl font-semibold mb-5 text-gray-800">Confirm Contract Signing</h2>
                <p className="text-gray-600 mb-5">Please review the details and the contract document below before confirming.</p>

                <div className="grid grid-cols-2 gap-4 mb-6 bg-gray-50 p-4 rounded-md border">
                    <div>
                        <h3 className="text-xs font-semibold text-gray-500 uppercase mb-1">Credit Limit</h3>
                        <p className="text-md font-bold text-gray-900">{formatCurrency(offerDetails?.creditLimit, offerDetails?.currency)}</p>
                    </div>
                    <div>
                        <h3 className="text-xs font-semibold text-gray-500 uppercase mb-1">Tenure</h3>
                        <p className="text-md font-bold text-gray-900">{offerDetails?.tenureDays ? `${offerDetails.tenureDays} days` : 'N/A'}</p>
                    </div>
                    <div>
                        <h3 className="text-xs font-semibold text-gray-500 uppercase mb-1">Processing Fee</h3>
                        <p className="text-md font-bold text-gray-900">
                            {offerDetails?.processingFee.type === 'percentage'
                                ? `${offerDetails.processingFee.value}%`
                                : formatCurrency(offerDetails?.processingFee.value, offerDetails?.currency)}
                        </p>
                    </div>
                    <div>
                        <h3 className="text-xs font-semibold text-gray-500 uppercase mb-1">Service Fee %</h3>
                        <p className="text-md font-bold text-gray-900">
                            {offerDetails?.interestRate ? `${offerDetails.interestRate}%` : 'N/A'}
                        </p>
                    </div>
                </div>

                <div className="mb-6 border rounded-md overflow-hidden">
                    <h3 className="text-sm font-semibold text-gray-700 bg-gray-100 p-3 border-b">Official Credit Line Contract Document</h3>
                    <iframe
                        src={contractPdfUrl} // Make sure contractPdfUrl is correctly set
                        title="Credit Line Contract Document"
                        width="100%"
                        height="400px"
                        style={{ border: 'none' }}
                    >
                        <p>Your browser does not support PDFs. Please download the PDF to view it:
                            <a href={contractPdfUrl} target="_blank" rel="noopener noreferrer">Download PDF</a>
                        </p>
                    </iframe>
                </div>
                <div className="flex justify-end gap-4">
                    <button
                        onClick={() => setModalIsOpen(false)}
                        disabled={loading}
                        className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition-colors disabled:opacity-50"
                    >
                        Cancel
                    </button>
                    <button
                        onClick={confirmSignContract}
                        disabled={loading}
                        className={`bg-[${themeColor}] text-white font-bold py-2 px-4 rounded-md transition-colors hover:opacity-90 disabled:opacity-50 disabled:cursor-wait flex items-center`}
                    >
                        {loading && (
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        )}
                        {loading ? 'Confirming...' : 'Confirm & E-Sign'}
                    </button>
                </div>
            </Modal >

            <style jsx global>{`
            .modal-overlay {
                position: fixed;
                inset: 0;
                background-color: rgba(0, 0, 0, 0.6);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000; /* Ensure this is below the consent modal's overlay if they can overlap */
            }
            .modal-content {
                position: relative;
                background: white;
                border-radius: 0.5rem;
                padding: 1.5rem;
                width: 90%;
                max-width: 768px;
                max-height: 90vh;
                overflow-y: auto;
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                outline: none;
                z-index: 1001;
            }
        `}</style>
        </div >
    );
};

export default CreditLineContract;

// Function to generate revolving credit PDF
export const generateRevolvingCreditPdf = async (
    offerDetails,
    user,
    lenderDetails,
    token,
    config,
    agreementFlags
) => {
    // --- 1. Input Validation ---
    if (!offerDetails?._id || !user?._id || !lenderDetails?.lenderName || !token || !config?.apiUrl) {
        console.error("generateRevolvingCreditPdf: Missing required arguments.");
        return { success: false, error: "Missing required data to generate the contract." };
    }
    if (!agreementFlags?.agreedAccurate || !agreementFlags?.readTerms || !agreementFlags?.readFinancialConsent) {
        return { success: false, error: "Please agree to all terms and consents before proceeding." };
    }

    try {
        const userId = user._id || user.id; // Handle potential variations in user ID field
        const offerId = offerDetails._id;

        // --- 2. Construct Payload ---
        // Ensure this payload matches exactly what '/generateRevolvingCreditContract' expects
        const pdfGenerationPayload = {
            userId: userId,
            offerId: offerId,
            lenderName: lenderDetails.lenderName || 'Default Lender',
            lenderAddress: lenderDetails.address || 'N/A',
            // Ensure borrowerName is always provided (API reported this as missing)
            borrowerName: user.businessName || user?.kyc?.businessDetails?.legalEntityName || user.name || user.companyName || 'Borrower',
            borrowerAddress: user.address?.fullAddress || user.address?.addressLine1 || localStorage.getItem('kyc_address_addressLine1') || 'N/A',
            borrowerCountry: user.address?.country || 'Qatar',
            facilityAmount: offerDetails.creditLimit || 0,
            advancePercentage: offerDetails.advanceRate || 80,
            interestRate: offerDetails.interestRate || 0,
            facilityDuration: `${offerDetails.tenureDays || 0} Days`,
            invoiceDueDays: offerDetails.maxInvoiceAge || 90,
            // Ensure facilityFeePercentage is always provided (API reported this as missing)
            // Force it to be a number and ensure it's not undefined or null
            // facilityFeePercentage: parseFloat(
            //     offerDetails.processingFee?.type === 'percentage'
            //         ? (offerDetails.processingFee?.value || offerDetails.processingFee || offerDetails.facilityFeePercentage || 0)
            //         : (offerDetails.processingFeePercentage || offerDetails.facilityFeePercentage || 0)
            // ) || 0,
            facilityFeePercentage: offerDetails.interestRate || 0,
            lateFeePercentage: offerDetails.lateFee?.rate || 1,
            effectiveDate: new Date().toISOString().split('T')[0],
            authorizedSignatoryLenderName: lenderDetails.contactPerson || lenderDetails.lenderName || 'Lender Signatory',
            authorizedSignatoryLenderTitle: lenderDetails.contactTitle || 'Authorized Signatory',
            // Ensure authorizedSignatoryBorrowerName is always provided (API reported this as missing)
            authorizedSignatoryBorrowerName: user.name || user.businessName || user?.kyc?.businessDetails?.legalEntityName || user.companyName || user.contactName || 'Borrower Signatory',
            authorizedSignatoryBorrowerTitle: 'Authorized Signatory',
            // Pass other optional fields if available and required by backend
            invoiceVerificationFee: offerDetails.invoiceVerificationFee || 0,
            advanceProcessingFee: offerDetails.advanceProcessingFee || 0,
            securityGuarantee: offerDetails.requiresGuarantee || false,
            securityAdditionalCollateral: offerDetails.additionalCollateralDesc || '',
            // Signature/OTP are usually handled post-confirmation, pass placeholders if needed for template
            signature: "PENDING_ESIGN",
            otp: "PENDING_ESIGN",
            lenderTimestamp: new Date().toISOString(), // Can be generation time
            borrowerTimestamp: new Date().toISOString(),
            processingFee: offerDetails.processingFee || 0,
            processingFeeType: offerDetails.processingFeeType || 'percentage',
            // Add any other fields the backend '/generateRevolvingCreditContract' requires
        };

        // Log the specific fields that were reported as missing
        console.log("Checking required fields that were previously missing:");
        console.log("borrowerName:", pdfGenerationPayload.borrowerName);
        console.log("facilityFeePercentage:", pdfGenerationPayload.facilityFeePercentage);
        console.log("authorizedSignatoryBorrowerName:", pdfGenerationPayload.authorizedSignatoryBorrowerName);

        // Debug the processing fee data structure to understand what's available
        console.log("DEBUG - Processing Fee Data:");
        console.log("offerDetails.processingFee:", offerDetails.processingFee);
        console.log("offerDetails.processingFeeType:", offerDetails.processingFeeType);
        console.log("offerDetails.processingFee:", offerDetails.processingFee);
        console.log("offerDetails.processingFeePercentage:", offerDetails.processingFee);
        console.log("offerDetails.facilityFeePercentage:", offerDetails.facilityFeePercentage);

        // Ensure facilityFeePercentage is explicitly set as a number in the payload
        pdfGenerationPayload.facilityFeePercentage = Number(pdfGenerationPayload.facilityFeePercentage) || 0;

        console.log("Sending payload to generate PDF:", JSON.stringify(pdfGenerationPayload, null, 2));

        // --- 3. API Call ---
        // Looking at the API code, it's specifically checking for a field named 'facilityFeePercentage'
        // Let's create a new payload with this field explicitly set at the top level

        // Create a new payload with the facilityFeePercentage explicitly set
        const finalPayload = {
            ...pdfGenerationPayload
        };

        // Log the final payload right before sending
        console.log("FINAL PAYLOAD facilityFeePercentage:", finalPayload.facilityFeePercentage);
        console.log("FINAL PAYLOAD facilityFeePercentage type:", typeof finalPayload.facilityFeePercentage);

        // Log the entire payload for debugging
        console.log("FINAL PAYLOAD:", JSON.stringify(finalPayload, null, 2));

        const response = await axios.post(
            `${config.apiUrl}/ops/invoiceFinancing/generateRevolvingCreditContract`, // Your backend endpoint
            finalPayload,
            { headers: { 'Content-Type': 'application/json', 'x-auth-token': token } }
        );

        // --- 4. Handle Response ---
        if (response.status === 200 && response.data?.signedUrl) {
            console.log("PDF generated successfully. Signed URL:", response.data.signedUrl);
            // Return success and the signed URL
            return { success: true, signedUrl: response.data.signedUrl };
        } else {
            // Extract error message from backend response if available
            const errorMessage = response.data?.message || response.data?.error || 'Failed to generate contract PDF. Invalid response from server.';
            console.error("PDF Generation Failed Response:", response.data || response.statusText);
            return { success: false, error: errorMessage };
        }

    } catch (error) {
        // --- 5. Handle Network/Axios Errors ---
        console.error('Error calling generateRevolvingCreditPdf API:', error);
        let displayError = `Failed to prepare contract document: ${error.message}. Please check your connection and try again.`;
        // Extract more specific error from Axios response if available
        if (axios.isAxiosError(error) && error.response?.data?.message) {
            displayError = `Failed to prepare contract document: ${error.response.data.message}`;
        } else if (axios.isAxiosError(error) && error.response?.data?.error) {
            displayError = `Failed to prepare contract document: ${error.response.data.error}`;
        } else if (axios.isAxiosError(error) && error.response?.status) {
            displayError = `Failed to prepare contract document. Server responded with status: ${error.response.status}.`;
        }
        return { success: false, error: displayError };
    }
};