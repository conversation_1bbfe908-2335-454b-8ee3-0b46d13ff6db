import React, { useEffect, useState } from 'react';
import axios from 'axios';
import config from "../../config.json"

const ActiveInvoices = () => {
  const [invoices, setInvoices] = useState([]);

  useEffect(() => {
    // Fetch the invoices from the backend
    const fetchInvoices = async () => {
      try {
        const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/fetchInvoices`);
        const invoiceData = response.data.map(invoice => ({
          id: invoice._id,
          date: invoice.invoiceDate || 'N/A',
          company: invoice.customerName || 'N/A',
          amount: `QAR ${invoice.totalAmount || 0}`,
          discount: '2%', // Hardcoded for now
          status: 'Pending' // Hardcoded for now
        }));
        setInvoices(invoiceData);
      } catch (error) {
        console.error('Error fetching invoices:', error);
      }
    };

    fetchInvoices();
  }, []);

  return (
    <div className="bg-white mt-8 p-6 rounded-md shadow-lg">
      <h3 className="text-xl font-semibold mb-4">Active Invoices</h3>
      <table className="w-full text-left table-auto">
        <thead>
          <tr className="bg-gray-100">
            <th className="p-2">Date</th>
            <th className="p-2">Buyer Company</th>
            <th className="p-2">Amount</th>
            <th className="p-2">Discount Rate</th>
            <th className="p-2">Status</th>
          </tr>
        </thead>
        <tbody>
          {invoices.length > 0 &&
            invoices.map(invoice => (
              invoice.date !== "N/A" ? (
                <tr key={invoice.id} className="border-b">
                  <td className="p-2">{invoice.date}</td>
                  <td className="p-2">{invoice.company}</td>
                  <td className="p-2">{invoice.amount}</td>
                  <td className="p-2">{invoice.discount}</td>
                  <td className="p-2">{invoice.status}</td>
                </tr>
              ) : (<></>)
            ))}
        </tbody>
      </table>
      {invoices.filter(el => el.date !== 'N/A').length === 0 && (
        <p className="text-center text-gray-500 p-4">
          No active invoices available.
        </p>
      )}
    </div>
  );
};

export default ActiveInvoices;
