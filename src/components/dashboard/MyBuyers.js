import React, { useState, useEffect } from 'react';
import axios from 'axios';
import config from '../../config.json'; // Adjust path if MyBuyersPage is in a different folder
import SharedCache from '../../sharedCache'; // Adjust path
import { getKycInfo } from '../../api/kyc'; // Adjust path
import { sendBuyerInvitationEmail } from '../../api/registration'; // Adjust path
import { toast } from 'react-toastify';
import {
    UserGroupIcon,
    PlusIcon,
    ArrowPathIcon,
    XCircleIcon,
    ChevronDownIcon,
    ExclamationTriangleIcon,
    XMarkIcon
} from '@heroicons/react/24/outline';
import LoadingModal from '../Reusable/Loading';

const fetchLenderDetails = async (lenderId, token) => {
    try {
        const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/lenders/${lenderId}`, {
            headers: { 'x-auth-token': token }
        });
        return response.data;
    } catch (error) {
        console.error(`Error fetching lender details for ID ${lenderId}:`, error);
        return null;
    }
};

const briefcaseMoney = require("../../images/briefcase_money.png");

// Loader Component
const Loader = () => (
    <div className="flex items-center justify-center py-10">
        <div className="w-12 h-12 border-4 border-[#004141] border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-3 text-gray-600">Loading...</p>
    </div>
);

// General Loading Modal Component
const GeneralLoadingModal = ({ title, message }) => {
    return <LoadingModal />
};

const formatCurrency = (amount, currency = 'QAR') => {
    const num = Number(amount);
    if (isNaN(num)) {
        if (typeof amount === 'string' && amount.includes(currency)) return amount;
        return `${currency} 0.00`;
    }
    return `${currency} ${num.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};

const MyBuyersPage = () => {
    const [userInfo, setUserInfo] = useState(null);
    const [allInvoices, setAllInvoices] = useState([]);
    const [displayedBuyers, setDisplayedBuyers] = useState([]);
    const [isLoadingPage, setIsLoadingPage] = useState(true);
    const [pageError, setPageError] = useState('');

    // State for the Add/View Buyer Modal
    const [isAddBuyerModalOpen, setIsAddBuyerModalOpen] = useState(false);
    const [newBuyerData, setNewBuyerData] = useState({
        buyerName: '',
        registrationNumber: '',
        contactPerson: '',
        contactPhone: '',
        contactEmail: '',
    });
    const [isSavingNewBuyer, setIsSavingNewBuyer] = useState(false);
    const [isViewMode, setIsViewMode] = useState(false); // True if modal is for viewing, false for adding
    const [buyerModalError, setBuyerModalError] = useState(''); // For errors within the buyer modal

    const [creditLine, setCreditLine] = useState(null);
    const [creditLineError, setCreditLineError] = useState(null);
    const [isCreditLineOverviewOpen, setIsCreditLineOverviewOpen] = useState(true);
    const [acceptedLenderDetails, setAcceptedLenderDetails] = useState(null);
    const [merchantOffers, setMerchantOffers] = useState([]);
    const [loadingCLDetails, setLoadingCLDetails] = useState(true);
    const [showContractModalCL, setShowContractModalCL] = useState(false);
    const [contractPdfUrlCL, setContractPdfUrlCL] = useState('');

    // New states for credit line activation status
    const [creditLineIsActive, setCreditLineIsActive] = useState(false);
    const [showActivationJourneyContent, setShowActivationJourneyContent] = useState(false);

    const [statusFilter, setStatusFilter] = useState('All');
    const [ageFilter, setAgeFilter] = useState('All');

    const user = SharedCache.get("user") || {};
    const userId = user._id || user.id || "";
    const token = SharedCache.get("token") || "placeholdertoken";
    const userEmail = user.email; // Get user's email for invitations

    useEffect(() => {
        const fetchData = async () => {
            if (!userId || !token) {
                setPageError("User session not found. Please log in again.");
                setIsLoadingPage(false);
                setLoadingCLDetails(false);
                return;
            }
            setIsLoadingPage(true);
            setLoadingCLDetails(true);
            setPageError('');
            try {
                await Promise.all([
                    fetchUserInfoInternal(),
                    fetchInvoicesInternal(),
                    fetchCreditLineDetailsInternal(),
                    fetchMerchantOffersInternal()
                ]);
            } catch (error) {
                console.error("Error fetching page data:", error);
                // Individual fetch functions handle their own toasts for specific errors
            } finally {
                setIsLoadingPage(false);
                setLoadingCLDetails(false);
            }
        };
        fetchData();
    }, [userId, token]);

    // This useEffect populates acceptedLenderDetails based on the creditLine state
    useEffect(() => {
        console.log("MyBuyersPage: creditLine state updated in second useEffect:", creditLine); // Log state change

        const fetchLender = async (lenderIdFromCreditLine) => { // Renamed param for clarity
            console.log(`MyBuyersPage: Attempting to fetch lender details for ID: ${lenderIdFromCreditLine}`);
            const lenderData = await fetchLenderDetails(lenderIdFromCreditLine, token);
            if (lenderData) {
                setAcceptedLenderDetails(lenderData);
                console.log("MyBuyersPage: Successfully fetched and set acceptedLenderDetails:", lenderData);
            } else {
                setAcceptedLenderDetails(null);
                console.log("MyBuyersPage: fetchLenderDetails returned null or empty data.");
            }
        };

        // CRITICAL CHECK: What is creditLine.lenderInfo?
        if (creditLine) {
            console.log("MyBuyersPage: creditLine exists.");
            if (creditLine.lenderInfo) {
                console.log("MyBuyersPage: creditLine.lenderInfo is present:", creditLine.lenderInfo);
                fetchLender(creditLine.lenderId); // Call with lenderId
            } else if (creditLine.lenderId) { // Add this check!
                console.log("MyBuyersPage: creditLine.lenderInfo is NOT present, but lenderId IS:", creditLine.lenderId);
                fetchLender(creditLine.lenderId); // If lenderInfo is missing but lenderId exists, proceed
            } else {
                console.log("MyBuyersPage: creditLine.lenderInfo and creditLine.lenderId are missing or null.");
                setAcceptedLenderDetails(null);
            }
        } else {
            console.log("MyBuyersPage: creditLine is null or undefined.");
            setAcceptedLenderDetails(null);
        }
    }, [creditLine, token]); // Ensure 'token' is also a dependency


    const fetchUserInfoInternal = async () => {
        try {
            const kycData = await getKycInfo(userId);
            if (kycData && kycData.user) {
                setUserInfo(kycData.user);
            } else {
                setUserInfo(null); // Explicitly set to null if no user data
            }
        } catch (error) {
            console.error("Error fetching user KYC info:", error);
            toast.error("Could not fetch user details.");
            throw error;
        }
    };

    const fetchInvoicesInternal = async () => {
        try {
            let url = `${config.apiUrl}/ops/invoiceFinancing/fetchInvoices?userId=${userId}`;
            const response = await axios.get(url, { headers: { 'x-auth-token': token } });
            let invoicesRes = [];
            if (response.data && Array.isArray(response.data)) invoicesRes = response.data;
            else if (response.data && Array.isArray(response.data.data)) invoicesRes = response.data.data;

            const processedInvoices = invoicesRes.map(invoice => ({ ...invoice, _doc: invoice._doc || invoice }));
            setAllInvoices(processedInvoices);
        } catch (error) {
            console.error('Error fetching invoices:', error);
            toast.error("Could not fetch invoices data.");
            throw error;
        }
    };

    const fetchCreditLineDetailsInternal = async () => {
        try {
            const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/fetchCreditLine/${userId}`, {
                headers: { 'x-auth-token': token },
            });
            setCreditLine(response.data);
            setCreditLineError(null);

            if (response.data?.creditLineStatus === 'ACTIVE') {
                setCreditLineIsActive(true);
                setShowActivationJourneyContent(false);
            } else {
                setCreditLineIsActive(false);
                setShowActivationJourneyContent(true);
            }

        } catch (error) {
            console.error('Error fetching credit line:', error);
            const errorMessage = error.response?.data?.message || 'Failed to fetch credit line.';
            setCreditLineError(errorMessage);
            setCreditLineIsActive(false);
            setShowActivationJourneyContent(true);

            if (error.response?.status === 404) {
                setCreditLine({ creditLineStatus: 'NOT_FOUND' });
            } else {
                setCreditLine(null);
                toast.error(errorMessage);
            }
            if (error.response?.status !== 404) throw error;
        }
    };

    const fetchMerchantOffersInternal = async () => {
        try {
            const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/offers`, {
                headers: { 'x-auth-token': token },
                params: { merchantId: userId }
            });
            if (response.data?.success && Array.isArray(response.data.offers)) {
                setMerchantOffers(response.data.offers);
            } else {
                setMerchantOffers([]);
            }
        } catch (error) {
            console.error('Error fetching offers:', error);
        }
    };

    useEffect(() => {
        if (userInfo?.kyc?.buyers) {
            const processedBuyers = userInfo.kyc.buyers.map(buyer => {
                const buyerInvoices = allInvoices.filter(inv => inv._doc.supplierName === buyer.buyerName || inv._doc.buyerId === buyer._id);
                const pendingInvoicesList = buyerInvoices.filter(inv =>
                    !['PAID', 'REJECTED_ANCHOR', 'REJECTED_LENDER', 'CANCELLED'].includes(inv._doc.status)
                );
                const pendingInvoicesCount = pendingInvoicesList.length;
                const totalInvoiceCount = buyerInvoices.length;
                let dueDate = null;
                let dueAmount = 0;

                if (pendingInvoicesCount > 0) {
                    pendingInvoicesList.sort((a, b) => new Date(a._doc.dueDate) - new Date(b._doc.dueDate));
                    dueDate = pendingInvoicesList[0]._doc.dueDate;
                    dueAmount = pendingInvoicesList.reduce((sum, inv) => sum + (parseFloat(inv._doc.totalAmount) || 0), 0);
                }
                return {
                    ...buyer,
                    id: buyer._id, // Ensure unique key for rows
                    pendingInvoices: pendingInvoicesCount,
                    totalInvoice: totalInvoiceCount,
                    dueDate: dueDate ? new Date(dueDate).toLocaleDateString('en-GB').replace(/\//g, '-') : '-',
                    dueAmount: dueAmount,
                    status: "Verified Buyer"
                };
            });
            let filtered = processedBuyers;
            if (statusFilter !== 'All') {
                filtered = filtered.filter(b => b.status === statusFilter);
            }
            setDisplayedBuyers(filtered.sort((a, b) => a.buyerName.localeCompare(b.buyerName)));
        } else {
            setDisplayedBuyers([]);
        }
    }, [userInfo, allInvoices, statusFilter, ageFilter]);

    // --- Add/View Buyer Modal Logic ---
    const openAddBuyerModal = () => {
        setNewBuyerData({ buyerName: '', registrationNumber: '', contactPerson: '', contactPhone: '', contactEmail: '' });
        setIsViewMode(false); // Set to add mode
        setBuyerModalError(''); // Clear any previous errors
        setIsAddBuyerModalOpen(true);
    };

    const openViewBuyerModal = (buyer) => {
        setNewBuyerData({
            buyerName: buyer.buyerName || '',
            registrationNumber: buyer.registrationNumber || '',
            contactPerson: buyer.contactPerson || '',
            contactPhone: buyer.contactPhone || '',
            contactEmail: buyer.contactEmail || '',
        });
        setIsViewMode(true); // Set to view mode
        setBuyerModalError(''); // Clear any previous errors
        setIsAddBuyerModalOpen(true);
    };

    const closeAddBuyerModal = () => {
        setIsAddBuyerModalOpen(false);
        setNewBuyerData({ buyerName: '', registrationNumber: '', contactPerson: '', contactPhone: '', contactEmail: '' }); // Clear data on close
        setIsViewMode(false); // Reset mode
        setBuyerModalError(''); // Clear any errors
    };

    const handleAddBuyerModalInputChange = (e) => {
        const { name, value } = e.target;
        setNewBuyerData(prev => ({ ...prev, [name]: value }));
    };
    const handleSaveNewBuyer = async (e) => {
        e.preventDefault();
        setBuyerModalError('');
        setIsSavingNewBuyer(true);

        const { buyerName, contactEmail, registrationNumber, contactPerson, contactPhone } = newBuyerData;

        // Basic Validation
        if (!buyerName.trim() || !contactEmail.trim()) {
            const msg = "Buyer Name and Contact Email are required.";
            setBuyerModalError(msg);
            toast.error(msg);
            setIsSavingNewBuyer(false);
            return;
        }
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(contactEmail.trim())) {
            const msg = "Invalid email address format.";
            setBuyerModalError(msg);
            toast.error(msg);
            setIsSavingNewBuyer(false);
            return;
        }

        const currentBuyersFromState = userInfo?.kyc?.buyers || [];

        // Comprehensive Duplicate Check (compare against existing buyers in userInfo)
        // A buyer is considered a duplicate if both buyerName and contactEmail match an existing buyer.
        const isDuplicate = currentBuyersFromState.some(
            b => b.buyerName?.trim().toLowerCase() === buyerName.trim().toLowerCase() ||
                b.contactEmail?.trim().toLowerCase() === contactEmail.trim().toLowerCase()
        );

        if (isDuplicate) {
            const msg = "A buyer with this name or email already exists.";
            setBuyerModalError(msg);
            toast.error(msg);
            setIsSavingNewBuyer(false);
            return;
        }

        const buyerPayload = {
            buyerName: buyerName.trim(),
            contactEmail: contactEmail.trim(),
            registrationNumber: registrationNumber?.trim() || null,
            contactPerson: contactPerson?.trim() || null,
            contactPhone: contactPhone?.trim() || null,
        };

        // Append the new buyer to the existing list to send the full updated list
        const updatedBuyersListForAPI = [...currentBuyersFromState, buyerPayload];

        try {
            // Using the updateKyc service (assuming it's correctly imported and handles the API call)
            const API_ENDPOINT = `${config.apiUrl}/ops/invoiceFinancing/updateKyc`;

            const response = await axios.post(API_ENDPOINT, {
                userId, kyc: {
                    buyers: updatedBuyersListForAPI,
                }
            }, {
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            console.log(response)

            if (!response.data.user || !response.data.user.kyc || !Array.isArray(response.data.user.kyc.buyers)) {
                throw new Error("Invalid response from buyer save API.");
            }

            toast.success("New buyer added successfully!");
            // Update local userInfo with the fresh data from the API response
            setUserInfo(prev => ({ ...prev, kyc: { ...prev.kyc, buyers: response.data.user.kyc.buyers } }));

            // Find the newly added buyer from the *updated list returned by the API* to get its _id
            const addedBuyerDetailsFromAPI = response.data.user.kyc.buyers.find(
                b => b.contactEmail.trim().toLowerCase() === newBuyerData.contactEmail.toLowerCase() &&
                    b.buyerName.trim().toLowerCase() === newBuyerData.buyerName.toLowerCase() &&
                    !currentBuyersFromState.some(cb => cb._id === b._id) // Ensure it's genuinely the new one
            );

            // Send invitation only for the newly added buyer if it exists and has an email
            if (userEmail && addedBuyerDetailsFromAPI?.contactEmail) {
                try {
                    // Assuming sendBuyerInvitationEmail is defined and accessible in this scope
                    // It expects msmeEmail, buyerEmail, buyerId (optional), and emailSubject (optional)
                    await sendBuyerInvitationEmail(userEmail, addedBuyerDetailsFromAPI.contactEmail, addedBuyerDetailsFromAPI._id, `Invitation to connect with ${user.companyName || user.email}`);
                    toast.info(`Invitation sent to ${addedBuyerDetailsFromAPI.contactEmail}`);
                } catch (emailError) {
                    console.error(`Failed to send invitation to ${addedBuyerDetailsFromAPI.contactEmail}:`, emailError);
                    toast.warn(`Buyer added, but invitation failed for ${addedBuyerDetailsFromAPI.contactEmail}.`);
                }
            }
            closeAddBuyerModal();
        } catch (error) {
            console.error("Error saving new buyer:", error);
            const errorMsg = error.response?.data?.message || error.message || "Could not save new buyer.";
            setBuyerModalError(errorMsg);
            toast.error(errorMsg);
        } finally {
            setIsSavingNewBuyer(false);
        }
    };

    // The old handleSaveBuyers method is no longer needed if all buyer saving is done via handleSaveNewBuyer
    // or if handleSaveNewBuyer specifically handles adding a single buyer and the other method handles bulk updates.
    // Given the request, it seems handleSaveNewBuyer is intended to replace the functionality of adding a single buyer,
    // which the original handleSaveBuyers also seemed to cover within its broader scope.
    // If handleSaveBuyers is used for a different UI flow (e.g., a table where multiple buyers can be edited/added),
    // then it would remain, but its logic for identifying new buyers might be simplified if single additions are done here.
    // For now, I'm providing the replacement for handleSaveNewBuyer as requested.

    const getCreditLineStatusColor = (status) => {
        if (!status) return 'bg-gray-200 text-gray-800';
        switch (status.toUpperCase()) {
            case 'ACTIVE': return 'bg-[#3dc285] text-white border border-green-300';
            case 'APPROVED': return 'bg-blue-100 text-blue-700 border border-blue-300';
            case 'UNDER_REVIEW': return 'bg-yellow-100 text-yellow-700 border border-yellow-300';
            case 'REJECTED': case 'SUSPENDED': case 'EXPIRED': return 'bg-red-100 text-red-700 border border-red-300';
            case 'NOT_FOUND': return 'bg-gray-100 text-gray-600 border border-gray-300';
            default: return 'bg-gray-200 text-gray-700 border border-gray-300';
        }
    };

    const shouldShowCreditLineDetails = () => creditLine && (creditLine.creditLineStatus === 'ACTIVE' || (creditLine.creditLineStatus === 'APPROVED' && creditLine.offerAccepted === true));

    const handleOpenContractModalCL = () => {
        if (acceptedLenderDetails?.contractUrl) {
            setContractPdfUrlCL(acceptedLenderDetails.contractUrl);
            setShowContractModalCL(true);
        } else {
            toast.warn("No credit line contract document available.");
        }
    };

    const utilizationPercent = creditLine?.creditLimit > 0 ? ((creditLine.utilizedAmount / creditLine.creditLimit) * 100) : 0;
    const availablePercent = 100 - utilizationPercent;

    const renderAddViewBuyerModal = () => (
        isAddBuyerModalOpen && (
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-4 transition-opacity duration-300">
                <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-lg relative transform transition-all duration-300 scale-100">
                    <button onClick={closeAddBuyerModal} className="absolute top-3 right-3 text-gray-400 hover:text-gray-600" disabled={isSavingNewBuyer}>
                        <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                    </button>
                    <h3 className="text-lg font-medium mb-4 text-gray-900">{isViewMode ? 'Buyer Details' : 'Add New Buyer'}</h3>
                    {buyerModalError && (
                        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                            <span className="block sm:inline">{buyerModalError}</span>
                        </div>
                    )}
                    <form onSubmit={handleSaveNewBuyer}>
                        <div className="space-y-3">
                            <div>
                                <label htmlFor="buyerName" className="block text-sm font-medium text-gray-700">Buyer Name {isViewMode ? '' : <span className="text-red-500">*</span>}</label>
                                <input type="text" name="buyerName" id="buyerName" value={newBuyerData.buyerName} onChange={handleAddBuyerModalInputChange} required={!isViewMode} readOnly={isViewMode} className={`mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-1.5 px-3 text-sm ${isViewMode ? 'bg-gray-100 cursor-not-allowed' : 'focus:ring-indigo-500 focus:border-indigo-500'}`} />
                            </div>
                            <div>
                                <label htmlFor="registrationNumber" className="block text-sm font-medium text-gray-700">CR Number</label>
                                <input type="text" name="registrationNumber" id="registrationNumber" value={newBuyerData.registrationNumber} onChange={handleAddBuyerModalInputChange} readOnly={isViewMode} className={`mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-1.5 px-3 text-sm ${isViewMode ? 'bg-gray-100 cursor-not-allowed' : 'focus:ring-indigo-500 focus:border-indigo-500'}`} />
                            </div>
                            <div>
                                <label htmlFor="contactPerson" className="block text-sm font-medium text-gray-700">Contact Person</label>
                                <input type="text" name="contactPerson" id="contactPerson" value={newBuyerData.contactPerson} onChange={handleAddBuyerModalInputChange} readOnly={isViewMode} className={`mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-1.5 px-3 text-sm ${isViewMode ? 'bg-gray-100 cursor-not-allowed' : 'focus:ring-indigo-500 focus:border-indigo-500'}`} />
                            </div>
                            <div>
                                <label htmlFor="contactPhone" className="block text-sm font-medium text-gray-700">Contact Number</label>
                                <input type="tel" name="contactPhone" id="contactPhone" value={newBuyerData.contactPhone} onChange={handleAddBuyerModalInputChange} readOnly={isViewMode} className={`mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-1.5 px-3 text-sm ${isViewMode ? 'bg-gray-100 cursor-not-allowed' : 'focus:ring-indigo-500 focus:border-indigo-500'}`} />
                            </div>
                            <div>
                                <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700">Contact Email {isViewMode ? '' : <span className="text-red-500">*</span>}</label>
                                <input type="email" name="contactEmail" id="contactEmail" value={newBuyerData.contactEmail} onChange={handleAddBuyerModalInputChange} required={!isViewMode} readOnly={isViewMode} className={`mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-1.5 px-3 text-sm ${isViewMode ? 'bg-gray-100 cursor-not-allowed' : 'focus:ring-indigo-500 focus:border-indigo-500'}`} />
                            </div>
                        </div>
                        <div className="mt-5 sm:mt-6 flex justify-end space-x-3">
                            <button type="button" onClick={closeAddBuyerModal} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">Close</button>
                            {!isViewMode && (
                                <button type="submit" disabled={isSavingNewBuyer} className="px-4 py-2 text-sm font-medium text-white bg-[#63ac67] border border-transparent rounded-md shadow-sm hover:bg-[#005a5a] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#004141] flex items-center justify-center min-w-[100px]">
                                    {isSavingNewBuyer ? <ArrowPathIcon className="w-4 h-4 animate-spin" /> : 'Save Buyer'}
                                </button>
                            )}
                        </div>
                    </form>
                </div>
            </div>
        )
    );

    const renderContractModalForCL = () => (
        showContractModalCL && contractPdfUrlCL && (
            <div className="fixed inset-0 bg-black bg-opacity-70 flex justify-center items-center z-[101] p-4">
                <div className="bg-white p-5 rounded-lg shadow-xl w-full max-w-4xl h-[90vh] flex flex-col">
                    <div className="flex justify-between items-center mb-4 border-b pb-3">
                        <h3 className="text-xl font-semibold text-gray-800">Credit Line Contract</h3>
                        <button onClick={() => setShowContractModalCL(false)} className="text-gray-500 hover:text-gray-800 p-1" aria-label="Close">
                            <XCircleIcon className="w-7 h-7" />
                        </button>
                    </div>
                    <div className="flex-grow h-[calc(100%-4rem)]">
                        <iframe src={contractPdfUrlCL} className="w-full h-full border-0" title="Credit Line Contract PDF" />
                    </div>
                </div>
            </div>
        )
    );

    if (isLoadingPage && !userInfo) return <LoadingModal />;
    if (pageError && !userInfo) return <div className="min-h-screen flex justify-center items-center p-6 text-red-600 bg-red-50 rounded-md border border-red-300 text-center shadow-md"><ExclamationTriangleIcon className="h-12 w-12 text-red-500 mx-auto mb-3" /><p className="text-lg font-medium">Loading Error</p>{pageError}</div>;

    const userFirstName = user?.firstName || user?.companyName?.split(' ')[0] || user?.contactPerson?.split(' ')[0] || 'User';

    return (
        <div className="min-h-screen bg-gray-100">
            {/* Header - Full Width */}
            <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 pt-4 flex flex-col sm:flex-row justify-between items-center gap-3">
                <h1 className="text-xl font-bold text-gray-800 mb-3 sm:mb-0">Welcome, {userFirstName}</h1> {/* Added margin-bottom for mobile */}
                <button onClick={openAddBuyerModal} className="bg-[#139d54] text-white px-4 py-2 rounded-md shadow hover:bg-opacity-90 transition-colors flex items-center text-sm font-medium w-full sm:w-auto justify-center" disabled={!userInfo} title={!userInfo ? "Loading..." : "Add a new buyer"}>
                    <PlusIcon className="h-5 w-5 mr-2" /> Add New Buyer
                </button>
            </div>

            {/* Main Content Area - Full Width */}
            <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

                    {/* Left Column */}
                    <div className="lg:col-span-1 space-y-6">
                        {loadingCLDetails && !creditLine ? (
                            <div className="bg-white p-6 rounded-lg shadow-lg"><Loader /></div>
                        ) : creditLine ? (
                            shouldShowCreditLineDetails() ? (
                                <div className="bg-white rounded-lg shadow-lg p-6">
                                    <div className="flex justify-between items-center mb-4 md:cursor-default cursor-pointer" onClick={(e) => window.innerWidth < 768 && setIsCreditLineOverviewOpen(!isCreditLineOverviewOpen)}>
                                        <h2 className="text-lg font-semibold text-gray-800">Credit Line Overview</h2>
                                        <div className="flex items-center">
                                            <span className={`px-3 py-1 rounded-md text-xs font-medium ${getCreditLineStatusColor(creditLine.creditLineStatus)}`}>{creditLine.creditLineStatus.replace(/_/g, ' ')}</span>
                                            <ChevronDownIcon className={`ml-2 h-5 w-5 text-gray-500 transform transition-transform duration-300 md:hidden ${isCreditLineOverviewOpen ? 'rotate-180' : 'rotate-0'}`} />
                                        </div>
                                    </div>
                                    <div className="border border-gray-300 bg-gray-50 rounded-lg p-4 mb-4">
                                        <div className="grid grid-cols-3 gap-2 text-center sm:text-left">
                                            {[
                                                { label: "Interest Rate", value: `${creditLine.interestRate ?? 'N/A'}%` },
                                                { label: "Tenure", value: `${creditLine.tenure ?? 'N/A'} Days` },
                                                { label: "Processing Fee", value: creditLine.processingFeeType === 'percentage' ? `${creditLine.processingFee}%` : formatCurrency(creditLine.processingFee || 0) }
                                            ].map(item => (
                                                <div key={item.label}>
                                                    <p className="text-xs text-gray-500 mb-1">{item.label}</p>
                                                    <p className="font-semibold text-gray-800 text-sm">{item.value}</p>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                    <div className={`overflow-hidden transition-all duration-500 ease-in-out md:max-h-none md:opacity-100 md:pt-4 ${isCreditLineOverviewOpen ? 'max-h-[500px] opacity-100 pt-4' : 'max-h-0 opacity-0'}`}>
                                        <div className="mb-4">
                                            <div className="flex justify-between items-center mb-1">
                                                <span className="text-xs text-gray-600">Credit Available</span>
                                                <span className="text-xs font-medium">{`${availablePercent.toFixed(0)}% Available`}</span>
                                            </div>
                                            <div className="h-3 bg-red-100 rounded-full overflow-hidden w-full"><div style={{ width: `${availablePercent}%` }} className="h-full bg-green-500 rounded-full transition-all duration-500"></div></div>
                                            <p className="text-[10px] text-gray-500 mt-1 text-right">{formatCurrency(creditLine?.availableBalance ?? (creditLine?.creditLimit || 0) * (availablePercent / 100))} Available</p>
                                        </div>
                                        <hr className="border-t border-gray-200 my-4" />
                                        {acceptedLenderDetails && (
                                            <>
                                                <div className="flex items-center justify-between mb-4">
                                                    <div className="flex items-center">
                                                        {acceptedLenderDetails.logoUrl ? <img src={acceptedLenderDetails.logoUrl} alt={`${acceptedLenderDetails.lenderName} logo`} className="h-10 w-10 object-contain rounded-full mr-3 border" /> : <div className="h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center text-gray-500 text-xs mr-3 p-1 text-center">No Logo</div>}
                                                        <div>
                                                            <p className="font-semibold text-xs text-gray-900">{acceptedLenderDetails.lenderName}</p>
                                                            <p className="text-xs text-gray-600">Bank</p>
                                                        </div>
                                                    </div>
                                                    {acceptedLenderDetails.contractUrl && <button onClick={handleOpenContractModalCL} className="px-2 py-1 bg-[#00393b] text-white text-xs font-medium rounded-md hover:bg-[#002a2c] transition-colors">View Contract</button>}
                                                </div>
                                                <div className="bg-gray-50 border border-gray-300 rounded-lg p-3">
                                                    <p className="text-xs text-gray-500 mb-1">Contact Person</p>
                                                    <p className="font-semibold text-gray-800 text-sm">{acceptedLenderDetails.contactPersonName}</p>
                                                    <p className="text-xs text-gray-700 break-all">{acceptedLenderDetails.contactPersonEmail}</p>
                                                </div>
                                            </>
                                        )}
                                    </div>
                                </div>
                            ) : ( // Credit line exists but not active/approved for details view
                                <div className="bg-white rounded-lg shadow-lg p-6 text-center text-gray-600">
                                    <h2 className="text-md font-semibold text-gray-800 mb-2">Credit Line Status</h2>
                                    <span className={`inline-block px-3 py-1 my-2 rounded-md text-sm font-medium ${getCreditLineStatusColor(creditLine?.creditLineStatus)}`}>{creditLine?.creditLineStatus?.replace(/_/g, ' ') || 'Not Available'}</span>
                                    {creditLine?.creditLineStatus === 'NOT_FOUND' && <p className="text-sm mt-1">No credit line application found.</p>}
                                    {creditLine?.creditLineStatus === 'UNDER_REVIEW' && <p className="text-sm mt-1">Your credit line application is under review.</p>}
                                    {showActivationJourneyContent && creditLine?.creditLineStatus !== 'ACTIVE' && creditLine?.creditLineStatus !== 'NOT_FOUND' && (
                                        <p className="text-sm mt-2 italic">Your credit line is not yet active. Please complete any pending steps.</p>
                                    )}
                                    {creditLineError && creditLine?.creditLineStatus !== 'NOT_FOUND' && <p className="text-sm text-red-500 mt-2">{creditLineError}</p>}
                                </div>
                            )
                        ) : ( // No credit line data loaded (or error state handled by loadingCLDetails check)
                            <div className="bg-white rounded-lg shadow-lg p-6 text-center text-gray-500"><p>Credit line information is currently unavailable.</p></div>
                        )}
                        <div className="bg-[#27c686] text-white rounded-lg shadow p-4 md:p-6">
                            <div className="flex items-center justify-center md:justify-start mb-3 md:mb-4 md:pl-4">
                                <img src={briefcaseMoney} alt="Briefcase with money" className="h-24 md:h-32 w-auto" />
                            </div>
                            <h2 className="text-lg md:text-xl font-bold mb-2 text-center md:text-left">Instant cash from your invoices</h2>
                            <p className="text-sm text-center md:text-left">Turn your unpaid invoices into immediate working capital. Access funds in a few clicks—no waiting, no hassle.</p>
                        </div>
                    </div>

                    {/* Right Column */}
                    <div className="lg:col-span-2 space-y-6">
                        <div className="bg-white rounded-lg shadow-lg">
                            <div className="p-4 sm:p-6 border-b border-gray-200 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                                <h2 className="text-xl font-semibold text-gray-800">My Buyers</h2>
                                <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                                    {[{ label: "Status", value: statusFilter, setter: setStatusFilter, options: ["All", "Verified Buyer"] }, { label: "Age", value: ageFilter, setter: setAgeFilter, options: ["All", "Recent", "Old"] }].map(f => (
                                        <div key={f.label} className="w-full sm:w-auto">
                                            <label htmlFor={`buyer${f.label}Filter`} className="sr-only">{f.label}</label>
                                            <select id={`buyer${f.label}Filter`} value={f.value} onChange={(e) => f.setter(e.target.value)} className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-[#004141] focus:border-transparent text-sm">
                                                {f.options.map(opt => <option key={opt} value={opt}>{f.label}: {opt}</option>)}
                                            </select>
                                        </div>
                                    ))}
                                </div>
                            </div>
                            <div className="p-2 sm:p-4 bg-gray-200">
                                <div className="overflow-x-auto bg-white rounded-md shadow-sm">
                                    <table className="w-full min-w-[700px]">
                                        <thead className="bg-gray-50">
                                            <tr>{['Buyer Name', 'Pending Invoices', 'Total Invoices', 'Next Due Date', 'Total Due Amt.', 'Status', 'Actions'].map(h => <th key={h} className={`px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${h.includes('Amt') || h.includes('Invoices') ? 'text-right sm:text-left' : ''}`}>{h}</th>)}</tr>
                                        </thead>
                                        <tbody className="divide-y divide-gray-200">
                                            {isLoadingPage && displayedBuyers.length === 0 ? (
                                                <tr><td colSpan="7" className="p-6 text-center"><Loader /></td></tr>
                                            ) : !isLoadingPage && displayedBuyers.length === 0 ? (
                                                <tr><td colSpan="7" className="px-6 py-10 text-center text-sm text-gray-500">{userInfo?.kyc?.buyers?.length > 0 ? "No buyers match filters." : "No buyers added yet. Click 'Add New Buyer' to start."}</td></tr>
                                            ) : (
                                                displayedBuyers.map(buyer => (
                                                    <tr key={buyer.id} className="hover:bg-gray-50">
                                                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{buyer.buyerName}</td>
                                                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700 text-right sm:text-center">{buyer.pendingInvoices}</td>
                                                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700 text-right sm:text-center">{buyer.totalInvoice}</td>
                                                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">{buyer.dueDate}</td>
                                                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700 text-right">{formatCurrency(buyer.dueAmount)}</td>
                                                        <td className="px-4 py-4 whitespace-nowrap"><span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-[#3CC185] text-white">{buyer.status}</span></td>
                                                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-center">
                                                            <button onClick={() => openViewBuyerModal(buyer)} className="text-[#199d4f] hover:text-teal-700 hover:underline">View Detail</button>
                                                        </td>
                                                    </tr>
                                                ))
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {renderAddViewBuyerModal()}
            {renderContractModalForCL()}
            {isSavingNewBuyer && <GeneralLoadingModal title="Adding Buyer" message="Please wait while we save buyer details and send an invitation." />}
        </div>
    );
};

export default MyBuyersPage;