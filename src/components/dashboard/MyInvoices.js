import React, { useState, useEffect, useRef } from 'react';
import { useLocation, useHistory } from 'react-router-dom';
import axios from 'axios';
import config from '../../config.json';
import SharedCache from '../../sharedCache';
import { uploadKycDocument, uploadAdditionalInvoiceDocument } from '../../api/kyc';
import { ExclamationTriangleIcon, EyeIcon } from '@heroicons/react/24/outline';
import { ArrowPathIcon, DocumentArrowUpIcon, LinkIcon, XCircleIcon, XMarkIcon, ArrowUturnLeftIcon, PlusIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import { shortenDocumentName } from '../../components/utils';
import { parseISO, formatDistanceToNow, differenceInDays } from 'date-fns';
import { getKycInfo, updateKyc } from '../../api/kyc';
import stringSimilarity from 'string-similarity';
import { validateDocumentType } from '../../utils/documentValidator';
import { sendBuyerInvitationEmail } from '../../api/registration'; //  ADD THIS LINE (adjust path if needed)
import { toast } from 'react-toastify';
import LoadingModal from '../Reusable/Loading';

// Helper function to calculate invoice age
const calculateInvoiceAge = (dateString) => {
  if (!dateString) return 'N/A';
  try {
    const date = parseISO(dateString); // Parse the ISO string (e.g., "2025-04-03T09:33:17.697Z")
    return formatDistanceToNow(date, { addSuffix: true }); // e.g., "about 2 hours ago"
  } catch (error) {
    console.error("Error parsing date for age calculation:", dateString, error);
    return 'Invalid Date';
  }
};

const Loader = () => (
  <div className="flex items-center justify-center">
    <div className="w-12 h-12 border-4 border-[#004141] border-t-transparent rounded-full animate-spin"></div>
  </div>
);

// Helper function to parse "Month Day, Year" string to "YYYY-MM-DD"
// or handle already formatted YYYY-MM-DD strings, or ISO strings.
const formatDateToYYYYMMDD = (dateString) => {
  if (!dateString) return '';
  // Check if already YYYY-MM-DD
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
    return dateString;
  }
  try {
    const date = new Date(dateString); // Handles "Feb 12, 2025" and ISO strings
    if (isNaN(date.getTime())) return ''; // Invalid date
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  } catch (e) {
    console.error("Error parsing date string for YYYY-MM-DD:", dateString, e);
    return '';
  }
};

// GeneralLoadingModal Component (can be in a separate file and imported)
const GeneralLoadingModal = ({ title, message }) => {
  return <LoadingModal />
};

const MyInvoicesPage = () => {
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showPdfPreview, setShowPdfPreview] = useState(false);
  const [pdfUrl, setPdfUrl] = useState('');
  const [invoices, setInvoices] = useState([]);
  const [mindeeData, setMindeeData] = useState(null);
  const [extractingData, setExtractingData] = useState(false);
  const location = useLocation();
  const [creditLine, setCreditLine] = useState(null);
  const [creditLineError, setCreditLineError] = useState(null);
  const [applyButtonText, setApplyButtonText] = useState('Submit for Discounting');
  const [applyButtonDisabled, setApplyButtonDisabled] = useState(false);
  const [applyButtonReason, setApplyButtonReason] = useState('');
  const [userInfo, setUserInfo] = useState(null); // To store the fetched KYC data
  // Inside MyInvoicesPage component
  const [showNewInitialUploadModal, setShowNewInitialUploadModal] = useState(false);
  const [showGeneralLoadingModal, setShowGeneralLoadingModal] = useState(false);
  const [generalLoadingTitle, setGeneralLoadingTitle] = useState('');
  const [generalLoadingMessage, setGeneralLoadingMessage] = useState('');
  const initialInvoiceFileRef = useRef(null); // For the new initial upload modal's file input
  const MAX_INVOICE_FILE_SIZE_MB = 20;
  const MAX_INVOICE_FILE_SIZE_BYTES = MAX_INVOICE_FILE_SIZE_MB * 1024 * 1024;
  const [viewingInvoice, setViewingInvoice] = useState(null); // Holds the full invoice object being previewed
  const [showViewModal, setShowViewModal] = useState(false); // Controls the invoice view modal
  const [isUploadingAdditionalDoc, setIsUploadingAdditionalDoc] = useState(false);
  const [additionalDocError, setAdditionalDocError] = useState('');
  const additionalFileInputRef = useRef(null); // Ref for the additional doc input
  const [isBuyerValid, setIsBuyerValid] = useState(true); // Assume valid until proven otherwise
  const [buyerValidationError, setBuyerValidationError] = useState('');
  const [statusFilter, setStatusFilter] = useState(''); // '' means 'All Statuses'
  const [ageFilter, setAgeFilter] = useState('');     // '' means 'Any Age'
  const [filteredInvoices, setFilteredInvoices] = useState([]);
  // Inside MyInvoicesPage component, add these state variables:
  const [offersData, setOffersData] = useState([]); // To store fetched offers
  const [showContractModal, setShowContractModal] = useState(false); // Controls contract modal visibility
  const [contractPdfUrl, setContractPdfUrl] = useState(''); // Holds the PDF URL for the contract modal
  const [offersLoading, setOffersLoading] = useState(false); // Optional: loading state for offers
  const [offersError, setOffersError] = useState('');     // Optional: error state for offers
  const [acceptedLenderDetails, setAcceptedLenderDetails] = useState(null);
  const [contractPdfUrlCL, setContractPdfUrlCL] = useState('');
  const [showContractModalCL, setShowContractModalCL] = useState(false)
  const [showReUploadModal, setShowReUploadModal] = useState(false);
  const [reUploadInvoiceId, setReUploadInvoiceId] = useState(null); // Store the ID of the invoice needing re-upload
  const [isReUploading, setIsReUploading] = useState(false);
  const [reUploadError, setReUploadError] = useState('');
  const reUploadFileInputRef = useRef(null);
  const [additionalFilesToUpload, setAdditionalFilesToUpload] = useState([]); // Holds File objects for Step 2
  const step2FileInputRef = useRef(null);
  // Add this state variable inside the MyInvoicesPage component
  const [selectedBuyerIdForUpload, setSelectedBuyerIdForUpload] = useState('');
  // Inside MyInvoicesPage component, add these state variables:
  const [showBuyersModal, setShowBuyersModal] = useState(false);
  const [modalBuyers, setModalBuyers] = useState([]); // Buyers currently being edited in the modal
  const [newBuyer, setNewBuyer] = useState({ // State for the "Add New Buyer" form
    buyerName: '',
    contactEmail: '',
    contactPerson: '',
    registrationNumber: '',
    contactPhone: '',
  });
  const [originalExtractedBuyerName, setOriginalExtractedBuyerName] = useState(''); // To store the initial Mindee extraction
  const [selectedKycBuyerId, setSelectedKycBuyerId] = useState(''); // ID of the buyer chosen in the dropdown
  const [buyerNameFieldWarning, setBuyerNameFieldWarning] = useState(''); // Specific warning for the buyer dropdown field
  const [isBuyerDropdownEditable, setIsBuyerDropdownEditable] = useState(false); // Controls editability of the dropdown
  const [isSavingBuyers, setIsSavingBuyers] = useState(false);
  const [buyersModalError, setBuyersModalError] = useState('');
  const [isCreditLineOverviewOpen, setIsCreditLineOverviewOpen] = useState(true);
  const getCreditLineStatusColor = (status) => {
    if (!status) return 'bg-gray-200 text-gray-800';
    switch (status.toUpperCase()) {
      case 'ACTIVE': return 'bg-[#3dc285] text-white border border-green-300';
      case 'APPROVED': return 'bg-blue-100 text-blue-700 border border-blue-300';
      case 'UNDER_REVIEW': return 'bg-yellow-100 text-yellow-700 border border-yellow-300';
      case 'REJECTED': case 'SUSPENDED': case 'EXPIRED': return 'bg-red-100 text-red-700 border border-red-300';
      case 'NOT_FOUND': return 'bg-gray-100 text-gray-600 border border-gray-300';
      default: return 'bg-gray-200 text-gray-700 border border-gray-300';
    }
  };
  const user = SharedCache.get("user") || {};
  const userId = user._id || user.id || "";
  const token = SharedCache.get("token") || "placeholdertoken";

  // --- START: Add Handler for opening Re-upload Modal ---
  const handleOpenReUploadModal = (invoiceId) => {
    const invoiceToReUpload = invoices.find(inv => inv._doc._id === invoiceId);
    if (invoiceToReUpload) {
      setReUploadInvoiceId(invoiceId);
      setShowReUploadModal(true);
      setReUploadError(''); // Clear previous errors
      setIsReUploading(false); // Reset loading state
    } else {
      console.error("Could not find invoice details for ID:", invoiceId);
      alert("Error: Could not load invoice details for re-upload.");
    }
  };
  // --- END: Add Handler for opening Re-upload Modal ---
  const userFirstName = user?.firstName || user?.companyName?.split(' ')[0] || user?.contactPerson?.split(' ')[0] || 'User';

  // --- START: Add Handler for actual re-upload ---
  const handleReUploadAdditionalDocument = async (event) => {
    const file = event.target.files[0];
    if (!file || !reUploadInvoiceId) {
      return;
    }

    // Reset file input
    event.target.value = null;

    // Validate if the document is appropriate (not a resume, etc.)
    // We're using a generic validation here since additional documents can be of various types
    // but we still want to catch obvious mistakes like uploading a resume
    const validation = validateDocumentType(file, 'otherDocument');
    if (!validation.isValid) {
      setReUploadError(validation.message || "This document appears to be of the wrong type. Please upload an appropriate supporting document.");
      return;
    }

    setIsReUploading(true);
    setReUploadError('');

    try {
      const result = await uploadAdditionalInvoiceDocument(reUploadInvoiceId, file);

      if (result.success) {
        console.log("Additional document re-uploaded:", result.document);
        // Refresh invoice list to get the updated data
        await fetchInvoices(); // This will update the 'invoices' state
        // The useEffect hook watching 'invoices' will update viewingInvoice if the preview modal is open
        // For the re-upload modal, we just need the list refreshed.
        // Optionally, close the re-upload modal after successful upload:
        // setShowReUploadModal(false);
        // setReUploadInvoiceId(null);
      } else {
        throw new Error(result.error || "Re-upload failed");
      }
    } catch (error) {
      console.error("Error in handleReUploadAdditionalDocument:", error);
      setReUploadError(error.message || "Failed to re-upload document. Please try again.");
    } finally {
      setIsReUploading(false);
    }
  };
  // --- END: Add Handler for actual re-upload ---

  const [uploadStep, setUploadStep] = useState(1);
  // const [additionalDoc1, setAdditionalDoc1] = useState(null);
  // const [additionalDoc2, setAdditionalDoc2] = useState(null);
  // const [additionalDoc1Name, setAdditionalDoc1Name] = useState('');
  // const [additionalDoc2Name, setAdditionalDoc2Name] = useState('');
  // const [additionalDoc1UploadStatus, setAdditionalDoc1UploadStatus] = useState('');
  // const [additionalDoc2UploadStatus, setAdditionalDoc2UploadStatus] = useState('');
  const [ConsentAccepted, setConsentAccepted] = useState(false);
  const [editingInvoice, setEditingInvoice] = useState(null);

  const fileInputRef = useRef(null);

  const handleStep2FileSelect = (event) => {
    const files = Array.from(event.target.files);
    if (!files.length) return;

    // Validate each file to ensure it's not a resume or inappropriate document type
    const validatedFiles = [];
    const invalidFiles = [];

    for (const file of files) {
      // Check if file is already in the list
      const isDuplicate = additionalFilesToUpload.some(
        existingFile => existingFile.name === file.name && existingFile.size === file.size
      );

      if (isDuplicate) continue;

      // Validate the file type
      const validation = validateDocumentType(file, 'otherDocument');
      if (validation.isValid) {
        validatedFiles.push(file);
      } else {
        invalidFiles.push({ file, message: validation.message });
      }
    }

    // Show warnings for invalid files
    if (invalidFiles.length > 0) {
      const invalidFileNames = invalidFiles.map(item => `"${item.file.name}"`).join(', ');
      alert(`The following files appear to be of the wrong type and were not added: ${invalidFileNames}. Please upload appropriate supporting documents.`);
    }

    // Add valid files to the list
    const combined = [...additionalFilesToUpload, ...validatedFiles];

    if (combined.length > 10) {
      alert(`You can only select up to 10 additional documents. ${combined.length - 10} files were ignored.`);
      setAdditionalFilesToUpload(combined.slice(0, 10)); // Keep only the first 10
    } else {
      setAdditionalFilesToUpload(combined);
    }

    // Reset file input to allow selecting the same file again if removed
    event.target.value = null;
  };

  const removeFileFromStep2 = (indexToRemove) => {
    setAdditionalFilesToUpload(prevFiles => prevFiles.filter((_, index) => index !== indexToRemove));
  };

  // Inside MyInvoicesPage component:

  useEffect(() => {
    const checkDuplicateInvoice = async (invoiceDataForDuplicateCheck) => {
      // ... (your existing duplicate check logic - ensure it sets isBuyerValid=false and buyerValidationError on duplicate)
      // For brevity, assuming it returns true if duplicate, false otherwise.
      if (!invoiceDataForDuplicateCheck.invoiceNumber || !invoiceDataForDuplicateCheck.supplierName || !invoiceDataForDuplicateCheck.totalAmount || !token) {
        return false;
      }
      try {
        const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/checkDuplicateInvoice`, { /* ... */ });
        if (response.data?.isDuplicate) {
          setBuyerValidationError("DUPLICATE_INVOICE: This invoice appears to be a duplicate and has already been submitted. Please upload a different invoice or contact support.");
          setIsBuyerValid(false); // Crucial: Mark as invalid
          return true;
        }
        return false;
      } catch (error) { console.error('Error checking duplicate:', error); return false; }
    };

    // Only run this core matching logic if we are in Step 1, not editing an existing persisted invoice,
    // and we have the necessary data (userInfo and mindeeData).
    if (uploadStep === 1 && !editingInvoice && userInfo && mindeeData) {
      // Store original extracted name ONCE when mindeeData first becomes available with a supplierName.
      if (!originalExtractedBuyerName && mindeeData.supplierName) {
        setOriginalExtractedBuyerName(mindeeData.supplierName);
      }

      // Use originalExtractedBuyerName for matching if available, otherwise current from mindeeData
      const nameToMatch = originalExtractedBuyerName || mindeeData.supplierName;
      const kycBuyerList = userInfo.kyc?.buyers || [];
      const normalize = (name) => name?.trim().replace(/\s+/g, ' ').toLowerCase();
      let localIsBuyerValid = true;
      let localBuyerValidationError = '';
      let localSelectedKycBuyerId = ''; // Assume we need to find a match
      let localIsBuyerDropdownEditable = true; // Default to editable
      let localMindeeSupplierName = nameToMatch; // Start with the name from invoice

      if (kycBuyerList.length > 0) {
        if (nameToMatch) {
          const namesForSimilarity = kycBuyerList.map(b => normalize(b.buyerName));
          const { bestMatch } = stringSimilarity.findBestMatch(normalize(nameToMatch), namesForSimilarity);
          const SIMILARITY_THRESHOLD = 0.85;
          const matchedKycBuyer = kycBuyerList.find(b => normalize(b.buyerName) === bestMatch.target);

          if (matchedKycBuyer && bestMatch.rating >= SIMILARITY_THRESHOLD) {
            localSelectedKycBuyerId = matchedKycBuyer._id;
            localMindeeSupplierName = matchedKycBuyer.buyerName; // Use the name from KYC
            localIsBuyerValid = true;
            localBuyerValidationError = ''; // Matched
            localIsBuyerDropdownEditable = false; // Lock it
            setBuyerNameFieldWarning(''); // Clear specific warnings
          } else {
            localIsBuyerValid = false;
            localBuyerValidationError = `Extracted Buyer: "${nameToMatch || 'N/A'}". Does not closely match an approved buyer. Please select or Add/Manage.`;
            // localSelectedKycBuyerId remains empty, forcing user choice
            localIsBuyerDropdownEditable = true;
          }
        } else { // No buyer name extracted from invoice
          localIsBuyerValid = false;
          localBuyerValidationError = 'Buyer name not extracted from invoice. Please select or use Add/Manage Buyers.';
          localIsBuyerDropdownEditable = true;
        }
      } else { // No buyers in KYC profile
        localIsBuyerValid = false;
        localBuyerValidationError = 'No buyers in your KYC profile. Please use "Add/Manage Buyers".';
        localIsBuyerDropdownEditable = true;
      }

      // Set states based on this matching logic
      // Only update mindeeData.supplierName if it changed due to matching
      if (mindeeData.supplierName !== localMindeeSupplierName) {
        setMindeeData(prev => ({ ...prev, supplierName: localMindeeSupplierName }));
      }
      setSelectedKycBuyerId(localSelectedKycBuyerId);
      setIsBuyerDropdownEditable(localIsBuyerDropdownEditable);

      // Perform duplicate check. If it's a duplicate, it will override setIsBuyerValid and setBuyerValidationError.
      if (mindeeData.invoiceNumber && mindeeData.totalAmount && localMindeeSupplierName) {
        checkDuplicateInvoice({ ...mindeeData, supplierName: localMindeeSupplierName })
          .then(isDuplicate => {
            if (!isDuplicate) { // If not a duplicate, apply validation from buyer matching
              setIsBuyerValid(localIsBuyerValid);
              setBuyerValidationError(localBuyerValidationError);
            }
            // If it is a duplicate, checkDuplicateInvoice handles setting isBuyerValid=false and error message.
          });
      } else {
        // Apply validation from buyer matching if duplicate check can't run
        setIsBuyerValid(localIsBuyerValid);
        setBuyerValidationError(localBuyerValidationError);
      }

    } else if (uploadStep === 1 && !editingInvoice && !mindeeData) {
      // Reset states when invoice is cleared (e.g., user clicks "Change File" or closes modal and reopens)
      setOriginalExtractedBuyerName('');
      setSelectedKycBuyerId('');
      setIsBuyerDropdownEditable(false);
      setBuyerValidationError('');
      setBuyerNameFieldWarning('');
      setIsBuyerValid(true); // Default before any data
    }
    // Key dependencies: originalExtractedBuyerName, mindeeData (for initial supplierName and essential fields for duplicate check), userInfo (for kyc.buyers).
    // uploadStep and editingInvoice are for context.
    // Removed selectedKycBuyerId and isBuyerDropdownEditable from deps to avoid loops with their own setters.
    // Logic inside now handles when to apply auto-matching vs. respecting user choices (which are handled in handleBuyerDropdownChange).
  }, [originalExtractedBuyerName, mindeeData?.supplierName, mindeeData?.invoiceNumber, mindeeData?.totalAmount, userInfo, uploadStep, editingInvoice, token]);
  const fetchOffers = async () => {
    if (!userId) return; // Don't fetch if userId is not available

    setOffersLoading(true); // Optional: Start loading indicator
    setOffersError('');     // Optional: Clear previous errors
    try {
      console.log(`Workspaceing offers for merchantId: ${userId}`);
      const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/offers`, {
        headers: { 'x-auth-token': token },
        params: {
          merchantId: userId, // Filter offers by the current user's ID
          // Optionally add other default filters if needed, e.g., offerType=invoiceDiscountingOffer
        }
      });

      if (response.data?.success && Array.isArray(response.data.offers)) {
        console.log(`Workspaceed ${response.data.offers.length} offers.`);
        setOffersData(response.data.offers);
      } else {
        console.warn("No offers found or unexpected format:", response.data);
        setOffersData([]); // Set empty array if no offers or error
      }
    } catch (error) {
      console.error('Error fetching offers:', error);
      setOffersError(error.response?.data?.message || 'Failed to fetch associated offers.');
      setOffersData([]); // Clear data on error
    } finally {
      setOffersLoading(false); // Optional: Stop loading indicator
    }
  };
  const history = useHistory();

  // Add state for error messages display
  const [uploadError, setUploadError] = useState('');
  const [validationError, setValidationError] = useState('');
  const [duplicateInvoiceId, setDuplicateInvoiceId] = useState(null);

  const handleFileUpload = async (selectedFileObject) => {
    const file = selectedFileObject;

    if (file) {
      setUploadError('');
      setGeneralLoadingTitle("Extracting Invoice Data");
      setGeneralLoadingMessage("Please wait while we process your document...");
      setShowGeneralLoadingModal(true);

      try {
        const formData = new FormData();
        formData.append('pdfFile', file);
        formData.append('userId', userId);
        formData.append('extractionOnly', 'true');

        console.log("Uploading invoice for extraction to backend (from MyInvoicesPage)...");
        const backendResponse = await axios.post(
          `${config.apiUrl}/ops/invoiceFinancing/uploadInvoice`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
              'x-auth-token': token
            }
          }
        );

        console.log("Backend extraction response (MyInvoicesPage):", backendResponse.data);
        setShowGeneralLoadingModal(false);

        if (backendResponse.data?.success && backendResponse.data?.extractedData) {
          const extracted = backendResponse.data.extractedData;
          const invoiceNumber = extracted.invoice_id || extracted.invoice_number;
          const totalAmountStr = (extracted.total_amount !== null && extracted.total_amount !== undefined)
            ? String(extracted.total_amount)
            : null;
          const supplierName = extracted.supplier_name;

          if (!invoiceNumber || totalAmountStr === null || !supplierName) {
            // ... (your existing error handling for missing essential fields) ...
            let missingFields = [];
            if (!invoiceNumber) missingFields.push("invoice number");
            if (totalAmountStr === null) missingFields.push("total amount");
            if (!supplierName) missingFields.push("buyer/supplier name");
            const missingFieldsText = missingFields.join(", ");
            setUploadError(`The document was processed, but essential invoice information is missing: ${missingFieldsText}. Please upload a clear and valid invoice document.`);
            setShowNewInitialUploadModal(true);
            return;
          }

          const rawLineItems = extracted.line_items || [];
          const plainLineItemsForMindeeData = rawLineItems.map(item => {
            // ... (your existing line item mapping)
            const description = item.description?.value !== undefined ? item.description.value : item.description;
            const quantity = item.quantity?.value !== undefined ? item.quantity.value : item.quantity;
            const amount = item.total_amount?.value !== undefined ? item.total_amount.value :
              (item.amount?.value !== undefined ? item.amount.value :
                (item.total_line_amount?.value !== undefined ? item.total_line_amount.value :
                  (item.amount || item.total_amount || item.total_line_amount)));
            const unitPrice = item.unit_price?.value !== undefined ? item.unit_price.value : item.unit_price;
            return {
              description: description || null,
              quantity: quantity !== undefined && quantity !== null ? parseFloat(quantity) : null,
              amount: amount !== undefined && amount !== null ? parseFloat(amount) : null,
              unitPrice: unitPrice !== undefined && unitPrice !== null ? parseFloat(unitPrice) : null,
            };
          }).filter(item => item.description || (item.amount != null && !isNaN(item.amount)));

          // Corrected mapping for newMindeeData
          const newMindeeData = {
            invoiceNumber: invoiceNumber,
            invoiceDate: formatDateToYYYYMMDD(extracted.invoice_date) || formatDateToYYYYMMDD(new Date().toISOString()),
            dueDate: formatDateToYYYYMMDD(extracted.due_date) || formatDateToYYYYMMDD(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()),
            totalAmount: totalAmountStr,
            supplierName: supplierName,
            customerName: extracted.customer_name || user.companyName || 'Default Customer',
            billingAddress: extracted.billing_address || extracted.supplier_address || '',
            customerAddress: extracted.customer_address || '',
            // Handle netAmount: if it's just "QAR", treat as empty or 0, otherwise use the value.
            netAmount: (extracted.net_amount && typeof extracted.net_amount === 'string' && !isNaN(parseFloat(extracted.net_amount)))
              ? String(extracted.net_amount)
              : (typeof extracted.net_amount === 'number' ? String(extracted.net_amount) : ''),
            totalTaxAmount: (extracted.total_tax_amount !== null && extracted.total_tax_amount !== undefined) ? String(extracted.total_tax_amount) : '',
            supplierPhone: extracted.supplier_phone_number || extracted.supplier_phone || '',
            supplierEmail: extracted.supplier_email || '',
            supplierWebsite: extracted.supplier_website || '',
            invoiceType: extracted.document_type || extracted.invoice_type || '',
            lineItems: plainLineItemsForMindeeData,
            currency: extracted.currency || 'QAR',
            // Assuming your API might provide these for Qatar addresses:
            addressZone: extracted.address_zone || '',
            addressStreetNo: extracted.address_street_no || '',
            addressBuildingNo: extracted.address_building_no || '',
            addressFloorNo: extracted.address_floor_no || '',
            addressUnitNo: extracted.address_unit_no || '',
          };

          // Corrected navigation for React Router v5 (useHistory)
          // Ensure the path '/uploadinvoicepage' matches your route definition
          history.push('/uploadInvoice', { // If your route is '/uploadInvoice', use that.
            mindeeData: newMindeeData,
            uploadedInvoiceFile: file
          });

        } else {
          // ... (your existing error handling) ...
          const errorMsg = backendResponse.data?.details || backendResponse.data?.message || "Failed to extract invoice data. Please check the document and try again.";
          setUploadError(errorMsg);
          setShowNewInitialUploadModal(true);
        }
      } catch (error) {
        // ... (your existing error handling) ...
        console.error('Error during invoice extraction via backend (MyInvoicesPage):', error);
        setShowGeneralLoadingModal(false);
        const errorDetails = error.response?.data?.details || error.response?.data?.message || error.message || "An unknown error occurred during document processing.";
        setUploadError(`Could not process the invoice: ${errorDetails}. Please try uploading again.`);
        setShowNewInitialUploadModal(true);
      } finally {
        if (initialInvoiceFileRef.current) {
          initialInvoiceFileRef.current.value = '';
        }
      }
    } else {
      setUploadError("No file selected.");
      setShowNewInitialUploadModal(true);
    }
  };

  const handleFinalSubmit = async () => {
    setValidationError('');
    setDuplicateInvoiceId(null);

    if (!ConsentAccepted) {
      setValidationError('Please accept the Consent and disclosure terms.');
      return;
    }
    if (!uploadedFile || !mindeeData) {
      setValidationError('Main invoice file or its extracted details are missing. Please re-upload the invoice.');
      return;
    }
    if (!mindeeData.invoiceNumber || !mindeeData.totalAmount || !mindeeData.supplierName) {
      setValidationError('Invalid invoice data. Key fields like invoice number, total amount, or supplier name are missing. Please re-upload.');
      return;
    }

    // --- Strict Buyer Validation (copied from "Next" button logic for consistency) ---
    if (!isBuyerValid) {
      const currentError = buyerValidationError || "The selected buyer is not valid or the invoice is a duplicate. Please resolve the issue.";
      toast.error(currentError); // Keep toast here for clarity during submit attempt
      setValidationError(currentError);
      return;
    }

    const kycBuyersList = userInfo?.kyc?.buyers || [];
    const finalSelectedBuyerDetailsForSubmit = kycBuyersList.find(b => b._id === selectedKycBuyerId);

    if (!selectedKycBuyerId || !finalSelectedBuyerDetailsForSubmit) {
      const errorMsg = 'An approved buyer must be selected for this invoice to be submitted.';
      setValidationError(errorMsg);
      toast.error(errorMsg);
      setIsBuyerDropdownEditable(true); // Ensure dropdown is editable to fix
      return;
    }

    const normalize = (name) => name?.trim().replace(/\s+/g, ' ').toLowerCase();
    if (normalize(mindeeData.supplierName) !== normalize(finalSelectedBuyerDetailsForSubmit.buyerName)) {
      const errorMsg = `Data inconsistency before submission: Invoice buyer ("${mindeeData.supplierName}") does not match selected approved buyer ("${finalSelectedBuyerDetailsForSubmit.buyerName}"). Please correct.`;
      setValidationError(errorMsg);
      toast.error("Buyer name mismatch. Please re-select from dropdown.");
      setIsBuyerDropdownEditable(true);
      return;
    }
    // --- End Strict Buyer Validation ---

    setIsLoading(true);
    let newInvoiceId = null;

    try {
      const mainFormData = new FormData();
      mainFormData.append('pdfFile', uploadedFile);
      mainFormData.append('userId', userId);
      if (user.gstin) mainFormData.append('gstin', user.gstin);
      mainFormData.append('status', "APPROVAL_PENDING");
      mainFormData.append('extractionOnly', 'false');

      console.log("MindeeData being used for final FormData submission:", JSON.stringify(mindeeData, null, 2));
      for (const key in mindeeData) {
        if (Object.prototype.hasOwnProperty.call(mindeeData, key) && mindeeData[key] !== null && mindeeData[key] !== undefined) {
          const value = mindeeData[key];
          if (key === 'lineItems' && Array.isArray(value)) {
            try {
              mainFormData.append(key, JSON.stringify(value));
            } catch (stringifyError) {
              console.error(`Error stringifying '${key}':`, stringifyError);
              mainFormData.append(key, "[]");
            }
          } else if (typeof value === 'object' && !Array.isArray(value) && value !== null) {
            try {
              mainFormData.append(key, JSON.stringify(value));
            } catch (stringifyError) {
              console.error(`Error stringifying object '${key}':`, stringifyError);
              mainFormData.append(key, "{}");
            }
          } else {
            mainFormData.append(key, String(value));
          }
        }
      }

      console.log("Submitting final invoice data to backend...");
      const uploadResponse = await axios.post(
        `${config.apiUrl}/ops/invoiceFinancing/uploadInvoice`,
        mainFormData,
        { headers: { 'Content-Type': 'multipart/form-data', 'x-auth-token': token } }
      );

      if (!uploadResponse.data?.success || !uploadResponse.data?.invoice?._id) {
        // ... (existing duplicate error handling from your code)
        if (uploadResponse.data?.error === 'Duplicate invoice' && uploadResponse.data?.existingInvoice) {
          // ... setValidationError, setDuplicateInvoiceId ...
          return;
        }
        throw new Error(uploadResponse.data?.details || uploadResponse.data?.message || "Failed to save invoice or get new invoice ID.");
      }
      newInvoiceId = uploadResponse.data.invoice._id;
      console.log("Invoice saved successfully with ID:", newInvoiceId);

      if (additionalFilesToUpload.length > 0 && newInvoiceId) {
        console.log(`Uploading ${additionalFilesToUpload.length} additional documents for invoice ${newInvoiceId}...`);
        for (const file of additionalFilesToUpload) {
          try {
            await uploadAdditionalInvoiceDocument(newInvoiceId, file);
            console.log(`Successfully uploaded additional file: ${file.name}`);
          } catch (additionalError) {
            console.error(`Failed to upload additional file ${file.name}:`, additionalError);
            // Alert for individual additional doc failure, but main invoice is still created
            toast.warn(`Main invoice created, but failed to upload additional file: ${file.name}.`);
          }
        }
      }

      // --- START: Send Buyer Invitation Email on Final Invoice Submit (No Toast as per request) ---
      if (newInvoiceId && finalSelectedBuyerDetailsForSubmit && finalSelectedBuyerDetailsForSubmit.contactEmail) {
        const currentMsmeEmail = user.email; // MSME's email
        if (currentMsmeEmail) {
          console.log(`Invoice ${newInvoiceId} submitted. Initiating verification email to buyer: ${finalSelectedBuyerDetailsForSubmit.contactEmail}.`);
          try {
            // Pass necessary identifiers to the API
            await sendBuyerInvitationEmail(
              currentMsmeEmail,
              finalSelectedBuyerDetailsForSubmit.contactEmail,
              newInvoiceId, // Pass the newly created invoice ID
              mindeeData.invoiceNumber // Pass the invoice number
            );
            console.log(`Verification email process initiated for buyer ${finalSelectedBuyerDetailsForSubmit.contactEmail} for invoice ${newInvoiceId}.`);
          } catch (emailError) {
            // Log error silently for this specific call as per requirement (no user-facing toast)
            console.error(
              `Failed to initiate verification email for buyer ${finalSelectedBuyerDetailsForSubmit.contactEmail} (Invoice ID: ${newInvoiceId}):`,
              emailError.response?.data?.message || emailError.message || "Unknown email sending error"
            );
          }
        } else {
          console.warn(`Could not send buyer verification email for invoice ${newInvoiceId}: MSME user email is missing.`);
        }
      } else {
        console.warn(`Could not send buyer verification email for invoice ${newInvoiceId}: Missing details (MSME email, selected buyer, or buyer contact email).`);
      }
      // --- END: Send Buyer Invitation Email ---

      toast.success("Invoice submitted successfully!"); // General success toast

      await fetchInvoices();
      // ... (rest of your cleanup: setShowUploadModal, setUploadStep, etc.)
      setShowUploadModal(false);
      setUploadStep(1);
      setUploadedFile(null);
      setMindeeData(null);
      setEditingInvoice(null);
      setConsentAccepted(false);
      setAdditionalFilesToUpload([]);
      setPdfUrl('');
      if (fileInputRef.current) fileInputRef.current.value = null;
      setIsBuyerValid(true);
      setBuyerValidationError('');
      setSelectedBuyerIdForUpload(''); // This seems unused, selectedKycBuyerId is primary
      setOriginalExtractedBuyerName('');
      setSelectedKycBuyerId('');
      setBuyerNameFieldWarning('');
      setIsBuyerDropdownEditable(false); // Reset for next upload

    } catch (error) {
      // ... (your existing error handling for final submit) ...
      console.error('Error during final submit process:', error);
      if (error.response?.data?.error === "Duplicate invoice") {
        // ... (existing duplicate error handling) ...
      } else {
        setValidationError(`Failed to submit invoice: ${error.response?.data?.details || error.response?.data?.message || error.message || 'Unknown error'}`);
        toast.error(`Failed to submit invoice: ${error.response?.data?.details || error.response?.data?.message || error.message || 'Unknown error'}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const renderUploadModal = () => {
    // 3 Steps for initial upload
    const steps = ['Invoice Upload', 'Additional Documents', 'Consent'];

    return (
      // Modal Backdrop
      <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4 overflow-y-auto"> {/* Allow backdrop scroll if modal overflows viewport height */}

        {/* Modal Content Box */}
        <div className="bg-white p-5 md:p-6 rounded-lg shadow-xl w-full max-w-4xl md:max-w-5xl lg:max-w-6xl max-h-[95vh] flex flex-col"> {/* Use max-h and flex-col */}

          {/* Step Indicator */}
          <div className="flex-shrink-0 flex justify-between mb-5 md:mb-6 space-x-2 md:space-x-4">
            {steps.map((step, index) => (
              <div
                key={step}
                className={`flex-1 text-center py-2.5 px-2 text-sm sm:text-base md:text-lg rounded-md transition-all duration-300 ${uploadStep === index + 1
                  ? 'bg-[#004141] text-white font-semibold shadow-md'
                  : 'bg-gray-200 text-gray-600'
                  }`}
              >
                {step}
              </div>
            ))}
          </div>

          {/* Content Area (Scrollable) */}
          <div className="flex-grow overflow-y-auto mb-4 pr-1 scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100">

            {/* --- Step 1: Invoice Upload --- */}
            {uploadStep === 1 && (
              <div className="min-h-[60vh] flex flex-col"> {/* Ensure minimum height */}
                {!uploadedFile ? (
                  // Initial Upload Prompt
                  <div className="text-center flex-grow flex flex-col justify-center items-center p-5">
                    <div className="flex justify-center mb-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 md:h-20 md:w-20 text-[#004141]" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <h2 className="text-xl md:text-2xl font-bold text-gray-800 mb-3">
                      Upload Invoice
                    </h2>
                    <p className="text-gray-600 mb-6 text-sm md:text-base">
                      Please upload the invoice PDF file (max 10MB)
                    </p>
                    <input
                      type="file"
                      ref={fileInputRef}
                      className="hidden"
                      accept=".pdf"
                      onChange={handleFileUpload}
                    />
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      className="px-5 py-2 md:px-6 md:py-2.5 text-base md:text-lg bg-[#004141] text-white rounded-md hover:bg-opacity-90 transition-colors shadow hover:shadow-md"
                    >
                      Select Invoice PDF
                    </button>
                  </div>
                ) : (
                  // Preview and Details Form after upload
                  <div className="flex flex-col md:flex-row flex-grow gap-4"> {/* Use flex-grow */}
                    {/* PDF Preview Pane */}
                    <div className="w-full md:w-3/5 h-[50vh] md:h-auto border border-gray-200 rounded-md overflow-hidden flex flex-col">
                      <div className="flex-shrink-0 p-2 bg-gray-100 border-b border-gray-200 flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-700">Invoice Preview</span>
                        <input type="file" ref={fileInputRef} className="hidden" accept=".pdf" onChange={handleFileUpload} /> {/* Input needed for re-upload */}
                        <button onClick={() => fileInputRef.current?.click()} className="text-xs text-blue-600 hover:underline" title="Select a different invoice PDF">
                          Change File
                        </button>
                      </div>
                      {extractingData ? (
                        <div className="flex items-center justify-center flex-grow p-10">
                          <Loader />
                          <span className="ml-3 text-gray-600">Extracting details...</span>
                        </div>
                      ) : pdfUrl ? (
                        <iframe src={pdfUrl} className="w-full flex-grow border-0" title="Invoice PDF Preview" />
                      ) : (
                        <div className="flex items-center justify-center flex-grow text-gray-500 p-10">Preview Error.</div>
                      )}
                    </div>

                    {/* Details Pane */}
                    <div className="w-full md:w-2/5 h-full overflow-y-auto md:pr-1"> {/* Scroll only details */}
                      <div className="bg-gray-50 p-4 rounded-md border border-gray-200 h-full">
                        <h4 className="text-lg font-medium mb-4 text-gray-800">Extracted Invoice Details</h4>

                        {/* --- START: Document Validation Error Display --- */}
                        {uploadError && (
                          <div className="mb-4 p-4 bg-red-50 border-l-4 border-red-500 text-red-700 text-sm rounded-md">
                            <h3 className="font-medium text-red-800 mb-1">Invalid Document</h3>
                            <p className="mb-2">{uploadError}</p>
                            <button
                              onClick={() => {
                                // Reset the file input and clear errors
                                if (fileInputRef.current) {
                                  fileInputRef.current.value = '';
                                }
                                // Clear the current document and reset the view
                                setUploadedFile(null);
                                setUploadError('');
                                setMindeeData(null);
                                // Release the object URL to prevent memory leaks
                                if (pdfUrl) {
                                  URL.revokeObjectURL(pdfUrl);
                                  setPdfUrl('');
                                }
                              }}
                              className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                            >
                              <ArrowUturnLeftIcon className="-ml-0.5 mr-1 h-4 w-4" aria-hidden="true" />
                              Go Back & Try Again
                            </button>
                          </div>
                        )}
                        {/* --- END: Document Validation Error Display --- */}

                        {/* --- START: Validation Error Display --- */}
                        {validationError && !uploadError && (
                          <div className="mb-4 p-3 bg-red-100 border border-red-300 text-red-700 text-sm rounded-md flex items-start">
                            <ExclamationTriangleIcon className="h-5 w-5 mr-2 text-red-500 flex-shrink-0 mt-0.5" />
                            <span>{validationError}</span>
                          </div>
                        )}
                        {/* --- END: Validation Error Display --- */}

                        {/* --- START: Buyer Validation Warning/Error Display --- */}
                        {buyerValidationError && !uploadError && (
                          <div className={`mb-4 p-3 ${isBuyerValid ? 'bg-yellow-50 border border-yellow-300 text-yellow-700' : 'bg-red-100 border border-red-300 text-red-700'} text-sm rounded-md flex items-start`}>
                            <ExclamationTriangleIcon className={`h-5 w-5 mr-2 ${isBuyerValid ? 'text-yellow-500' : 'text-red-500'} flex-shrink-0 mt-0.5`} />
                            <span>{buyerValidationError}</span>
                          </div>
                        )}
                        {/* --- END: Buyer Validation Warning/Error Display --- */}

                        {(mindeeData || editingInvoice) ? (
                          <div className="space-y-4">
                            {renderInvoiceDetails(
                              editingInvoice
                                ? { _doc: editingInvoice }
                                : { _doc: { ...mindeeData, _id: null, status: 'VERIFICATION_PENDING' } }
                            )}
                          </div>
                        ) : extractingData ? (
                          <div className="text-center text-gray-500 py-10">Extracting data...</div>
                        ) : (
                          <div className="text-center text-gray-500 py-10">Upload an invoice to see details.</div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* --- Step 2: Add Additional Documents (Dynamic List) --- */}
            {uploadStep === 2 && (
              <div className="p-4 md:p-6 min-h-[60vh]">
                <h3 className="text-xl md:text-2xl font-bold text-gray-800 mb-3">Add Supporting Documents (Optional)</h3>
                <p className="text-gray-600 mb-5 text-sm md:text-base">
                  You can add up to 10 files (PDF, JPG, PNG) like Delivery Notes, Purchase Orders, etc.
                </p>

                {/* File Input Trigger Button */}
                <div className="mb-5">
                  <input
                    type="file"
                    ref={step2FileInputRef}
                    multiple
                    className="hidden"
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={handleStep2FileSelect}
                  />
                  <button
                    onClick={() => step2FileInputRef.current?.click()}
                    className={`inline-flex items-center justify-center px-5 py-2 text-sm font-medium rounded-md transition-colors ${additionalFilesToUpload.length >= 10
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-[#004141] text-white hover:bg-opacity-90 shadow hover:shadow-md'
                      }`}
                    disabled={additionalFilesToUpload.length >= 10}
                    title={additionalFilesToUpload.length >= 10 ? "Maximum 10 documents reached" : "Select files (PDF, JPG, PNG)"}
                  >
                    <DocumentArrowUpIcon className="w-5 h-5 mr-2" />
                    Select Files ({additionalFilesToUpload.length}/10)
                  </button>
                </div>

                {/* List of Selected Files */}
                <div className="space-y-2 max-h-[40vh] overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 border-t border-b py-3">
                  {additionalFilesToUpload.length > 0 ? (
                    additionalFilesToUpload.map((file, index) => {
                      // --- Create a temporary URL for preview ---
                      let tempUrl = null;
                      try {
                        // Note: Object URLs should be revoked later to prevent memory leaks
                        tempUrl = URL.createObjectURL(file);
                      } catch (error) {
                        console.error("Error creating object URL:", error);
                        // Handle cases where URL creation might fail (though unlikely for File objects)
                      }

                      return (
                        <div key={`${file.name}-${file.lastModified}-${index}`} className="flex items-center justify-between bg-gray-100 p-2 pl-3 rounded border border-gray-200 text-sm shadow-sm">
                          <span className="flex-grow truncate text-gray-800 mr-2" title={file.name}>
                            {file.name} <span className="text-gray-500 text-xs">({(file.size / 1024).toFixed(1)} KB)</span>
                          </span>

                          <div className="flex-shrink-0 flex items-center space-x-2 ml-2"> {/* Wrapper for buttons */}
                            {/* --- View Button --- */}
                            {tempUrl ? (
                              <a
                                href={tempUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-xs text-blue-600 hover:text-blue-800 font-medium px-2 py-1 rounded hover:bg-blue-100 transition-colors"
                                title={`Preview ${file.name} (opens new tab)`}
                              // Optional: Add onClick handler to revoke URL after click if desired,
                              // but it's better to handle cleanup in useEffect
                              >
                                View
                              </a>
                            ) : (
                              <span className="text-xs text-gray-400 px-2 py-1">No Preview</span>
                            )}

                            {/* --- Remove Button --- */}
                            <button
                              onClick={() => {
                                // Revoke URL immediately when removing the file from the list
                                if (tempUrl) URL.revokeObjectURL(tempUrl);
                                removeFileFromStep2(index);
                              }}
                              className="text-red-500 hover:text-red-700 p-1 rounded-full hover:bg-red-100 transition-colors"
                              title="Remove file"
                              aria-label={`Remove ${file.name}`}
                            >
                              <XCircleIcon className="w-5 h-5" />
                            </button>
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <p className="text-sm text-gray-500 italic text-center py-4">No additional documents selected.</p>
                  )}
                </div>
              </div>
            )}

            {/* --- Step 3: Consent --- */}
            {uploadStep === 3 && (
              <div className="space-y-6 p-4 md:p-6 min-h-[60vh]"> {/* Ensure minimum height */}
                <h3 className="text-xl md:text-2xl font-bold text-gray-800 mb-4">Invoice Financing Consent</h3>

                {/* Validation Error Display */}
                {validationError && (
                  <div className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-md">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
                      </div>
                      <div className="ml-3 flex-1">
                        <h3 className="text-sm font-medium text-red-800">Error</h3>
                        <div className="mt-1 text-sm text-red-700">
                          {/* Check if this is a multiline error (like duplicate invoice) */}
                          {validationError.includes('\n') ? (
                            <div className="whitespace-pre-line">
                              {/* First line as heading */}
                              <p className="font-semibold">{validationError.split('\n')[0]}</p>
                              {/* Rest of the message */}
                              <div className="mt-2 p-3 bg-red-100 rounded-md">
                                {validationError.split('\n\n')[1]}
                              </div>
                            </div>
                          ) : (
                            <p>{validationError}</p>
                          )}
                        </div>
                        {/* Action buttons for error */}
                        <div className="mt-3 flex flex-wrap gap-2">
                          <button
                            onClick={() => {
                              setUploadStep(1); // Go back to step 1
                              setValidationError(''); // Clear error
                              setDuplicateInvoiceId(null); // Clear duplicate invoice ID
                            }}
                            className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                          >
                            <ArrowUturnLeftIcon className="-ml-0.5 mr-1 h-4 w-4" aria-hidden="true" />
                            Go Back & Fix
                          </button>

                          {/* View Existing Invoice button - only show for duplicate invoice errors */}
                          {duplicateInvoiceId && (
                            <button
                              onClick={() => {
                                // Close the modal
                                setShowUploadModal(false);

                                // Find the duplicate invoice in the list and set it as the viewing invoice
                                const duplicateInvoice = invoices.find(inv => inv._doc._id === duplicateInvoiceId);
                                if (duplicateInvoice) {
                                  setViewingInvoice(duplicateInvoice);
                                  setShowViewModal(true);
                                }

                                // Reset states
                                setValidationError('');
                                setDuplicateInvoiceId(null);
                                setUploadStep(1);
                                setUploadedFile(null);
                                setMindeeData(null);
                              }}
                              className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            >
                              <EyeIcon className="-ml-0.5 mr-1 h-4 w-4" aria-hidden="true" />
                              View Existing Invoice
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Consent Text Box */}
                <div className="bg-gray-50 p-4 md:p-6 rounded-md border border-gray-200 max-h-[45vh] md:max-h-[50vh] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                  <p className="mb-4 text-gray-700 font-semibold">
                    Consent for Invoice Financing in the State of Qatar
                  </p>
                  <div className="prose prose-sm max-w-none text-gray-600">
                    <ol className="list-decimal pl-5 space-y-2">
                      <li>I/We hereby undertake and confirm that the invoice submitted is genuine, accurate, and legally valid.</li>
                      <li>The invoice represents a legitimate business transaction and is free from any fraudulent or misleading information.</li>
                      <li>I/We authorize the financial institution to verify the details of this invoice with the mentioned parties (supplier, customer, etc.).</li>
                      <li>I/We understand that any false representation may lead to legal action and potential disqualification from future financing services.</li>
                      <li>The invoice is eligible for financing under the applicable commercial laws and regulations of the State of Qatar.</li>
                      <li>I/We agree to the terms and conditions associated with the invoice financing service provided.</li>
                    </ol>
                  </div>
                </div>

                {/* Consent Checkbox */}
                <div className="mt-6">
                  <label className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={ConsentAccepted}
                      onChange={(e) => setConsentAccepted(e.target.checked)}
                      className="form-checkbox h-5 w-5 text-[#004141] rounded border-gray-300 focus:ring-[#004141]"
                    />
                    <span className="text-gray-700 select-none text-sm md:text-base">
                      I have read, understood, and accept the Consent terms.
                    </span>
                  </label>
                </div>
              </div>
            )}
          </div> {/* End Content Area */}

          {/* --- Navigation Buttons --- */}
          <div className="flex-shrink-0 mt-auto pt-4 border-t flex justify-between items-center">
            {/* Previous Button: Shows on Step 2 and 3 */}
            {uploadStep > 1 ? (
              <button
                onClick={() => setUploadStep(prev => prev - 1)}
                className="px-5 py-2 md:px-6 md:py-2 text-base text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
                aria-label="Go to previous step"
              >
                Previous
              </button>
            ) : (
              // Close Button: Shows only on Step 1
              <button
                onClick={() => {
                  setShowUploadModal(false);
                  // Reset state fully on close
                  setUploadedFile(null); setMindeeData(null); setEditingInvoice(null);
                  setUploadStep(1); setConsentAccepted(false);
                  setAdditionalFilesToUpload([]); // Clear selected files
                  setExtractingData(false);
                  setIsBuyerValid(true); // Reset buyer validation state
                  setBuyerValidationError(''); // Reset buyer validation error
                  setValidationError(''); // Clear any validation errors
                  setSelectedBuyerIdForUpload(''); // <<< ADD THIS LINE TO RESET
                  setDuplicateInvoiceId(null); // Clear duplicate invoice ID
                  setOriginalExtractedBuyerName(''); // <<< ADD
                  setSelectedKycBuyerId('');      // <<< ADD
                  setBuyerNameFieldWarning('');     // <<< ADD
                  setIsBuyerDropdownEditable(false); // <<< ADD
                }}
                className="px-5 py-2 md:px-6 md:py-2 text-base bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                aria-label="Close upload modal"
              >
                Close
              </button>
            )}

            {/* Next Button: Shows on Step 1 and 2 */}
            {uploadStep < 3 && (
              <button
                onClick={() => {
                  // Clear any previous validation errors
                  setValidationError('');

                  if (uploadStep === 1) {
                    if (!uploadedFile) { /* ... */ return; }
                    if (extractingData || !mindeeData) { /* ... */ return; }
                    if (!mindeeData.invoiceNumber || mindeeData.totalAmount === null || mindeeData.totalAmount === undefined /* || !mindeeData.supplierName */) {
                      // Supplier name check is now more nuanced due to selection logic
                      setValidationError('Extracted invoice data is incomplete (missing number or amount). Please upload a clearer document.');
                      return;
                    }

                    // --- Strict Buyer Validation before proceeding to Step 2 ---
                    if (!isBuyerValid) {
                      const currentError = buyerValidationError || "The selected buyer is not valid or the invoice is a duplicate. Please resolve the issue.";
                      toast.error(currentError);
                      setValidationError(currentError);
                      return;
                    }

                    const kycBuyersList = userInfo?.kyc?.buyers || [];
                    const selectedBuyerObject = kycBuyersList.find(b => b._id === selectedKycBuyerId);

                    if (!selectedKycBuyerId || !selectedBuyerObject) {
                      const errorMsg = 'An approved buyer must be selected. If the extracted buyer is incorrect or not listed, please use "Add/Manage Buyers" and select the correct one.';
                      setValidationError(errorMsg);
                      toast.error(errorMsg);
                      setIsBuyerDropdownEditable(true); // Ensure dropdown is editable
                      return;
                    }

                    // Final check: the mindeeData.supplierName (which is what will be submitted)
                    // MUST match the name of the selectedKycBuyerId from the dropdown.
                    const normalize = (name) => name?.trim().replace(/\s+/g, ' ').toLowerCase();
                    if (normalize(mindeeData.supplierName) !== normalize(selectedBuyerObject.buyerName)) {
                      const errorMsg = `Data mismatch: The invoice buyer name ("${mindeeData.supplierName}") that will be submitted does not match the selected approved buyer ("${selectedBuyerObject.buyerName}"). Please ensure the correct buyer is selected from the dropdown. If the extracted name was correct and matched, it should be locked.`;
                      setValidationError(errorMsg);
                      toast.error("Buyer name mismatch. Re-select from dropdown.");
                      setIsBuyerDropdownEditable(true); // Allow correction
                      return;
                    }
                    // --- End Strict Buyer Validation ---
                  }
                  // If all step 1 validations pass:
                  setValidationError(''); // Clear any previous minor validation errors
                  setUploadStep(prev => prev + 1); // Proceed to next step
                }}
                className="ml-auto px-5 py-2 md:px-6 md:py-2 text-base bg-[#004141] text-white rounded-md hover:bg-opacity-90 disabled:opacity-50 disabled:cursor-not-allowed"
                // --- MODIFIED disabled and title ---
                disabled={
                  (uploadStep === 1 && (!uploadedFile || extractingData || !mindeeData || !!editingInvoice || uploadError || !isBuyerValid))
                  // No specific validation needed for step 2, allow proceeding

                }
                title={
                  (uploadStep === 1 && !uploadedFile) ? "Please upload an invoice first" :
                    (uploadStep === 1 && (extractingData || !mindeeData)) ? "Waiting for invoice details extraction..." :
                      (uploadStep === 1 && !!editingInvoice) ? "Please save or cancel invoice edits first." :
                        (uploadStep === 1 && uploadError) ? "Please upload a valid invoice document" :
                          (uploadStep === 1 && !isBuyerValid) ? "This invoice is a duplicate and cannot be processed. Please contact support team." :
                            (uploadStep === 2) ? "Proceed to consent step" :
                              "Go to next step"

                }
                // --- END MODIFICATION ---
                aria-label="Go to next step"
              >
                {uploadStep === 1 && extractingData ? 'Processing...' :
                  uploadStep === 1 ? 'Next: Additional Documents' :
                    uploadStep === 2 ? 'Next: Consent' : 'Next'}
              </button>
            )}

            {/* Submit Button: Shows only on Step 3 */}
            {uploadStep === 3 && (
              <button
                onClick={handleFinalSubmit}
                className="ml-auto px-5 py-2 md:px-6 md:py-2 text-base bg-[#004141] text-white rounded-md hover:bg-opacity-90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center min-w-[120px]" // Added min-width
                disabled={!ConsentAccepted || isLoading}
                aria-label="Submit invoice"
              >
                {isLoading ? (
                  <>
                    <ArrowPathIcon className="w-5 h-5 mr-2 animate-spin" /> Submitting...
                  </>
                ) : (
                  'Submit Invoice'
                )}
              </button>
            )}
          </div> {/* End Navigation Buttons */}

        </div> {/* End Modal Content Box */}
      </div> // End Modal Backdrop
    );
  };

  useEffect(() => {
    fetchInvoices();
    fetchCreditLine();
    fetchOffers();
    fetchUserInfo();
    console.log(creditLine, invoices);
  }, []);

  const fetchUserInfo = async () => {
    // Ensure userId is available (it's defined earlier in your MyInvoicesPage component)
    if (!userId) {
      console.error("User ID not found, cannot fetch user info.");
      setUserInfo(null);
      return;
    }

    console.log(`Workspaceing KYC info for userId: ${userId}`);

    try {
      // Call the imported getKycInfo function
      const kycData = await getKycInfo(userId);

      console.log("Fetched KYC Info:", kycData.user);

      // Assuming getKycInfo returns the user's KYC object directly on success,
      // or throws an error on failure. Adjust based on actual getKycInfo behavior.
      if (kycData) {
        // Store the entire fetched KYC data object in state
        // This object should contain the 'buyers' array if it exists in the data
        setUserInfo(kycData.user);
      } else {
        // Handle cases where the function might return null/undefined without erroring
        console.warn("Received no KYC data, but no error was thrown.");
        setUserInfo(null);
      }

    } catch (error) {
      console.error("Error fetching user KYC info:", error);
      // Set error state based on the error received
      setUserInfo(null); // Clear any potentially stale data
    }
  };

  useEffect(() => {
    if (location.state?.success && location.state?.status && location.state?.invoiceId) {
      setInvoices(prevInvoices =>
        prevInvoices.map(inv =>
          inv._doc._id === location.state.invoiceId
            ? { ...inv, _doc: { ...inv._doc, status: location.state.status } }
            : inv
        )
      );
    }
  }, [location]);

  useEffect(() => {
    let tempInvoices = [...invoices]; // Start with the raw list from API

    // Apply Status Filter
    if (statusFilter) {
      // Ensure invoice._doc exists before filtering
      tempInvoices = tempInvoices.filter(inv => inv?._doc?.status === statusFilter);
    }

    // Apply Age Filter (using uploadedAt)
    if (ageFilter) {
      const now = new Date();
      tempInvoices = tempInvoices.filter(inv => {
        // Ensure invoice._doc and invoice._doc.uploadedAt exist
        if (!inv?._doc?.uploadedAt) return false;
        try {
          const uploadedDate = parseISO(inv._doc.uploadedAt);
          const diffDays = differenceInDays(now, uploadedDate);

          switch (ageFilter) {
            case '7': return diffDays <= 7;
            case '30': return diffDays <= 30;
            case '90': return diffDays <= 90;
            case 'over90': return diffDays > 90;
            default: return true; // No valid age filter selected
          }
        } catch (e) {
          console.error("Error parsing date for age filter:", inv._doc.uploadedAt, e);
          return false; // Exclude if date is invalid
        }
      });
    }

    setFilteredInvoices(tempInvoices); // Update the state used for rendering the table
  }, [invoices, statusFilter, ageFilter]);

  const updateApplyButton = () => {
    if (creditLineError) {
      setApplyButtonDisabled(true);
      setApplyButtonReason(creditLineError);
      return;
    }

    if (!invoices || invoices.length === 0) {
      setApplyButtonDisabled(true);
      setApplyButtonReason('Please upload an invoice to proceed.');
      return;
    }

    const verifiedInvoice = invoices.find(
      (invoice) => invoice?._doc && (invoice._doc.status === 'VERIFIED_ANCHOR' || invoice._doc.status === 'ACCEPTED_LENDER' || invoice._doc.status === 'READY_FOR_DISBURSAL')
    );

    if (!verifiedInvoice) {
      setApplyButtonDisabled(true);
      setApplyButtonReason(
        'Please wait for invoice verification.'
      );
      return;
    }

    if (creditLine && creditLine.creditLineStatus !== 'ACTIVE') {
      setApplyButtonDisabled(true);
      let reason = '';
      switch (creditLine.creditLineStatus) {
        case 'DRAFT':
          reason = 'Your credit line is in draft. Please complete the setup.';
          break;
        case 'ON_HOLD':
          reason = 'Your credit line is on hold. Please contact support.';
          break;
        case 'UNDER_REVIEW':
          reason = 'Your credit line is under review. Please wait until it is approved to receive invoice discounting offers.';
          break;
        case 'APPROVED':
          reason = 'Your credit line is approved. Please accept the offer to activate it.';
          break;
        case 'REJECTED':
          reason = 'Your credit line application was rejected. Please contact support for more information.';
          break;
        case 'SUSPENDED':
          reason = 'Your credit line is suspended. Please contact support.';
          break;
        case 'EXPIRED':
          reason = 'Your credit line has expired. Please apply for a new one.';
          break;
      }
      setApplyButtonReason(reason);
      setApplyButtonDisabled(true);
      return;
    }

    if (verifiedInvoice?._doc?.status === 'VERIFIED_ANCHOR') {
      setApplyButtonText('Submit for Discounting');
      setApplyButtonDisabled(false);
      setApplyButtonReason('');
    } else if (verifiedInvoice?._doc?.status === 'ACCEPTED_LENDER') {
      setApplyButtonText('Sign Contract');
      setApplyButtonDisabled(false);
      setApplyButtonReason('');
    } else {
      setApplyButtonDisabled(true);
    }
  };

  const handleApplyAction = async () => {
    const verifiedInvoice = invoices.find(
      (invoice) => invoice._doc.status === 'VERIFIED_ANCHOR' || invoice._doc.status === 'ACCEPTED_LENDER'
    );
    if (applyButtonText === 'Submit for Discounting') {
      try {
        await axios.put(
          `${config.apiUrl}/ops/invoiceFinancing/updateInvoice/${verifiedInvoice._doc._id}`,
          { status: "VERIFICATION_PENDING_LENDER" },
          {
            headers: {
              'x-auth-token': token
            }
          }
        );
        await fetchInvoices();
      }
      catch (error) {
        console.error("error submitting for discounting: ", error);
        alert("error submitting for discounting");
      }
    } else if (applyButtonText === 'Sign Contract') {
      window.location.href = `/invoiceContract/invoice-offer?id=${verifiedInvoice._doc._id}`;
    }
  };

  const handleInitialInvoiceFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      if (file.size > MAX_INVOICE_FILE_SIZE_BYTES) {
        toast.error(`File size exceeds ${MAX_INVOICE_FILE_SIZE_MB}MB limit.`);
        if (initialInvoiceFileRef.current) {
          initialInvoiceFileRef.current.value = ''; // Reset file input
        }
        return;
      }
      if (!file.type.includes('pdf')) {
        toast.error('Invalid file type. Please upload a PDF.');
        if (initialInvoiceFileRef.current) {
          initialInvoiceFileRef.current.value = ''; // Reset file input
        }
        return;
      }

      setShowNewInitialUploadModal(false); // Close this modal
      // Now call the original handleFileUpload (which will show GeneralLoadingModal and then navigate)
      handleFileUpload(file); // Pass the file directly
    }
  };

  const renderNewInitialUploadModal = () => {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-[9998] p-4">
        <div className="bg-white p-6 sm:p-8 rounded-lg shadow-xl w-full max-w-lg text-center">
          <div className="flex justify-end">
            <button
              onClick={() => setShowNewInitialUploadModal(false)}
              className="text-gray-400 hover:text-gray-600"
              aria-label="Close"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">Upload your invoice</h2>
          <p className="text-gray-400 mb-6">Please upload your invoice PDF.</p>

          <div
            className="bg-[#eff7f7] border-2 border-dashed border-gray-300 rounded-lg p-8 sm:p-12 flex flex-col items-center justify-center cursor-pointer hover:border-gray-400 transition-colors"
            onClick={() => initialInvoiceFileRef.current?.click()}
          >
            <div className="flex items-center gap-2 mb-1">
              <DocumentArrowUpIcon className="h-6 w-6 text-[#004141]" />
              <span className="text-lg font-medium text-[#004141]">Upload</span>
            </div>
            <p className="text-sm text-gray-500">Max size: {MAX_INVOICE_FILE_SIZE_MB}MB</p>
          </div>

          <input
            type="file"
            ref={initialInvoiceFileRef}
            className="hidden"
            accept=".pdf"
            onChange={handleInitialInvoiceFileSelect}
          />
          {uploadError && (
            <p className="text-sm text-red-500 mt-2">{uploadError}</p>
          )}
        </div>
      </div>

    );
  };


  const getFilenameFromPath = (path) => {
    if (!path) return 'document';
    try {
      // Attempt to handle both full URLs and simple paths
      const url = new URL(path);
      const pathnameParts = url.pathname.split('/');
      return decodeURIComponent(pathnameParts[pathnameParts.length - 1] || 'document');
    } catch (e) {
      // If not a valid URL, treat as a simple path
      const pathParts = path.split('/');
      return pathParts[pathParts.length - 1] || 'document';
    }
  };


  // Function to handle the selection and upload of an additional document
  const handleAdditionalDocumentUpload = async (event) => {
    const file = event.target.files[0];
    if (!file || !viewingInvoice?._doc?._id) {
      return;
    }

    // Reset file input to allow uploading the same file again if needed
    event.target.value = null;

    setIsUploadingAdditionalDoc(true);
    setAdditionalDocError('');

    try {
      const result = await uploadAdditionalInvoiceDocument(viewingInvoice._doc._id, file);

      if (result.success) {
        console.log("Additional document uploaded:", result.document);
        // Refresh invoice list to get the updated data (including new signed URLs)
        await fetchInvoices(); // This will update the 'invoices' state

        // Find the *updated* invoice from the refreshed list
        // Note: fetchInvoices needs to be awaited and state updates might take a cycle
        // We will update the viewingInvoice in an effect hook after invoices state updates.
        // For immediate feedback, we could manually add the doc locally, but fetching ensures consistency.

      } else {
        throw new Error(result.error || "Upload failed");
      }
    } catch (error) {
      console.error("Error in handleAdditionalDocumentUpload:", error);
      setAdditionalDocError(error.message || "Failed to upload document. Please try again.");
    } finally {
      setIsUploadingAdditionalDoc(false);
    }
  };

  // Effect Hook to update viewingInvoice after invoices state is refreshed
  useEffect(() => {
    if (viewingInvoice?._doc?._id && invoices.length > 0) {
      const updatedInvoice = invoices.find(inv => inv._doc._id === viewingInvoice._doc._id);
      if (updatedInvoice) {
        setViewingInvoice(updatedInvoice);
      }
    }
  }, [invoices]); // Re-run this effect when 'invoices' state changes



  useEffect(() => {
    updateApplyButton();
  }, [invoices, creditLine, creditLineError]);

  const handleUpdateInvoice = async () => {
    if (mindeeData && !editingInvoice._id) {
      setMindeeData(prevData => ({
        ...prevData,
        invoiceNumber: editingInvoice.invoiceNumber,
        invoiceDate: editingInvoice.invoiceDate,
        dueDate: editingInvoice.dueDate,
        totalAmount: editingInvoice.totalAmount,
        supplierName: editingInvoice.supplierName,
        customerName: editingInvoice.customerName,
        billingAddress: editingInvoice.billingAddress,
        customerAddress: editingInvoice.customerAddress
      }));

      setEditingInvoice(null);
      return;
    }

    if (!editingInvoice) return;

    try {
      setIsLoading(true);
      const response = await axios.put(
        `${config.apiUrl}/ops/invoiceFinancing/updateInvoice/${editingInvoice._id}`,
        {
          invoiceNumber: editingInvoice.invoiceNumber,
          invoiceDate: editingInvoice.invoiceDate,
          dueDate: editingInvoice.dueDate,
          totalAmount: editingInvoice.totalAmount,
          supplierName: editingInvoice.supplierName,
          customerName: editingInvoice.customerName,
          billingAddress: editingInvoice.billingAddress,
          customerAddress: editingInvoice.customerAddress
        },
        {
          headers: {
            'x-auth-token': token
          }
        }
      );

      console.log(response);

      await fetchInvoices();
      setEditingInvoice(null);
    } catch (error) {
      console.error('Error updating invoice:', error);
      alert('Failed to update invoice. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  const briefcaseMoney = require("../../images/briefcase_money.png");
  function getCreditBarColor(percent) {
    if (percent < 30) return 'bg-red-500';
    if (percent < 70) return 'bg-yellow-500';
    return 'bg-green-500';
  }

  const renderInvoiceDetails = (invoice) => {
    const isEditing = editingInvoice && editingInvoice._id === invoice._doc._id;
    const isUploadContext = !invoice._doc._id; // True if this is for a new invoice in the upload modal
    const extractedBuyerNameFromInvoice = invoice._doc.supplierName; // This is mindeeData.supplierName in upload context
    const kycBuyers = userInfo?.kyc?.buyers || [];
    const normalizeName = (name) => name?.trim().replace(/\s+/g, ' ').toLowerCase();

    // Determine if the dropdown should be editable based on the validation error state
    // This specifically checks for the "not approved" or "no buyers" message.
    const isBuyerUnmatchedOrNotApproved = buyerValidationError

    const handleBuyerDropdownChange = (e) => {
      const newSelectedId = e.target.value;
      setSelectedKycBuyerId(newSelectedId); // Update state for the dropdown's controlled value

      const kycBuyers = userInfo?.kyc?.buyers || [];
      const selectedKycBuyer = kycBuyers.find(b => b._id === newSelectedId);

      if (selectedKycBuyer) {
        // User selected a valid KYC buyer
        setMindeeData(prev => ({ ...prev, supplierName: selectedKycBuyer.buyerName }));
        setIsBuyerValid(true);
        setBuyerValidationError(''); // Clear general error since user made a valid choice

        const normalize = (name) => name?.trim().replace(/\s+/g, ' ').toLowerCase();
        if (originalExtractedBuyerName && normalize(selectedKycBuyer.buyerName) !== normalize(originalExtractedBuyerName)) {
          const similarity = stringSimilarity.compareTwoStrings(normalize(selectedKycBuyer.buyerName), normalize(originalExtractedBuyerName));
          if (similarity < 0.75) { // Stricter threshold for user override warning
            setBuyerNameFieldWarning(`Selected: "${selectedKycBuyer.buyerName}". Invoice suggested: "${originalExtractedBuyerName}". Ensure this is correct.`);
          } else {
            setBuyerNameFieldWarning('');
          }
        } else {
          setBuyerNameFieldWarning('');
        }
        setIsBuyerDropdownEditable(false); // Lock the dropdown after a valid user selection
        console.log(`User selected buyer: ${selectedKycBuyer.buyerName}. Dropdown locked.`);
      } else {
        // User selected the placeholder ("" value)
        setMindeeData(prev => ({ ...prev, supplierName: originalExtractedBuyerName || '' }));
        setIsBuyerValid(false); // Invalid selection
        setBuyerValidationError('Please select an approved buyer from the list.');
        setBuyerNameFieldWarning('');
        setIsBuyerDropdownEditable(true); // Keep editable to force selection
        console.log("User selected placeholder. Dropdown remains editable.");
      }
    };

    // Determine if the dropdown should ultimately be editable.
    // It's editable if EITHER the initial validation (buyerValidationError) flagged a major issue
    // OR if our specific field logic (isBuyerDropdownEditable) says so.
    const finalDropdownEditableState = isBuyerDropdownEditable ||
      (buyerValidationError &&
        (buyerValidationError.includes("does not closely match") ||
          buyerValidationError.includes("No buyers have been added") ||
          buyerValidationError.includes("Your KYC profile seems incomplete")));

    return (
      <div className="bg-white shadow-lg rounded-xl p-6 space-y-4">
        <div className="grid grid-cols-1 gap-4">
          <div className="flex flex-col">
            <label className="text-sm font-semibold text-gray-600 mb-2">Invoice Number</label>
            {isEditing ? (
              <input
                type="text"
                value={editingInvoice.invoiceNumber}
                onChange={(e) => setEditingInvoice({
                  ...editingInvoice,
                  invoiceNumber: e.target.value
                })}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#004141] focus:outline-none transition-all"
              />
            ) : (
              <div className="w-full p-3 bg-gray-50 text-gray-800 rounded-md">
                {invoice._doc.invoiceNumber || 'N/A'}
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex flex-col">
              <label className="text-sm font-semibold text-gray-600 mb-2">Invoice Date</label>
              {isEditing ? (
                <input
                  type="date"
                  value={editingInvoice.invoiceDate}
                  onChange={(e) => setEditingInvoice({
                    ...editingInvoice,
                    invoiceDate: e.target.value
                  })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#004141] focus:outline-none transition-all"
                />
              ) : (
                <div className="w-full p-3 bg-gray-50 text-gray-800 rounded-md">
                  {invoice._doc.invoiceDate || 'N/A'}
                </div>
              )}
            </div>

            <div className="flex flex-col">
              <label className="text-sm font-semibold text-gray-600 mb-2">Due Date</label>
              {isEditing ? (
                <input
                  type="date"
                  value={editingInvoice.dueDate}
                  onChange={(e) => setEditingInvoice({
                    ...editingInvoice,
                    dueDate: e.target.value
                  })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#004141] focus:outline-none transition-all"
                />
              ) : (
                <div className="w-full p-3 bg-gray-50 text-gray-800 rounded-md">
                  {invoice._doc.dueDate || 'N/A'}
                </div>
              )}
            </div>
          </div>

          <div className="flex flex-col">
            <label className="text-sm font-semibold text-gray-600 mb-2">Total Amount</label>
            {isEditing ? (
              <input
                type="number"
                value={editingInvoice.totalAmount}
                onChange={(e) => setEditingInvoice({
                  ...editingInvoice,
                  totalAmount: e.target.value
                })}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#004141] focus:outline-none transition-all"
              />
            ) : (
              <div className="w-full p-3 bg-gray-50 text-gray-800 rounded-md">
                {formatAmount(invoice._doc.totalAmount)}
              </div>
            )}
          </div>

          <div className="flex flex-col">
            {isEditing ? ( // When in explicit "Edit Invoice Details" mode (not the primary concern of the request)
              <>
                <label className="text-sm font-semibold text-gray-600 mb-2">Buyer Name</label>
                <input
                  type="text"
                  value={editingInvoice.supplierName}
                  onChange={(e) => setEditingInvoice({
                    ...editingInvoice,
                    supplierName: e.target.value
                  })}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#004141] focus:outline-none transition-all"
                />
              </>
            ) : isUploadContext && mindeeData ? ( // In Upload Modal Step 1, after Mindee extraction
              <div className="space-y-1">
                <div className="flex justify-between items-center mb-1">
                  {finalDropdownEditableState && (
                    <button
                      type="button"
                      onClick={() => {
                        handleOpenBuyersModal(); // This opens the buyers modal
                      }}
                      className="text-xs text-[#004141] hover:underline font-medium flex items-center"
                      title="Add or manage your list of approved buyers"
                    >
                      <PlusIcon className="h-4 w-4 mr-0.5" />
                      Add/Manage Buyers
                    </button>
                  )}
                </div>
                <div className="relative">
                  <select
                    id="buyerDropdown"
                    value={selectedKycBuyerId} // Controlled by this state
                    onChange={handleBuyerDropdownChange}
                    disabled={!finalDropdownEditableState || !isBuyerValid} // Disable if not editable OR if overall buyer state is invalid (e.g. duplicate invoice)
                    className={`w-full p-3 border rounded-md focus:ring-2 focus:ring-[#004141] focus:outline-none transition-colors text-sm appearance-none pr-8 
        ${(!finalDropdownEditableState || !isBuyerValid)
                        ? 'bg-gray-100 border-gray-300 text-gray-700 cursor-not-allowed' // Locked or invalid
                        : (kycBuyers.length === 0)
                          ? 'bg-white border-red-300 text-red-600 focus:border-red-500 focus:ring-red-200' // No buyers, editable
                          : 'bg-white border-gray-300 text-gray-800 focus:border-[#004141]' // Default editable
                      }`}
                  >
                    {(!finalDropdownEditableState && selectedKycBuyerId && kycBuyers.find(b => b._id === selectedKycBuyerId)) ? (
                      <option value={selectedKycBuyerId}>
                        {kycBuyers.find(b => b._id === selectedKycBuyerId)?.buyerName}
                      </option>
                    ) : (
                      <>
                        <option value="">
                          {kycBuyers.length === 0 ? "No buyers in profile. Use 'Add/Manage Buyers'." :
                            originalExtractedBuyerName && !selectedKycBuyerId ? `Extracted: "${originalExtractedBuyerName}". Select or Add...` :
                              "-- Select Approved Buyer --"}
                        </option>
                        {kycBuyers.map((buyer) => (
                          <option key={buyer._id} value={buyer._id}>
                            {buyer.buyerName}
                          </option>
                        ))}
                      </>
                    )}
                  </select>
                  {(finalDropdownEditableState && kycBuyers.length > 0) && (
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                      <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" /></svg>
                    </div>
                  )}
                </div>
                {buyerNameFieldWarning && (
                  <p className="text-xs text-yellow-700 mt-1 bg-yellow-50 border border-yellow-200 p-1.5 rounded">
                    <ExclamationTriangleIcon className="h-4 w-4 inline mr-1 text-yellow-600" />
                    {buyerNameFieldWarning}
                  </p>
                )}
                {/* The main 'buyerValidationError' is displayed globally above the form */}
              </div>
            ) : (
              // Original static display for View Modal (already submitted invoice) or if mindeeData not yet ready
              <>
                <label className="text-sm font-semibold text-gray-600 mb-2">Buyer Name</label>
                <div className="w-full p-3 bg-gray-50 text-gray-800 rounded-md text-sm">
                  {extractedBuyerNameFromInvoice || (isUploadContext && !mindeeData ? 'Processing...' : 'N/A')}
                </div>
              </>
            )}
          </div>

          <div className="flex flex-col">
            <label className="text-sm font-semibold text-gray-600 mb-2">Customer Name</label>
            {isEditing ? (
              <input
                type="text"
                value={editingInvoice.customerName}
                onChange={(e) => setEditingInvoice({
                  ...editingInvoice,
                  customerName: e.target.value
                })}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#004141] focus:outline-none transition-all"
              />
            ) : (
              <div className="w-full p-3 bg-gray-50 text-gray-800 rounded-md">
                {invoice._doc.customerName || 'N/A'}
              </div>
            )}
          </div>

          <div className="flex items-center space-x-3 mt-2 mb-2">
            <label className="text-sm font-semibold text-gray-600">Status:</label>
            <span className={`${getStatusStyle(invoice._doc.status)} px-3 py-1 rounded-md text-sm font-medium`}>
              {getStatusDisplay(invoice._doc.status)}
            </span>
          </div>

          <div className="flex flex-col">
            <label className="text-sm font-semibold text-gray-600 mb-2">Billing Address</label>
            {isEditing ? (
              <textarea
                value={editingInvoice.billingAddress}
                onChange={(e) => setEditingInvoice({
                  ...editingInvoice,
                  billingAddress: e.target.value
                })}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#004141] focus:outline-none transition-all"
                rows={3}
              />
            ) : (
              <div className="w-full p-3 bg-gray-50 text-gray-800 rounded-md">
                {invoice._doc.billingAddress || 'N/A'}
              </div>
            )}
          </div>

          <div className="flex flex-col">
            <label className="text-sm font-semibold text-gray-600 mb-2">Customer Address</label>
            {isEditing ? (
              <textarea
                value={editingInvoice.customerAddress}
                onChange={(e) => setEditingInvoice({
                  ...editingInvoice,
                  customerAddress: e.target.value
                })}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#004141] focus:outline-none transition-all"
                rows={3}
              />
            ) : (
              <div className="w-full p-3 bg-gray-50 text-gray-800 rounded-md">
                {invoice._doc.customerAddress || 'N/A'}
              </div>
            )}
          </div>
        </div>

        {(!invoice._doc?._id || invoice._doc?.status === 'VERIFICATION_PENDING') && (
          <div className="mt-6">
            {!isEditing ? (
              <></>
              // <button
              //   onClick={() => {
              //     setEditingInvoice(invoice._doc || mindeeData);
              //   }}
              //   className="w-full px-4 py-2.5 text-lg bg-[#004141] text-white rounded-md transition-colors duration-300 ease-in-out font-semibold"
              // >
              //   Edit Invoice Details
              // </button>
            ) : (
              <div className="grid grid-cols-2 gap-4">
                <button
                  onClick={handleUpdateInvoice}
                  className="px-4 py-2.5 text-lg bg-[#004141] text-white rounded-md transition-colors duration-300 ease-in-out font-semibold" disabled={isLoading}
                >
                  {isLoading ? 'Saving...' : 'Save Changes'}
                </button>
                <button
                  onClick={() => setEditingInvoice(null)}
                  className="px-4 py-2.5 text-lg bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-300 ease-in-out font-semibold"
                >
                  Cancel
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  const fetchLenderDetails = async (lenderId, token) => {
    try {
      const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/lenders/${lenderId}`, {
        headers: { 'x-auth-token': token }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching lender details for ID ${lenderId}:`, error);
      return null;
    }
  };

  const fetchLender = async (lenderIdFromCreditLine) => { // Renamed param for clarity
    console.log(`MyBuyersPage: Attempting to fetch lender details for ID: ${lenderIdFromCreditLine}`);
    const lenderData = await fetchLenderDetails(lenderIdFromCreditLine, token);
    if (lenderData) {
      setAcceptedLenderDetails(lenderData);
      console.log("MyBuyersPage: Successfully fetched and set acceptedLenderDetails:", lenderData);
    } else {
      setAcceptedLenderDetails(null);
      console.log("MyBuyersPage: fetchLenderDetails returned null or empty data.");
    }
  };

  const fetchCreditLine = async () => {
    try {
      const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/fetchCreditLine/${userId}`, {
        headers: {
          'x-auth-token': token,
        },
      });
      setCreditLine(response.data);
      if (response.data) {
        console.log("MyBuyersPage: creditLine exists.");
        if (response.data.lenderId) { // Add this check!
          console.log("MyBuyersPage: creditLine.lenderInfo is NOT present, but lenderId IS:", response.data.lenderId);
          fetchLender(response.data.lenderId); // If lenderInfo is missing but lenderId exists, proceed
        } else {
          console.log("MyBuyersPage: creditLine.lenderInfo and creditLine.lenderId are missing or null.");
          setAcceptedLenderDetails(null);
        }
      } else {
        console.log("MyBuyersPage: creditLine is null or undefined.");
        setAcceptedLenderDetails(null);
      }
    } catch (error) {
      console.error('Error fetching credit line:', error);
      setCreditLineError(error.response?.data?.message || 'Failed to fetch credit line.');
      setCreditLine(null);
    }
  };

  const isCreditLineEligible = (creditLine) => {
    return (
      (creditLine.creditLineStatus === 'APPROVED' || creditLine.creditLineStatus === 'ACTIVE') &&
      creditLine.offerAccepted === true
    );
  };

  const fetchInvoices = async () => {
    try {
      setIsLoading(true);

      const user = SharedCache.get("user") || {};
      const userId = user._id || user.id;
      const token = SharedCache.get("token") || "placeholdertoken";

      let url = `${config.apiUrl}/ops/invoiceFinancing/fetchInvoices`;
      const headers = {
        'x-auth-token': token
      };
      const params = {};

      if (userId) {
        url += `?userId=${userId}`;
      }

      console.log("Fetching invoices from:", url);

      const response = await axios.get(url, { headers, params });

      console.log("Response from fetchInvoices:", response);

      let invoices = [];
      if (response.data && Array.isArray(response.data)) {
        invoices = response.data;
      } else if (response.data && Array.isArray(response.data.data)) {
        invoices = response.data.data;
      } else if (response.data && response.data.message === 'No invoices found.') {
        invoices = [];
      } else {
        console.error('Unexpected response format:', response);
      }

      const statusPriority = {
        'VERIFIED_ANCHOR': 0,
        'ACCEPTED_LENDER': 1,
        'VERIFICATION_PENDING_LENDER': 2,
        'VERIFICATION_PENDING_ANCHOR': 3,
        'MORE_INFO_NEEDED_ANCHOR': 4,
        'MORE_INFO_NEEDED_LENDER': 5,
        'REJECTED_ANCHOR': 6,
        'REJECTED_LENDER': 7,
      };

      const sortedInvoices = [...invoices].sort((a, b) => {
        const priorityA = statusPriority[a.status] ?? 99;
        const priorityB = statusPriority[b.status] ?? 99;

        if (priorityA === priorityB) {
          return new Date(b.uploadedAt) - new Date(a.uploadedAt);
        }

        return priorityA - priorityB;
      });

      setInvoices(
        sortedInvoices.map(invoice => ({
          ...invoice,
          _doc: invoice._doc || invoice
        }))
      );

    } catch (error) {
      console.error('Error fetching invoices:', error);
      setInvoices([]);
    } finally {
      setIsLoading(false);
    }
  };

  const STATUS_STYLES = {
    'APPOVAL_PENDING': { bg: 'bg-yellow-200', text: 'text-yellow-800' },
    'VERIFICATION_PENDING_ANCHOR': { bg: 'bg-yellow-200', text: 'text-yellow-800' },
    'VERIFIED_ANCHOR': { bg: 'bg-[#44d282]', text: 'text-white' },
    'VERIFICATION_PENDING_LENDER': { bg: 'bg-yellow-200', text: 'text-yellow-800' },
    'ACCEPTED_LENDER': { bg: 'bg-blue-100', text: 'text-blue-800' },
    'MORE_INFO_NEEDED_ANCHOR': { bg: 'bg-orange-100', text: 'text-orange-800' },
    'MORE_INFO_NEEDED_LENDER': { bg: 'bg-orange-100', text: 'text-orange-800' },
    'REJECTED_ANCHOR': { bg: 'bg-red-100', text: 'text-red-800' },
    'REJECTED_LENDER': { bg: 'bg-red-100', text: 'text-red-800' },
    'READY_FOR_DISBURSAL': { bg: 'bg-[#33a8f8]', text: 'text-white' },
    'DISBURSED': { bg: 'bg-[#57b78a]', text: 'text-white' },
  };

  const STATUS_DISPLAY_NAMES = {
    'APPOVAL_PENDING': 'Approval Pending',
    'VERIFICATION_PENDING_ANCHOR': 'Verification Pending',
    'VERIFIED_ANCHOR': 'Verified (Buyer)',
    'VERIFICATION_PENDING_LENDER': 'Verification Pending',
    'ACCEPTED_LENDER': 'Accepted (Lender)',
    'MORE_INFO_NEEDED_ANCHOR': 'More Info Needed',
    'MORE_INFO_NEEDED_LENDER': 'More Info Needed',
    'REJECTED_ANCHOR': 'Rejected (Buyer)',
    'REJECTED_LENDER': 'Rejected (Lender)',
    'READY_FOR_DISBURSAL': 'Ready For Disbursal',
    'DISBURSED': 'Disbursed',
  };

  const handleUploadSubmit = async () => {
    if (!uploadedFile || !mindeeData) return;

    try {
      setIsLoading(true);

      const formData = new FormData();
      formData.append('pdfFile', uploadedFile);

      formData.append('userId', userId);
      formData.append('panNo', user.panNo || '');
      formData.append('gstin', user.gstin || '');

      formData.append('invoiceNumber', mindeeData.invoiceNumber);
      formData.append('invoiceDate', mindeeData.invoiceDate);
      formData.append('dueDate', mindeeData.dueDate);
      formData.append('totalAmount', mindeeData.totalAmount);
      formData.append('supplierName', mindeeData.supplierName);
      formData.append('customerName', mindeeData.customerName);
      formData.append('billingAddress', mindeeData.billingAddress);
      formData.append('customerAddress', mindeeData.customerAddress);
      formData.append('status', "APPROVAL_PENDING");

      const uploadResponse = await axios.post(
        `${config.apiUrl}/ops/invoiceFinancing/uploadInvoice`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            'x-auth-token': token
          }
        }
      );

      console.log(uploadResponse);

      await fetchInvoices();
      setShowUploadModal(false);
      setShowPdfPreview(false);
      setUploadedFile(null);
      setMindeeData(null);

    } catch (error) {
      console.error('Error uploading invoice:', error);
      alert('Failed to upload invoice. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewInvoice = (invoiceFromList) => {
    // Find the full invoice object from the state using the ID
    const fullInvoice = invoices.find(inv => inv._doc._id === invoiceFromList._doc._id);

    if (fullInvoice && fullInvoice._doc.signedUrl) {
      setViewingInvoice(fullInvoice); // Store the full invoice object
      setPdfUrl(fullInvoice._doc.signedUrl); // Set URL for main PDF preview
      setMindeeData(null); // Clear Mindee data if any
      setEditingInvoice(null); // Ensure not in editing mode
      setAdditionalDocError(''); // Clear previous errors
      setShowPdfPreview(true); // Open the preview modal
    } else {
      console.error('No signed URL found or invoice not found for:', invoiceFromList._doc.invoiceNumber);
      // Optionally show a fallback or an error message
      alert("Could not load invoice preview.");
      // Example fallback:
      // setViewingInvoice(fullInvoice); // Still set the invoice data if available
      // setPdfUrl("/assets/invoice-placeholder.pdf"); // A placeholder PDF
      // setMindeeData(null);
      // setEditingInvoice(null);
      // setAdditionalDocError('');
      // setShowPdfPreview(true);
    }
  };

  const getStatusStyle = (status) => {
    const style = STATUS_STYLES[status] || { bg: 'bg-gray-100', text: 'text-gray-800' };
    return `px-2 py-1 rounded-md text-sm ${style.bg} ${style.text}`;
  };

  const getStatusDisplay = (status) => {
    return STATUS_DISPLAY_NAMES[status] || status;
  };

  // Inside MyInvoicesPage component, add this function:
  const handleViewContractClick = (url) => {
    console.log("URL HERE", url);
    if (url) {
      setContractPdfUrl(url);
      setShowContractModal(true);
    } else {
      console.error("Attempted to view contract, but no URL was provided.");
      alert("Could not load contract document.");
    }
  };

  // Inside MyInvoicesPage component, add this function:
  const renderContractModal = () => {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-70 flex justify-center items-center z-50 p-4">
        <div className="bg-white p-5 rounded-lg shadow-xl w-full max-w-4xl h-[90vh] flex flex-col">
          {/* Modal Header */}
          <div className="flex justify-between items-center mb-4 border-b pb-3">
            <h3 className="text-xl font-semibold text-gray-800">
              Signed Contract Document
            </h3>
            <button
              onClick={() => {
                setShowContractModal(false);
                setContractPdfUrl(''); // Clear URL on close
              }}
              className="text-gray-500 hover:text-gray-800 p-1 rounded-full hover:bg-gray-200 transition-colors"
              aria-label="Close contract view"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          {/* Modal Body */}

          <div className="flex-grow h-[calc(100%-4rem)]">
            {contractPdfUrl ? (
              <iframe
                src={contractPdfUrl}
                className="w-full h-full border-0"
                title="Signed Contract PDF"
              />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                Loading contract document...
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Replace the entire existing renderActionButton function with this corrected version:
  const renderActionButton = (invoice) => {
    // Ensure invoice and invoice._doc exist before destructuring
    if (!invoice?._doc) {
      console.warn("renderActionButton called with invalid invoice object:", invoice);
      return null; // Or return a placeholder/disabled button
    }
    const { status: invoiceStatus, _id: invoiceId } = invoice._doc;

    // Find the relevant offer linked to this invoice in the offersData state
    const relevantOffer = Array.isArray(offersData)
      ? offersData.find(offer =>
        offer.invoiceId === invoiceId && offer.offerType === 'invoiceDiscountingOffer'
      )
      : null;

    const contractViewableOfferStatuses = [
      'LOAN_CONTRACT_ACCEPTED', 'LOAN_IN_PROGRESS', 'PAID', 'OVERDUE', 'DEFAULTED', 'WRITTEN_OFF'
    ];

    // --- START: Add check for MORE_INFO_NEEDED_ANCHOR ---
    if (invoiceStatus === 'MORE_INFO_NEEDED_ANCHOR') {
      return (
        <button
          className="bg-red-500 text-white px-3 py-1 rounded-md hover:bg-orange-600 text-xs font-medium whitespace-nowrap"
          onClick={() => handleOpenReUploadModal(invoiceId)} // Use the new handler
          title={`Re-upload additional documents for invoice ${invoice._doc.invoiceNumber}`}
        >
          Re-upload Docs
        </button>
      );
    }
    // --- END: Add check for MORE_INFO_NEEDED_ANCHOR ---

    // --- Check for "View Contract" condition ---
    if (relevantOffer && contractViewableOfferStatuses.includes(relevantOffer.status) && relevantOffer.invoiceContract?.signedUrl) {
      return (
        <button
          className="bg-green-600 text-white px-3 py-1 rounded-md hover:bg-green-700 text-xs font-medium whitespace-nowrap"
          onClick={() => handleViewContractClick(relevantOffer.invoiceContract.signedUrl)}
          title={`View signed contract for invoice ${invoice._doc.invoiceNumber}`}
        >
          View Contract
        </button>
      );
    }

    // --- Check for "Sign Contract" condition ---
    if (invoiceStatus === 'ACCEPTED_LENDER' && creditLine?.creditLineStatus === "ACTIVE") {
      return (
        <button
          className="bg-[#004141] text-white px-3 py-1 rounded-md hover:bg-opacity-90 text-xs font-medium whitespace-nowrap"
          onClick={() => {
            window.location.href = `/invoiceContract/invoice-offer?id=${invoiceId}`;
          }}
          title={`Sign contract for invoice ${invoice._doc.invoiceNumber}`}
        >
          Sign Contract
        </button>
      );
    }

    // --- Check for "Submit for Discounting" condition (Disabled state) ---
    if (invoiceStatus === 'VERIFIED_ANCHOR' && creditLine?.creditLineStatus === "ACTIVE") {
      return (
        <button
          disabled
          className="bg-gray-300 text-gray-500 px-3 py-1 rounded-md cursor-not-allowed text-xs whitespace-nowrap"
          title="Invoice verified. Awaiting lender offer."
        >
          Submit Discounting
        </button>
      );
    }

    // --- Default Disabled Button ---
    let reason = 'No action available for this status.';
    if (invoiceStatus !== 'VERIFIED_ANCHOR' && invoiceStatus !== 'ACCEPTED_LENDER' && invoiceStatus !== 'MORE_INFO_NEEDED_ANCHOR' && !(relevantOffer && contractViewableOfferStatuses.includes(relevantOffer.status))) {
      reason = 'Action available after invoice/offer status update.';
    } else if (creditLine?.creditLineStatus !== 'ACTIVE' && (invoiceStatus === 'VERIFIED_ANCHOR' || invoiceStatus === 'ACCEPTED_LENDER')) {
      reason = `Action requires ACTIVE credit line (Current: ${creditLine?.creditLineStatus || 'N/A'}).`;
    }


    return (
      <button
        className="bg-gray-300 text-gray-500 px-3 py-1 rounded-md cursor-not-allowed text-xs whitespace-nowrap"
        disabled
        title={reason}
      >
        {/* Adjust default text based on potential next steps if needed */}
        {invoiceStatus === 'ACCEPTED_LENDER' ? 'Sign Contract' : 'None'}
      </button>
    );
  };

  // --- START: Add function to render the Re-upload Modal ---
  const renderReUploadModal = () => {
    // Find the specific invoice details from the main state
    const invoice = invoices.find(inv => inv._doc._id === reUploadInvoiceId);

    if (!invoice || !invoice._doc) {
      // Fallback if invoice data isn't found (should ideally not happen if modal is opened correctly)
      return (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex justify-center items-center z-50 p-4">
          <div className="bg-white p-6 rounded-lg shadow-xl w-full max-w-lg">
            <h3 className="text-lg font-semibold text-red-600 mb-4">Error</h3>
            <p className="text-gray-700 mb-4">Could not load invoice details.</p>
            <button
              onClick={() => {
                setShowReUploadModal(false);
                setReUploadInvoiceId(null);
              }}
              className="w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
            >
              Close
            </button>
          </div>
        </div>
      );
    }

    const currentDocs = invoice._doc.additionalInvoiceDocuments || [];
    const canUploadMore = currentDocs.length < 10;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-70 flex justify-center items-center z-50 p-4">
        <div className="bg-white p-5 md:p-6 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col">
          {/* Modal Header */}
          <div className="flex justify-between items-center mb-4 border-b pb-3">
            <h3 className="text-lg md:text-xl font-semibold text-gray-800">
              Re-upload Documents for Invoice: {invoice._doc.invoiceNumber}
            </h3>
            <button
              onClick={() => {
                setShowReUploadModal(false);
                setReUploadInvoiceId(null);
              }}
              className="text-gray-500 hover:text-gray-800 p-1 rounded-full hover:bg-gray-200 transition-colors"
              aria-label="Close re-upload modal"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Modal Body (Scrollable) */}
          <div className="flex-grow overflow-y-auto mb-4 pr-1 scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100">
            <p className="text-sm text-orange-700 bg-orange-50 border border-orange-200 p-3 rounded-md mb-4 flex items-start">
              <ExclamationTriangleIcon className="h-5 w-5 mr-2 text-orange-500 flex-shrink-0 mt-0.5" />
              <span>More information is needed for this invoice. Please upload relevant supporting documents (e.g., Purchase Order, Delivery Confirmation).</span>
            </p>

            {/* Existing Documents Section */}
            <h4 className="text-base font-semibold text-gray-700 mb-2">
              Currently Uploaded Documents ({currentDocs.length}/10)
            </h4>
            <div className="space-y-2 mb-4 max-h-48 overflow-y-auto pr-1 border rounded-md p-2 scrollbar-thin">
              {currentDocs.length > 0 ? (
                currentDocs.map((doc, index) => (
                  doc && ( // Ensure doc object is valid
                    <div key={doc._id || index} className="flex items-center justify-between bg-gray-50 p-2 rounded border border-gray-200 text-sm">
                      <div className="flex items-center space-x-2 overflow-hidden mr-2">
                        <LinkIcon className="h-4 w-4 text-blue-500 flex-shrink-0" />
                        <span className="text-gray-700 truncate" title={getFilenameFromPath(doc.filePath)}>
                          {shortenDocumentName(getFilenameFromPath(doc.filePath))}
                        </span>
                      </div>
                      {doc.signedUrl ? (
                        <a
                          href={doc.signedUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-xs text-blue-600 hover:underline font-medium flex-shrink-0 px-2 py-1 rounded hover:bg-blue-100"
                          title={`View Document (Uploaded: ${doc.uploadedOn ? new Date(doc.uploadedOn).toLocaleDateString() : 'N/A'})`}
                        >
                          View
                        </a>
                      ) : (
                        <span className="text-xs text-gray-400 italic">No link</span>
                      )}
                    </div>
                  )
                ))
              ) : (
                <p className="text-sm text-gray-500 italic py-2 text-center">No additional documents currently uploaded.</p>
              )}
            </div>

            {/* Upload Section */}
            <h4 className="text-base font-semibold text-gray-700 mb-2 mt-4">Add New Document</h4>
            {reUploadError && (
              <p className="text-sm text-red-600 mb-2">{reUploadError}</p>
            )}
            <input
              type="file"
              ref={reUploadFileInputRef}
              className="hidden"
              accept=".pdf,.jpg,.jpeg,.png"
              onChange={handleReUploadAdditionalDocument} // Use the specific handler
            />
            <button
              onClick={() => reUploadFileInputRef.current?.click()}
              className={`w-full flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors ${(!canUploadMore || isReUploading)
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-[#004141] text-white hover:bg-opacity-90'
                }`}
              disabled={!canUploadMore || isReUploading}
              title={!canUploadMore ? "Maximum 10 documents allowed" : "Upload PDF, JPG, PNG"}
            >
              {isReUploading ? (
                <><ArrowPathIcon className="w-4 h-4 mr-2 animate-spin" /> Uploading...</>
              ) : (
                <><DocumentArrowUpIcon className="w-5 h-5 mr-2" /> Select & Upload Document</>
              )}
            </button>
            {!canUploadMore && (
              <p className="text-xs text-center text-gray-500 mt-1">Limit reached</p>
            )}
          </div>

          {/* Modal Footer (Close Button) */}
          <div className="flex-shrink-0 mt-auto pt-4 border-t flex justify-end">
            <button
              onClick={() => {
                setShowReUploadModal(false);
                setReUploadInvoiceId(null);
              }}
              className="px-5 py-2 text-base bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  };
  // --- END: Add function to render the Re-upload Modal ---

  const formatAmount = (amount) => {
    if (!amount) return 'N/A';
    if (typeof amount === 'string' && amount.includes('QAR')) {
      return amount;
    }
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    if (isNaN(numAmount)) return amount;
    return `QAR ${numAmount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  // --- START: Buyer Management Modal Functions ---

  const handleOpenBuyersModal = () => {
    if (!userInfo?.kyc?.buyers) {
      console.warn("Cannot open buyers modal: userInfo or buyers list is missing.");
      // Initialize with empty array if missing to prevent errors downstream
      setModalBuyers([]);
    } else {
      // Deep copy buyers to prevent direct mutation of userInfo state
      // Include default fields to ensure consistency if some are missing from DB
      const initialModalBuyers = userInfo.kyc.buyers.map(b => ({
        _id: b._id, // Keep existing ID if present
        buyerName: b.buyerName || '',
        contactEmail: b.contactEmail || '',
        contactPerson: b.contactPerson || '',
        registrationNumber: b.registrationNumber || '',
        contactPhone: b.contactPhone || '',
        // Add other fields from schema if needed
      }));
      setModalBuyers(initialModalBuyers);
    }
    // Reset the form for adding a new buyer
    setNewBuyer({
      buyerName: '',
      contactEmail: '',
      contactPerson: '',
      registrationNumber: '',
      contactPhone: '',
    });
    setBuyersModalError('');
    setIsSavingBuyers(false);
    setShowBuyersModal(true);
  };

  const handleCloseBuyersModal = () => {
    setShowBuyersModal(false);
    // Optionally clear state if you don't need to retain it
    // setModalBuyers([]);
    // setNewBuyer({...});
    // setBuyersModalError('');
  };

  const handleNewBuyerInputChange = (e) => {
    const { name, value } = e.target;
    setNewBuyer(prev => ({ ...prev, [name]: value }));
  };

  const handleAddNewBuyerToList = () => {
    setBuyersModalError(''); // Clear previous errors

    // Basic Validation
    if (!newBuyer.buyerName?.trim()) {
      setBuyersModalError('Buyer Name is required.');
      return;
    }
    if (!newBuyer.contactEmail?.trim()) {
      setBuyersModalError('Buyer Contact Email is required.');
      return;
    }
    // Optional: Add email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(newBuyer.contactEmail.trim())) {
      setBuyersModalError('Please enter a valid email address.');
      return;
    }

    // Duplicate Check within the current modal list + new buyer
    const potentialBuyers = [...modalBuyers, newBuyer];
    const seenCombinations = new Map();
    let duplicateFound = false;
    for (const buyer of potentialBuyers) {
      const normalizedName = buyer.buyerName?.trim().toLowerCase();
      const normalizedEmail = buyer.contactEmail?.trim().toLowerCase();
      if (normalizedName && normalizedEmail) {
        const key = `${normalizedName}|||${normalizedEmail}`;
        if (seenCombinations.has(key)) {
          duplicateFound = true;
          setBuyersModalError(`Duplicate entry: The combination of Buyer Name "${buyer.buyerName}" and Email "${buyer.contactEmail}" already exists in the list.`);
          break;
        }
        seenCombinations.set(key, true);
      }
    }

    if (duplicateFound) {
      return; // Stop if duplicate found
    }

    // Add to list (without _id as it's new)
    setModalBuyers(prev => [...prev, { ...newBuyer }]);

    // Reset form
    setNewBuyer({
      buyerName: '',
      contactEmail: '',
      contactPerson: '',
      registrationNumber: '',
      contactPhone: '',
    });
  };

  const handleDeleteBuyer = (indexToDelete) => {
    // Optional: Add confirmation dialog
    // if (!window.confirm(`Are you sure you want to remove buyer "${modalBuyers[indexToDelete]?.buyerName}"?`)) {
    //   return;
    // }
    setModalBuyers(prev => prev.filter((_, index) => index !== indexToDelete));
  };

  const handleSaveBuyers = async () => {
    setBuyersModalError('');
    setIsSavingBuyers(true);

    const user = SharedCache.get("user") || {};
    const currentUserId = user._id || user.id || "";
    const currentToken = SharedCache.get("token") || "placeholdertoken";
    const currentMsmeEmail = user.email;

    if (!currentUserId) {
      setBuyersModalError("User session invalid. Cannot save.");
      setIsSavingBuyers(false);
      return;
    }

    // --- START: Identify newly added buyers BEFORE saving ---
    // A buyer is "new" if they do not have an _id (which comes from the database)
    const newlyAddedBuyersForInvitation = modalBuyers.filter(b => !b._id);
    // --- END: Identify newly added buyers ---

    // --- Final Duplicate Check (existing logic - ensure it's thorough) ---
    // (Your existing duplicate check logic for modalBuyers)
    const seenCombinations = new Map();
    let duplicateFound = false;
    for (let i = 0; i < modalBuyers.length; i++) {
      const buyer = modalBuyers[i];
      const normalizedName = buyer.buyerName?.trim().toLowerCase();
      const normalizedEmail = buyer.contactEmail?.trim().toLowerCase();
      if (normalizedName && normalizedEmail) {
        const key = `${normalizedName}|||${normalizedEmail}`;
        if (seenCombinations.has(key)) {
          duplicateFound = true;
          const firstIndex = seenCombinations.get(key);
          const errorMessage = `Cannot save: Duplicate entry detected for Buyer Name "${buyer.buyerName}" and Email "${buyer.contactEmail}" (Buyers ${firstIndex + 1} and ${i + 1}).`;
          setBuyersModalError(errorMessage);
          toast.error(errorMessage);
          break;
        }
        seenCombinations.set(key, i);
      } else if (!normalizedName || !normalizedEmail) {
        setBuyersModalError(`Buyer ${i + 1} is missing a required Name or Email.`);
        duplicateFound = true;
        break;
      }
    }
    if (duplicateFound) {
      setIsSavingBuyers(false);
      return;
    }
    // --- End Final Duplicate Check ---

    const buyersPayload = modalBuyers.map(b => ({
      ...(b._id && { _id: b._id }), // Only include _id if it exists (for updates)
      buyerName: b.buyerName,
      contactEmail: b.contactEmail,
      contactPerson: b.contactPerson,
      registrationNumber: b.registrationNumber,
      contactPhone: b.contactPhone,
    }));

    const updatePayload = {
      userId: currentUserId,
      kyc: { buyers: buyersPayload }
    };

    const API_ENDPOINT = `${config.apiUrl}/ops/invoiceFinancing/updateKyc`;

    try {
      const response = await axios.post(API_ENDPOINT, updatePayload, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': currentToken
        }
      });

      if (!response.data?.success) {
        throw new Error(response.data?.message || "Failed to save buyers via API.");
      }

      console.log("Buyers saved successfully via API:", response.data);
      toast.success("Buyers updated successfully!");

      // Re-fetch user info to ensure the local state `userInfo` (which includes kyc.buyers) is fresh.
      // This is important because the backend `sendBuyerInvitationEmail` API might rely on
      // the buyer already existing in the database under this MSME.
      await fetchUserInfo();

      // --- START: Send Invitation Emails ONLY to NEWLY ADDED Buyers ---
      if (currentMsmeEmail && newlyAddedBuyersForInvitation.length > 0) {
        toast.info(`Processing invitations for ${newlyAddedBuyersForInvitation.length} new buyer(s)...`);
        let emailsSentSuccessfully = 0;
        let emailSendErrors = 0;

        for (const newBuyer of newlyAddedBuyersForInvitation) {
          if (newBuyer.contactEmail) { // Ensure the new buyer has an email
            try {
              // The backend API /api/registration/send-buyer-invitation
              // will verify if this buyer (by email) now exists under the MSME.
              await sendBuyerInvitationEmail(currentMsmeEmail, newBuyer.contactEmail);
              console.log(`Invitation processed for new buyer: ${newBuyer.contactEmail}`);
              emailsSentSuccessfully++;
            } catch (emailError) {
              console.error(`Failed to send invitation to new buyer ${newBuyer.contactEmail}:`, emailError);
              toast.error(`Invitation failed for ${newBuyer.contactEmail}: ${emailError.message || 'Unknown error'}`);
              emailSendErrors++;
            }
          }
        }

        if (emailsSentSuccessfully > 0) {
          toast.success(`${emailsSentSuccessfully} new buyer invitation(s) processed.`);
        }
        if (emailSendErrors > 0) {
          toast.warning(`${emailSendErrors} new buyer invitation(s) could not be sent. Check console for details.`);
        }
      } else if (!currentMsmeEmail) {
        toast.warn("Could not process invitations: MSME user email is missing.");
      } else if (newlyAddedBuyersForInvitation.length === 0) {
        console.log("No new buyers were added in this save operation to send invitations to.");
      }
      // --- END: Send Invitation Emails ---

      handleCloseBuyersModal();

    } catch (error) {
      console.error("Error saving buyers:", error);
      const errorMsg = error.response?.data?.message || error.message || "An unknown error occurred while saving.";
      setBuyersModalError(`Save Failed: ${errorMsg}`);
      toast.error(`Save Failed: ${errorMsg}`);
    } finally {
      setIsSavingBuyers(false);
    }
  };
  // --- END: Buyer Management Modal Functions ---

  // --- START: Buyers Modal Renderer ---
  const renderBuyersModal = () => {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4 overflow-y-auto">
        <div className="bg-white p-5 md:p-6 rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] flex flex-col">

          {/* Modal Header */}
          <div className="flex-shrink-0 flex justify-between items-center mb-4 border-b pb-3">
            <h3 className="text-xl md:text-2xl font-semibold text-gray-800">
              Manage Buyers
            </h3>
            <button
              onClick={handleCloseBuyersModal}
              className="text-gray-500 hover:text-gray-800 p-1 rounded-full hover:bg-gray-200 transition-colors"
              aria-label="Close buyers modal"
              disabled={isSavingBuyers}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Modal Body (Scrollable) */}
          <div className="flex-grow overflow-y-auto mb-4 pr-2 space-y-6 scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100">

            {/* Section 1: Existing Buyers List */}
            <div>
              <h4 className="text-lg font-semibold text-gray-700 mb-3">Existing Buyers ({modalBuyers.length})</h4>
              <div className="space-y-3 max-h-60 overflow-y-auto border rounded-md p-3 bg-gray-50 scrollbar-thin">
                {modalBuyers.length > 0 ? (
                  modalBuyers.map((buyer, index) => (
                    <div key={buyer._id || `buyer-${index}`} className="flex items-start justify-between bg-white p-3 rounded border border-gray-200 shadow-sm gap-4">
                      <div className="flex-grow text-sm space-y-1">
                        <p><span className="font-semibold text-gray-600">Name:</span> {buyer.buyerName}</p>
                        <p><span className="font-semibold text-gray-600">Email:</span> {buyer.contactEmail}</p>
                        {buyer.contactPerson && <p><span className="font-semibold text-gray-600">Contact:</span> {buyer.contactPerson}</p>}
                        {buyer.registrationNumber && <p><span className="font-semibold text-gray-600">Reg No:</span> {buyer.registrationNumber}</p>}
                        {buyer.contactPhone && <p><span className="font-semibold text-gray-600">Phone:</span> {buyer.contactPhone}</p>}
                      </div>
                      <button
                        onClick={() => handleDeleteBuyer(index)}
                        className="flex-shrink-0 text-red-500 hover:text-red-700 p-1 rounded-full hover:bg-red-100 transition-colors mt-1"
                        title={`Remove buyer ${buyer.buyerName}`}
                        disabled={isSavingBuyers}
                      >
                        <XCircleIcon className="w-5 h-5" />
                      </button>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-500 italic text-center py-4">No buyers added yet.</p>
                )}
              </div>
            </div>

            {/* Section 2: Add New Buyer Form */}
            <div>
              <h4 className="text-lg font-semibold text-gray-700 mb-3 pt-4 border-t">Add New Buyer</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 border rounded-md bg-gray-50">
                {/* Buyer Name */}
                <div>
                  <label htmlFor="buyerName" className="block text-sm font-medium text-gray-700 mb-1">Buyer Name <span className="text-red-500">*</span></label>
                  <input
                    type="text"
                    id="buyerName"
                    name="buyerName"
                    value={newBuyer.buyerName}
                    onChange={handleNewBuyerInputChange}
                    className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm"
                    placeholder="Enter buyer's company name"
                    required
                  />
                </div>
                {/* Contact Email */}
                <div>
                  <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700 mb-1">Contact Email <span className="text-red-500">*</span></label>
                  <input
                    type="email"
                    id="contactEmail"
                    name="contactEmail"
                    value={newBuyer.contactEmail}
                    onChange={handleNewBuyerInputChange}
                    className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm"
                    placeholder="Enter contact email address"
                    required
                  />
                </div>
                {/* Contact Person */}
                <div>
                  <label htmlFor="contactPerson" className="block text-sm font-medium text-gray-700 mb-1">Contact Person</label>
                  <input
                    type="text"
                    id="contactPerson"
                    name="contactPerson"
                    value={newBuyer.contactPerson}
                    onChange={handleNewBuyerInputChange}
                    className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm"
                    placeholder="Name of contact (Optional)"
                  />
                </div>
                {/* Registration Number */}
                <div>
                  <label htmlFor="registrationNumber" className="block text-sm font-medium text-gray-700 mb-1">CR/Reg. Number</label>
                  <input
                    type="text"
                    id="registrationNumber"
                    name="registrationNumber"
                    value={newBuyer.registrationNumber}
                    onChange={handleNewBuyerInputChange}
                    className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm"
                    placeholder="Buyer's CR Number (Optional)"
                  />
                </div>
                {/* Contact Phone */}
                <div className="sm:col-span-2"> {/* Span across both columns */}
                  <label htmlFor="contactPhone" className="block text-sm font-medium text-gray-700 mb-1">Contact Phone</label>
                  <input
                    type="tel"
                    id="contactPhone"
                    name="contactPhone"
                    value={newBuyer.contactPhone}
                    onChange={handleNewBuyerInputChange}
                    className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm"
                    placeholder="Contact phone number (Optional)"
                  />
                </div>

                {/* Add Button for the Form */}
                <div className="sm:col-span-2 flex justify-end mt-2">
                  <button
                    type="button" // Important: Prevent form submission if inside a <form> tag (not used here)
                    onClick={handleAddNewBuyerToList}
                    className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors"
                    disabled={isSavingBuyers} // Disable if overall save is in progress
                  >
                    Add Buyer to List
                  </button>
                </div>

              </div>
              {/* Display Add Buyer Form Error */}
              {buyersModalError && (
                <p className="text-sm text-red-600 mt-3 px-4">{buyersModalError}</p>
              )}
            </div>

          </div> {/* End Modal Body */}

          {/* Modal Footer */}
          <div className="flex-shrink-0 mt-auto pt-4 border-t flex justify-between items-center">
            <button
              onClick={handleCloseBuyersModal}
              className="px-5 py-2 text-sm bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
              disabled={isSavingBuyers}
            >
              Close
            </button>
            <button
              onClick={handleSaveBuyers}
              className="px-5 py-2 text-sm bg-[#004141] text-white rounded-md hover:bg-opacity-90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center min-w-[120px]"
              disabled={isSavingBuyers}
            >
              {isSavingBuyers ? (
                <>
                  <ArrowPathIcon className="w-4 h-4 mr-2 animate-spin" /> Saving...
                </>
              ) : (
                'Save Changes'
              )}
            </button>
          </div>

        </div> {/* End Modal Content Box */}
      </div> // End Modal Backdrop
    );
  };
  // --- END: Buyers Modal Renderer ---

  // --- RENDER ---
  const handleOpenContractModalCL = () => {
    if (acceptedLenderDetails?.contractUrl) {
      setContractPdfUrlCL(acceptedLenderDetails.contractUrl);
      setShowContractModalCL(true);
    } else {
      toast.warn("No credit line contract document available.");
    }
  };
  const utilizationPercent = creditLine?.creditLimit > 0 ? ((creditLine.utilizedAmount / creditLine.creditLimit) * 100) : 0;
  const availablePercent = 100 - utilizationPercent;

  const formatCurrency = (amount, currency = 'QAR') => {
    const num = Number(amount);
    if (isNaN(num)) {
      if (typeof amount === 'string' && amount.includes(currency)) return amount;
      return `${currency} 0.00`;
    }
    return `${currency} ${num.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  if (isLoading) {
    return <LoadingModal />
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header - Full Width */}
      <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 pt-4 flex flex-col sm:flex-row justify-between items-center gap-3">
        <h1 className="text-xl font-bold text-gray-800 mb-3 sm:mb-0">Welcome, {userFirstName}</h1>
        <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
          <button
            className="bg-[#209c52] text-white px-4 py-2 rounded-md shadow hover:bg-opacity-90 transition-colors flex items-center justify-center text-sm font-medium w-full sm:w-auto"
            onClick={() => {
              // Reset relevant states for a fresh upload attempt
              setUploadError(''); // Clear any previous upload errors
              // setShowUploadModal(true); // OLD WAY
              setShowNewInitialUploadModal(true); // NEW WAY
            }}
          >
            <PlusIcon className="w-5 h-5 mr-2" />
            Add Invoice
          </button>
          <button className="bg-white text-gray-700 px-4 py-2 rounded-md shadow hover:bg-gray-100 transition-colors flex items-center justify-center text-sm font-medium w-full sm:w-auto">
            Export
            <ChevronDownIcon className="w-5 h-5 ml-2" />
          </button>
        </div>
      </div>

      {/* Main Content Area - Full Width Grid */}
      <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">

          {/* Left Column */}
          <div className="lg:col-1 space-y-6">
            {!creditLine ? (
              <div className="bg-white p-6 rounded-lg shadow-lg"><Loader /></div>
            ) : creditLine ? (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex justify-between items-center mb-4 md:cursor-default cursor-pointer" onClick={(e) => window.innerWidth < 768 && setIsCreditLineOverviewOpen(!isCreditLineOverviewOpen)}>
                  <h2 className="text-lg font-semibold text-gray-800">Credit Line Overview</h2>
                  <div className="flex items-center">
                    <span className={`px-3 py-1 rounded-md text-xs font-medium ${getCreditLineStatusColor(creditLine.creditLineStatus)}`}>{creditLine.creditLineStatus.replace(/_/g, ' ')}</span>
                    <ChevronDownIcon className={`ml-2 h-5 w-5 text-gray-500 transform transition-transform duration-300 md:hidden ${isCreditLineOverviewOpen ? 'rotate-180' : 'rotate-0'}`} />
                  </div>
                </div>
                <div className="border border-gray-300 bg-gray-50 rounded-lg p-4 mb-4">
                  <div className="grid grid-cols-3 gap-2 text-center sm:text-left">
                    {[
                      { label: "Interest Rate", value: `${creditLine.interestRate ?? 'N/A'}%` },
                      { label: "Tenure", value: `${creditLine.tenure ?? 'N/A'} Days` },
                      { label: "Processing Fee", value: creditLine.processingFeeType === 'percentage' ? `${creditLine.processingFee}%` : formatCurrency(creditLine.processingFee || 0) }
                    ].map(item => (
                      <div key={item.label}>
                        <p className="text-xs text-gray-500 mb-1">{item.label}</p>
                        <p className="font-semibold text-gray-800 text-sm">{item.value}</p>
                      </div>
                    ))}
                  </div>
                </div>
                <div className={`overflow-hidden transition-all duration-500 ease-in-out md:max-h-none md:opacity-100 md:pt-4 ${isCreditLineOverviewOpen ? 'max-h-[500px] opacity-100 pt-4' : 'max-h-0 opacity-0'}`}>
                  <div className="mb-4">
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-xs text-gray-600">Credit Available</span>
                      <span className="text-xs font-medium">{`${availablePercent.toFixed(0)}% Available`}</span>
                    </div>
                    <div className="h-3 bg-red-100 rounded-full overflow-hidden w-full"><div style={{ width: `${availablePercent}%` }} className="h-full bg-green-500 rounded-full transition-all duration-500"></div></div>
                    <p className="text-[10px] text-gray-500 mt-1 text-right">{formatCurrency(creditLine?.availableBalance ?? (creditLine?.creditLimit || 0) * (availablePercent / 100))} Available</p>
                  </div>
                  <hr className="border-t border-gray-200 my-4" />
                  {acceptedLenderDetails && (
                    <>
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center">
                          {acceptedLenderDetails.logoUrl ? <img src={acceptedLenderDetails.logoUrl} alt={`${acceptedLenderDetails.lenderName} logo`} className="h-10 w-10 object-contain rounded-full mr-3 border" /> : <div className="h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center text-gray-500 text-xs mr-3 p-1 text-center">No Logo</div>}
                          <div>
                            <p className="font-semibold text-xs text-gray-900">{acceptedLenderDetails.lenderName}</p>
                            <p className="text-xs text-gray-600">Bank</p>
                          </div>
                        </div>
                        {acceptedLenderDetails.contractUrl && <button onClick={handleOpenContractModalCL} className="px-2 py-1 bg-[#00393b] text-white text-xs font-medium rounded-md hover:bg-[#002a2c] transition-colors">View Contract</button>}
                      </div>
                      <div className="bg-gray-50 border border-gray-300 rounded-lg p-3">
                        <p className="text-xs text-gray-500 mb-1">Contact Person</p>
                        <p className="font-semibold text-gray-800 text-sm">{acceptedLenderDetails.contactPersonName}</p>
                        <p className="text-xs text-gray-700 break-all">{acceptedLenderDetails.contactPersonEmail}</p>
                      </div>
                    </>
                  )}
                </div>
              </div>
            ) : ( // Credit line exists but not active/approved for details view
              <div className="bg-white rounded-lg shadow-lg p-6 text-center text-gray-600">
                <h2 className="text-md font-semibold text-gray-800 mb-2">Credit Line Status</h2>
                <span className={`inline-block px-3 py-1 my-2 rounded-md text-sm font-medium ${getCreditLineStatusColor(creditLine?.creditLineStatus)}`}>{creditLine?.creditLineStatus?.replace(/_/g, ' ') || 'Not Available'}</span>
                {creditLine?.creditLineStatus === 'NOT_FOUND' && <p className="text-sm mt-1">No credit line application found.</p>}
                {creditLine?.creditLineStatus === 'UNDER_REVIEW' && <p className="text-sm mt-1">Your credit line application is under review.</p>}
                {creditLineError && creditLine?.creditLineStatus !== 'NOT_FOUND' && <p className="text-sm text-red-500 mt-2">{creditLineError}</p>}
              </div>
            )
            }
            <div className="bg-[#27c686] text-white rounded-lg shadow p-4 md:p-6">
              <div className="flex items-center justify-center md:justify-start mb-3 md:mb-4 md:pl-4">
                <img src={briefcaseMoney} alt="Briefcase with money" className="h-24 md:h-32 w-auto" />
              </div>
              <h2 className="text-lg md:text-xl font-bold mb-2 text-center md:text-left">Instant cash from your invoices</h2>
              <p className="text-sm text-center md:text-left">Turn your unpaid invoices into immediate working capital. Access funds in a few clicks—no waiting, no hassle.</p>
            </div>
          </div>
          {/* Right Column - My Invoices Table */}
          <div className="lg:col-span-3 space-y-6">
            <div className="bg-white rounded-lg shadow-lg">
              <div className="p-4 sm:p-6 border-b border-gray-200 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <h2 className="text-xl font-semibold text-gray-800">My Invoices</h2>
                <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                  {/* Status Filter */}
                  <div className="w-full sm:w-auto">
                    <label htmlFor="statusFilter" className="sr-only">Status</label>
                    <select
                      id="statusFilter"
                      name="statusFilter"
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-[#004141] focus:border-transparent text-sm"
                      aria-label="Filter by status"
                    >
                      <option value="">Status: All</option>
                      {Object.entries(STATUS_DISPLAY_NAMES)
                        .sort(([, a], [, b]) => a.localeCompare(b)) // Sort alphabetically
                        .map(([key, displayName]) => (
                          <option key={key} value={key}>{displayName}</option>
                        ))}
                    </select>
                  </div>

                  {/* Age Filter */}
                  <div className="w-full sm:w-auto">
                    <label htmlFor="ageFilter" className="sr-only">Invoice Age</label>
                    <select
                      id="ageFilter"
                      name="ageFilter"
                      value={ageFilter}
                      onChange={(e) => setAgeFilter(e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-[#004141] focus:border-transparent text-sm"
                      aria-label="Filter by invoice age (uploaded date)"
                    >
                      <option value="">Age: Any</option>
                      <option value="1">Uploaded Today</option>
                      <option value="7">Uploaded Last 7 Days</option>
                      <option value="30">Uploaded Last 30 Days</option>
                      <option value="90">Uploaded Last 90 Days</option>
                      <option value="over90">Uploaded Over 90 Days Ago</option>
                    </select>
                  </div>
                </div>
              </div>
              <div className="p-2 sm:p-4 bg-gray-200"> {/* Consistent background for the table container */}
                <div className="overflow-x-auto bg-white rounded-md shadow-sm"> {/* Table itself remains white */}
                  <table className="w-full min-w-[900px]"> {/* Adjust min-width as needed */}
                    <thead className="bg-gray-50"> {/* Consistent header background */}
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice ID</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice Date</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Age</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice Amount</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {isLoading ? (
                        <tr>
                          <td colSpan={creditLine && isCreditLineEligible(creditLine) ? 9 : 8} className="p-6 text-center text-gray-500">
                            <Loader />
                            <span className="mt-2 block">Loading Invoices...</span>
                          </td>
                        </tr>
                      ) : filteredInvoices.length === 0 ? (
                        <tr>
                          <td colSpan={creditLine && isCreditLineEligible(creditLine) ? 9 : 8} className="px-6 py-10 text-center text-sm text-gray-500">
                            {(statusFilter || ageFilter)
                              ? 'No invoices match the current filters.'
                              : (invoices.length === 0 ? 'No invoices found. Click "Add Invoice" to start.' : 'No invoices to display.')}
                          </td>
                        </tr>
                      ) : (
                        filteredInvoices.map((invoice) => (
                          invoice && invoice._doc && (
                            <tr key={invoice._doc._id} className="hover:bg-gray-50 transition-colors">
                              <td className="px-4 py-4 align-top whitespace-nowrap">
                                <button onClick={() => handleViewInvoice(invoice)} className="text-[#004141] hover:text-blue-700 hover:underline text-sm font-medium" title={`View details for ${invoice._doc.invoiceNumber}`}>{invoice._doc.invoiceNumber || 'N/A'}</button>
                              </td>
                              <td className="px-4 py-4 align-top text-sm text-gray-700 whitespace-nowrap">{invoice._doc.invoiceDate || 'N/A'}</td>
                              <td className="px-4 py-4 align-top text-sm text-gray-600 whitespace-nowrap">{calculateInvoiceAge(invoice._doc.uploadedAt)}</td>
                              <td className="px-4 py-4 align-top text-sm text-gray-700 max-w-[150px] truncate" title={invoice._doc.customerName || ''}>{invoice._doc.customerName || 'N/A'}</td>
                              <td className="px-4 py-4 align-top text-sm text-gray-800 font-medium whitespace-nowrap text-right">{formatAmount(invoice._doc.totalAmount)}</td>
                              <td className="px-4 py-4 align-top whitespace-nowrap"><span className={`inline-block px-2.5 py-1 rounded-full text-xs font-medium ${getStatusStyle(invoice._doc.status)}`}>{getStatusDisplay(invoice._doc.status)}</span></td>
                              <td className="px-4 py-4 align-top text-center whitespace-nowrap">{renderActionButton(invoice)}</td>
                            </tr>
                          )
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modals (Keep existing modal rendering logic) */}
      {showUploadModal && renderUploadModal()}

      {/* Ensure viewingInvoice has _doc before rendering preview modal */}
      {showPdfPreview && viewingInvoice && viewingInvoice._doc && (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex justify-center items-center z-50 p-4">
          {/* Increased max-w-7xl for more space */}
          <div className="bg-white p-6 rounded-lg shadow-xl w-full max-w-7xl h-[95vh] flex flex-col">
            <div className="flex justify-between items-center mb-4 border-b pb-3">
              <h3 className="text-xl font-semibold text-gray-800">
                Invoice Preview: {viewingInvoice._doc.invoiceNumber}
              </h3>
              <button
                onClick={() => {
                  setShowPdfPreview(false);
                  setViewingInvoice(null); // Clear viewing invoice on close
                  setPdfUrl(''); // Clear PDF URL
                  setEditingInvoice(null); // Ensure edit mode is off
                }}
                className="text-gray-500 hover:text-gray-800 p-1 rounded-full hover:bg-gray-200 transition-colors"
                aria-label="Close preview"
              >
                <XMarkIcon className="w-6 h-6" />
              </button>
            </div>

            {/* Modal Body: PDF + Details/Actions */}
            <div className="flex flex-col md:flex-row flex-grow h-[calc(100%-4rem)] gap-5 overflow-hidden">

              {/* Left Side: PDF Viewer */}
              <div className="w-full md:w-3/5 h-1/2 md:h-full border border-gray-300 rounded-md overflow-hidden flex flex-col bg-gray-100">
                <div className="flex-shrink-0 p-2 bg-gray-200 border-b border-gray-300">
                  <p className="text-sm font-medium text-gray-700">Original Invoice Document</p>
                </div>
                {pdfUrl ? (
                  <iframe
                    src={pdfUrl}
                    className="w-full flex-grow border-0"
                    title="PDF Preview"
                  />
                ) : (
                  <div className="flex items-center justify-center flex-grow text-gray-500 p-4 text-center">
                    Invoice document preview not available or failed to load.
                  </div>
                )}
              </div>

              {/* Right Side: Details & Additional Docs (Scrollable) */}
              <div className="w-full md:w-2/5 h-1/2 md:h-full overflow-y-auto pr-2 space-y-5 scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100">

                {/* Invoice Details Section */}
                <div className="bg-white border border-gray-200 p-4 rounded-md shadow-sm">
                  <h4 className="text-lg font-semibold text-gray-700 mb-4 border-b pb-2">Invoice Details</h4>
                  {/* Pass the full viewingInvoice object which contains _doc */}
                  {renderInvoiceDetails(viewingInvoice)} {/* Pass the full object to renderInvoiceDetails */}
                </div>

                {/* Additional Documents Section */}
                <div className="bg-white border border-gray-200 p-4 rounded-md shadow-sm">
                  <h4 className="text-lg font-semibold text-gray-700 mb-3">
                    Additional Documents ({viewingInvoice._doc.additionalInvoiceDocuments?.length || 0}/10)
                  </h4>
                  {/* List */}
                  <div className="space-y-2 mb-4 max-h-60 overflow-y-auto pr-1 border-t border-b py-2 scrollbar-thin">
                    {viewingInvoice._doc.additionalInvoiceDocuments && viewingInvoice._doc.additionalInvoiceDocuments.length > 0 ? (
                      viewingInvoice._doc.additionalInvoiceDocuments.map((doc, index) => (
                        doc && ( // Ensure doc object is valid
                          <div key={doc._id || index} className="flex items-center justify-between bg-gray-50 p-2 rounded border border-gray-200 text-sm">
                            <div className="flex items-center space-x-2 overflow-hidden mr-2">
                              <LinkIcon className="h-4 w-4 text-blue-500 flex-shrink-0" />
                              <span className="text-gray-700 truncate" title={getFilenameFromPath(doc.filePath)}>
                                {shortenDocumentName(getFilenameFromPath(doc.filePath))}
                              </span>
                            </div>
                            {doc.signedUrl ? (
                              <a
                                href={doc.signedUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-xs text-blue-600 hover:underline font-medium flex-shrink-0 px-2 py-1 rounded hover:bg-blue-100"
                                title={`View Document (Uploaded: ${doc.uploadedOn ? new Date(doc.uploadedOn).toLocaleDateString() : 'N/A'})`}
                              >
                                View
                              </a>
                            ) : (
                              <span className="text-xs text-gray-400 italic">No link</span>
                            )}
                          </div>
                        )
                      ))
                    ) : (
                      <p className="text-sm text-gray-500 italic py-2">No additional documents uploaded.</p>
                    )}
                  </div>
                  {/* Upload Interface */}
                  {additionalDocError && (
                    <p className="text-sm text-red-600 mb-2">{additionalDocError}</p>
                  )}
                  <input
                    type="file"
                    ref={additionalFileInputRef}
                    className="hidden"
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={handleAdditionalDocumentUpload}
                  />
                  <button
                    onClick={() => additionalFileInputRef.current?.click()}
                    className={`w-full flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors ${(viewingInvoice._doc.additionalInvoiceDocuments?.length >= 10 || isUploadingAdditionalDoc)
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-[#004141] text-white hover:bg-opacity-90'
                      }`}
                    disabled={viewingInvoice._doc.additionalInvoiceDocuments?.length >= 10 || isUploadingAdditionalDoc}
                    title={(viewingInvoice._doc.additionalInvoiceDocuments?.length >= 10) ? "Maximum 10 documents allowed" : "Upload PDF, JPG, PNG"}
                  >
                    {isUploadingAdditionalDoc ? (
                      <><ArrowPathIcon className="w-4 h-4 mr-2 animate-spin" /> Uploading...</>
                    ) : (
                      <><DocumentArrowUpIcon className="w-5 h-5 mr-2" /> Add Document</>
                    )}
                  </button>
                  {(viewingInvoice._doc.additionalInvoiceDocuments?.length >= 10) && (
                    <p className="text-xs text-center text-gray-500 mt-1">Limit reached</p>
                  )}
                </div>
              </div> {/* End Right Side */}
            </div> {/* End Modal Body */}
          </div> {/* End Modal Content Box */}
        </div>
      )}
      {showContractModal && renderContractModal()}
      {showReUploadModal && renderReUploadModal()}
      {showBuyersModal && renderBuyersModal()} {/* Render the buyers modal if needed */}
      {showNewInitialUploadModal && renderNewInitialUploadModal()}
      {showGeneralLoadingModal && <GeneralLoadingModal title={generalLoadingTitle} message={generalLoadingMessage} />}

    </div>
  );

};

export default MyInvoicesPage;