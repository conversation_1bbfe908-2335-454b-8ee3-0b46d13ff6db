import React, { useState } from 'react';

const LoanOffersPage = () => {
  const [selectedInvoice, setSelectedInvoice] = useState(false);

  // Invoice data (can be fetched dynamically)
  const invoice = {
    invoiceNumber: '7295',
    date: '2024-12-16',
    dueDate: '2025-01-15',
    amount: 'QAR 87,000.00',
    supplier: 'GLOBAL OILS FACTORY',
    customer: 'BIG TRADERS',
    description: 'Raw Material Supply',
    reference: 'PO-2024-001',
    pdfUrl: '/assets/invoice.pdf'
  };

  const handleViewInvoice = () => {
    setSelectedInvoice(true);
  };

  return (
    <div className="p-6">
      {/* <h1 className="text-2xl font-bold mb-6">Invoice Offers</h1> */}

      {/* Invoice Section */}
      {/* <div className="bg-white p-6 rounded-md shadow-lg mb-8">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h2 className="text-xl font-bold mb-2">Invoice Details</h2>
            <div className="grid grid-cols-2 gap-x-8 gap-y-2">
              <div>
                <span className="text-gray-600">Invoice Number:</span>
                <span className="ml-2 font-medium">{invoice.invoiceNumber}</span>
              </div>
              <div>
                <span className="text-gray-600">Date:</span>
                <span className="ml-2 font-medium">{invoice.date}</span>
              </div>
              <div>
                <span className="text-gray-600">Due Date:</span>
                <span className="ml-2 font-medium">{invoice.dueDate}</span>
              </div>
              <div>
                <span className="text-gray-600">Amount:</span>
                <span className="ml-2 font-medium">{invoice.amount}</span>
              </div>
              <div>
                <span className="text-gray-600">Supplier:</span>
                <span className="ml-2 font-medium">{invoice.supplier}</span>
              </div>
              <div>
                <span className="text-gray-600">Customer:</span>
                <span className="ml-2 font-medium">{invoice.customer}</span>
              </div>
            </div>
          </div>
          <button
<<<<<<< HEAD
            className="bg-[#004141] text-white px-4 py-2 rounded-md  "
=======
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-[#003030]blue-600"
>>>>>>> 7e0b937a8250bdd5d3e23f5eccbccf9a52f5b6e6
            onClick={handleViewInvoice}
          >
            View Invoice
          </button>
        </div>
      </div> */}

      {/* Offers Section */}
      <div className="mb-6">
        <h2 className="text-xl font-bold mb-4">Available Offers</h2>
        <div className="bg-white shadow-lg rounded-md p-6">
          <div className="text-center py-8">
            <p className="text-gray-600">No offers yet!</p>
          </div>
        </div>
      </div>

      {/* Invoice Preview Modal */}
      {selectedInvoice && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <div className="bg-white p-6 rounded-md shadow-lg w-full max-w-4xl h-[80vh]">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold">Invoice Preview</h3>
              <button
                onClick={() => setSelectedInvoice(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <iframe
              src={invoice.pdfUrl}
              className="w-full h-[calc(100%-3rem)]"
              title="Invoice PDF"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default LoanOffersPage;