import React, { useState } from 'react';

const TermLoanContract = () => {
    const [data, setData] = useState({
        partnerId: '',
        merchantCode: '',
        month: '',
        year: '',
        option: 'pending',
        trueScoreGT: '',
        trueScoreLT: ''
    });

    const [errors, setErrors] = useState({});
    const [list, setList] = useState([
        {
            id: '1',
            partner: 'ABC Finance',
            merchantCode: 'MRC001',
            merchantType: 'Online',
            mobileNo: '9876543210',
            emailId: '<EMAIL>',
            onboardedDate: new Date(),
            pincode: '560001',
            trueScore: 750,
            trxnId: 'TRX001',
            loanType: '',
            productType: '',
            processingFee: '',
            annualIntrestRate: '',
            tenureInDays: '',
            approvedAmount: '',
            emiAmt: '5000',
            interestType: '',
            sentOn: null
        }
    ]);

    const months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
    const years = Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - 4 + i);

    const borrowingPartnerList = [
        { _id: '1', displayName: 'Partner 1' },
        { _id: '2', displayName: 'Partner 2' }
    ];

    const handleChange = (e) => {
        const { name, value } = e.target;
        setData(prev => ({ ...prev, [name]: value }));
    };

    const handleRecordChange = (e, itemId, trxnId) => {
        const { name, value } = e.target;
        setList(prev => prev.map(item =>
            item.id === itemId && item.trxnId === trxnId
                ? { ...item, [name.split('_')[0]]: value }
                : item
        ));
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        // Placeholder for submit logic
        console.log('Form submitted', data);
    };

    const handleApprove = (approvalId) => {
        // Placeholder for approval logic
        console.log('Approve clicked', approvalId);
    };

    return (
        <div className="p-4 bg-white">
            <div className="bg-[#208039] text-white py-4 mb-4 text-center">
                <h2 className="text-2xl font-bold">Term Loan Contract</h2>
            </div>

            <form onSubmit={handleSubmit} className="mb-4">
                <div className="grid grid-cols-3 gap-4">
                    {/* Partner Dropdown */}
                    <div>
                        <label className="block mb-2">Partner</label>
                        <select
                            name="partnerId"
                            value={data.partnerId}
                            onChange={handleChange}
                            className="w-full p-2 border rounded-md"
                        >
                            <option value="">---ALL---</option>
                            {borrowingPartnerList.map((item) => (
                                <option key={item._id} value={item._id}>
                                    {item.displayName}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Merchant Code */}
                    <div>
                        <label className="block mb-2">Merchant Code</label>
                        <input
                            type="text"
                            name="merchantCode"
                            value={data.merchantCode}
                            onChange={handleChange}
                            maxLength="100"
                            className="w-full p-2 border rounded-md"
                        />
                    </div>

                    {/* Month Dropdown */}
                    <div>
                        <label className="block mb-2">Month</label>
                        <select
                            name="month"
                            value={data.month}
                            onChange={handleChange}
                            className="w-full p-2 border rounded-md"
                        >
                            <option value="">---Select---</option>
                            {months.map((value) => (
                                <option key={value} value={value}>{value}</option>
                            ))}
                        </select>
                    </div>

                    {/* Year Dropdown */}
                    <div>
                        <label className="block mb-2">Year</label>
                        <select
                            name="year"
                            value={data.year}
                            onChange={handleChange}
                            className="w-full p-2 border rounded-md"
                        >
                            <option value="">---Select---</option>
                            {years.map((value) => (
                                <option key={value} value={value}>{value}</option>
                            ))}
                        </select>
                    </div>

                    {/* Other form fields... */}
                </div>

                <button
                    type="submit"
                    className="mt-4 bg-[#208039] text-white px-4 py-2 rounded-md hover:bg-[#004141]"
                >
                    Apply Filters
                </button>
            </form>

            {/* Data Table */}
            <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                    <thead>
                        <tr className="bg-[#208039] text-white">
                            <th className="p-2 border">Partner</th>
                            <th className="p-2 border">Merchant Code</th>
                            <th className="p-2 border">Merchant Type</th>
                            <th className="p-2 border">Mobile</th>
                            <th className="p-2 border">Email</th>
                            <th className="p-2 border">Loan Details</th>
                            <th className="p-2 border">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {list.map((item, index) => (
                            <tr key={item.id} className={index % 2 === 0 ? 'bg-gray-100' : ''}>
                                <td className="p-2 border text-center">{item.partner}</td>
                                <td className="p-2 border text-center">{item.merchantCode}</td>
                                <td className="p-2 border text-center">{item.merchantType}</td>
                                <td className="p-2 border text-center">{item.mobileNo}</td>
                                <td className="p-2 border text-center">{item.emailId}</td>
                                <td className="p-2 border">
                                    <div className="space-y-2">
                                        <input
                                            type="text"
                                            placeholder="Loan Amount"
                                            value={item.approvedAmount}
                                            onChange={(e) => handleRecordChange(e, item.id, item.trxnId)}
                                            name={`approvedAmount_${item.id}_${item.trxnId}`}
                                            className="w-full p-1 border rounded-md"
                                        />
                                        <select
                                            value={item.interestType}
                                            onChange={(e) => handleRecordChange(e, item.id, item.trxnId)}
                                            name={`interestType_${item.id}_${item.trxnId}`}
                                            className="w-full p-1 border rounded-md"
                                        >
                                            <option value="">Select Interest Type</option>
                                            <option value="Simple Interest">Simple Interest</option>
                                            <option value="Compound Interest">Compound Interest</option>
                                        </select>
                                    </div>
                                </td>
                                <td className="p-2 border text-center">
                                    <button
                                        onClick={() => handleApprove(`approved_${item.id}_${item.trxnId}`)}
                                        className="bg-[#208039] text-white px-3 py-1 rounded-md hover:bg-[#004141]"
                                    >
                                        Approve
                                    </button>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default TermLoanContract;