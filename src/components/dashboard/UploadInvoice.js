import React, { useState, useEffect, useRef, useContext, useCallback } from 'react';
import { useLocation, useHistory } from 'react-router-dom';
import axios from 'axios';
import { toast } from 'react-toastify';
import config from '../../config.json'; // Adjust path as needed
import SharedCache from '../../sharedCache'; // Adjust path as needed
import { uploadAdditionalInvoiceDocument } from '../../api/kyc'; // Adjust path as needed
import { sendBuyerInvitationEmail } from '../../api/registration'; // Adjust path as needed
import { getKycInfo } from '../../api/kyc'; // Adjust path
import { LayoutContext } from '../../MainLayout'; // Adjust path for LayoutContext
import { findBestBuyerMatch, getPotentialMatches, debugNameProcessing } from '../../utils/BuyerMatchUtility';
// Icons
import {
    EyeIcon, ArrowPathIcon, DocumentArrowUpIcon as UploadIconHero, XMarkIcon,
    CheckIcon, ExclamationTriangleIcon, ArrowUturnLeftIcon, ClockIcon,
    CheckCircleIcon, XCircleIcon, InformationCircleIcon, DocumentPlusIcon,
    ArrowDownIcon,
    PencilSquareIcon,
    PlusIcon
} from '@heroicons/react/24/outline';
import LoadingModal from '../Reusable/Loading';
// ADD The Modal Component from the Support Page (assuming it's reusable)
// You might need to export it from your support page file or move it to a shared components folder.
// For this example, let's assume you have a Modal component you can import.
import { Modal } from './SupportTickets'; // Adjust the path as necessary

// --- BEGIN TOP-LEVEL COMPONENT DEFINITIONS AND CONSTANTS ---

const GeneralLoadingModal = ({ title, message }) => {
    return <LoadingModal />
};

// Correct top-level definition for AddressInput
const AddressInput = React.memo(({ value, onChange, placeholder, className = "" }) => (
    <input
        type="text"
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white focus:ring-indigo-500 focus:border-indigo-500 ${className}`}
    />
));

// Moved LockedInput to top-level
const LockedInput = ({ value, placeholder = "N/A" }) => (
    <input
        type="text"
        value={value || ''}
        placeholder={placeholder}
        className="w-full max-w-md px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-100 text-gray-700 cursor-not-allowed focus:outline-none focus:ring-0"
        disabled
    />
);

// Moved FormRow to top-level
const FormRow = ({ label, children }) => (
    <div className="flex flex-col sm:flex-row sm:items-center py-3 gap-2 sm:gap-4 border-b border-gray-100 last:border-b-0">
        <div className="w-full sm:w-1/4">
            <label className="block text-sm font-medium text-gray-700">{label}</label>
        </div>
        <div className="w-full sm:w-3/4 flex justify-start">
            {children}
        </div>
    </div>
);

const MAX_FILE_SIZE_MB = 20;
const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;
const ACCEPTED_FORMATS_ARRAY = ['.pdf', '.jpg', '.jpeg', '.png'];
const ACCEPTED_FORMATS_STRING = ACCEPTED_FORMATS_ARRAY.join(',');
const ACCEPTED_FORMATS_DISPLAY = 'PDF, JPG, PNG';

// Helper function
const shortenDocumentName = (name, maxLength = 20) => {
    if (!name) return '';
    if (name.length <= maxLength) return name;
    const extPart = name.substring(name.lastIndexOf('.'));
    const namePart = name.substring(0, name.lastIndexOf('.'));
    if (namePart.length <= maxLength - extPart.length - 3) return name;
    return `${namePart.substring(0, maxLength - extPart.length - 3)}...${extPart}`;
};

const LoadingSpinner = () => (
    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
);

const SmallUploadIcon = () => (
    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
    </svg>
);

const parseAndFormatDate = (dateString) => {
    if (!dateString) return '';
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
        return dateString;
    }
    let date;
    const partsSlash = dateString.split('/');
    if (partsSlash.length === 3) {
        const day = parseInt(partsSlash[0], 10);
        const month = parseInt(partsSlash[1], 10) - 1;
        let year = parseInt(partsSlash[2], 10);
        if (year < 100) { year += 2000; }
        date = new Date(Date.UTC(year, month, day));
    } else {
        date = new Date(dateString);
        if (dateString.includes('T')) {
            const localDate = new Date(dateString);
            if (localDate && !isNaN(localDate.getTime())) {
                const year = localDate.getUTCFullYear();
                const month = String(localDate.getUTCMonth() + 1).padStart(2, '0');
                const day = String(localDate.getUTCDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            }
        }
    }

    if (date && !isNaN(date.getTime())) {
        const year = date.getUTCFullYear();
        const month = String(date.getUTCMonth() + 1).padStart(2, '0');
        const day = String(date.getUTCDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
    console.warn("Could not parse date string to YYYY-MM-DD:", dateString);
    return '';
};

const FileUploadField = ({ label, docKey, fileData, onFileSelect, onViewClick, onReplaceClick, inputRef, disabled }) => {
    const handleFileChange = (event) => {
        const file = event.target.files?.[0];
        if (!file) return;
        if (file.size > MAX_FILE_SIZE_BYTES) {
            toast.error(`${label}: File size exceeds ${MAX_FILE_SIZE_MB}MB.`);
            if (inputRef.current) inputRef.current.value = '';
            onFileSelect(docKey, null, `File size exceeds ${MAX_FILE_SIZE_MB}MB.`);
            return;
        }
        const fileExtension = "." + file.name.split('.').pop().toLowerCase();
        if (!ACCEPTED_FORMATS_ARRAY.includes(fileExtension)) {
            toast.error(`${label}: Invalid file type. Accepted: ${ACCEPTED_FORMATS_DISPLAY}.`);
            if (inputRef.current) inputRef.current.value = '';
            onFileSelect(docKey, null, `Invalid file type. Accepted: ${ACCEPTED_FORMATS_DISPLAY}.`);
            return;
        }
        onFileSelect(docKey, file, null);
        if (inputRef.current) inputRef.current.value = '';
    };

    const renderUploadedStatus = () => {
        if (fileData?.status === 'selected' && fileData.fileName) {
            return (
                <span className="text-xs text-green-600 font-medium flex items-center">
                    <CheckIcon className="h-3 w-3 mr-1" /> Uploaded
                </span>
            );
        }
        return null;
    };

    return (
        <div className="border border-gray-200 rounded-lg p-3 bg-white shadow-sm flex flex-col justify-between w-full">
            <div className="flex justify-between items-center mb-2">
                <label className="text-xs font-medium text-gray-700">{label}</label>
                {renderUploadedStatus()}
            </div>

            {fileData?.status === 'selected' && fileData.fileName ? (
                <div>
                    <div className="bg-[#eff7f7] border-2 border-dashed border-gray-300 rounded-md p-2 text-center mb-2">
                        <span
                            className="text-xs font-medium text-gray-700 truncate break-words block"
                            title={fileData.fileName}
                        >
                            {shortenDocumentName(fileData.fileName, 20)}
                        </span>
                    </div>
                    {fileData.error && (
                        <p className="mt-1 text-xs text-orange-500 text-center break-words">
                            Note: {fileData.error}
                        </p>
                    )}
                </div>
            ) : (
                <div>
                    <button
                        type="button"
                        onClick={() => !disabled && inputRef.current?.click()}
                        className={`bg-[#eff7f7] w-full flex flex-col items-center justify-center px-2 py-3 border-2 border-dashed rounded-md text-xs font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#004141] group
                            ${disabled ? 'border-gray-300 bg-gray-100 text-gray-400 cursor-not-allowed'
                                : fileData?.error ? 'border-red-400 text-red-600 hover:bg-red-50'
                                    : 'border-gray-300 text-gray-500 hover:border-gray-400 hover:bg-gray-100'}`}
                        disabled={disabled}
                    >
                        <p className="text-[10px] text-gray-500 text-center mt-0.5">Max {MAX_FILE_SIZE_MB}MB</p>
                    </button>
                    <p className="mt-0.5 text-[10px] text-center text-gray-500">Formats: {ACCEPTED_FORMATS_DISPLAY}</p>
                    {fileData?.error && fileData.status === 'error' && (
                        <p className="mt-0.5 text-xs text-red-500 text-center break-words">{fileData.error}</p>
                    )}
                </div>
            )}

            {(fileData?.status === 'selected' || (fileData?.file && fileData?.fileName)) && (
                <div className="mt-auto pt-2 flex items-center justify-start gap-3">
                    {(fileData.file || fileData.fileName) && (
                        <button
                            type="button"
                            onClick={onViewClick}
                            className="flex items-center text-[11px] text-black hover:underline font-medium"
                        >
                            <EyeIcon className="h-3.5 w-3.5 mr-0.5" /> View
                        </button>
                    )}
                    {!disabled && (
                        <button
                            type="button"
                            onClick={onReplaceClick}
                            className="flex items-center text-[11px] text-black hover:underline font-medium"
                        >
                            <ArrowPathIcon className="h-3.5 w-3.5 mr-0.5" /> Replace
                        </button>
                    )}
                </div>
            )}
            <input
                ref={inputRef}
                type="file"
                className="hidden"
                onChange={handleFileChange}
                accept={ACCEPTED_FORMATS_STRING}
                disabled={disabled}
            />
        </div>
    );
};

// --- END TOP-LEVEL COMPONENT DEFINITIONS AND CONSTANTS ---

const UploadInvoicePage = () => {
    const location = useLocation();
    const history = useHistory();
    const { setCurrentPage } = useContext(LayoutContext);
    const { mindeeData: initialMindeeData, uploadedInvoiceFile: mainInvoiceFileFromPrevPage } = location.state || {};

    const user = SharedCache.get("user") || {};
    const userId = user._id || user.id || "";
    const token = SharedCache.get("token") || "placeholdertoken";

    const [invoiceDate, setInvoiceDate] = useState('');
    const [dueDate, setDueDate] = useState('');
    const [totalAmount, setTotalAmount] = useState('');
    const [currency, setCurrency] = useState('QAR');
    // customerNameForDisplay now refers to the Buyer's name (e.g., "BIG TRADERS")
    const [customerNameForDisplay, setCustomerNameForDisplay] = useState('');
    const [selectedKycBuyerId, setSelectedKycBuyerId] = useState('');

    // These address fields will be for the Buyer (Customer)
    const [addressZone, setAddressZone] = useState('');
    const [addressStreetNo, setAddressStreetNo] = useState('');
    const [addressBuildingNo, setAddressBuildingNo] = useState('');
    const [addressFloorNo, setAddressFloorNo] = useState('');
    const [addressUnitNo, setAddressUnitNo] = useState('');
    // businessEmail will be the Buyer's (Customer's) email for contact/invitation
    const [businessEmail, setBusinessEmail] = useState('');

    const [isAddBuyerModalOpen, setIsAddBuyerModalOpen] = useState(false);
    const [newBuyerData, setNewBuyerData] = useState({
        buyerName: '',
        registrationNumber: '',
        contactPerson: '',
        contactPhone: '',
        contactEmail: '',
    });
    const [isSavingNewBuyer, setIsSavingNewBuyer] = useState(false);
    const [buyerModalError, setBuyerModalError] = useState('');

    const [kycBuyersList, setKycBuyersList] = useState([]);
    const [isBuyerApproved, setIsBuyerApproved] = useState(true); // Defaulting to true, validation will set it
    const [buyerApprovalError, setBuyerApprovalError] = useState('');

    const initialSupportingDocStates = () => ([{
        key: `doc1`, file: null, fileName: '', status: 'empty', error: null,
    }]);

    const [supportingDocuments, setSupportingDocuments] = useState(initialSupportingDocStates());
    const [consentAccepted, setConsentAccepted] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [generalFormError, setGeneralFormError] = useState('');
    const docInputRefs = useRef([]); // Initialize as an empty array
    const [isRequestChangesModalOpen, setIsRequestChangesModalOpen] = useState(false);
    const [isTicketSubmitting, setIsTicketSubmitting] = useState(false);
    const [ticketSubject, setTicketSubject] = useState('');
    const [ticketDescription, setTicketDescription] = useState('');
    const [isChangeRequestDrafted, setIsChangeRequestDrafted] = useState(false); const handleAddressZoneChange = useCallback(e => setAddressZone(e.target.value), []);
    const handleAddressStreetNoChange = useCallback(e => setAddressStreetNo(e.target.value), []);
    const handleAddressBuildingNoChange = useCallback(e => setAddressBuildingNo(e.target.value), []);
    const handleAddressFloorNoChange = useCallback(e => setAddressFloorNo(e.target.value), []);
    const handleAddressUnitNoChange = useCallback(e => setAddressUnitNo(e.target.value), []);

    const fetchAndValidateBuyer = useCallback(async (isRecheck = false) => {
        if (!userId) return;
        if (isRecheck) {
            toast.info("Re-checking buyer details...");
        }
        try {
            const kycData = await getKycInfo(userId);
            const fetchedKycBuyers = kycData?.user?.kyc?.buyers || [];
            setKycBuyersList(fetchedKycBuyers);

            const extractedBuyerName = initialMindeeData.customerName?.trim();

            if (extractedBuyerName) {
                const matchedBuyer = findBestBuyerMatch(extractedBuyerName, fetchedKycBuyers, {
                    exactMatchThreshold: 0.95, goodMatchThreshold: 0.8, minMatchThreshold: 0.6
                });

                if (matchedBuyer) {
                    setSelectedKycBuyerId(matchedBuyer._id);
                    setCustomerNameForDisplay(matchedBuyer.buyerName);
                    setBusinessEmail(matchedBuyer.contactEmail || '');
                    setAddressZone(matchedBuyer.addressZone || initialMindeeData.customer_address_zone || '');
                    setAddressStreetNo(matchedBuyer.addressStreetNo || initialMindeeData.customer_address_street_number || '');
                    setAddressBuildingNo(matchedBuyer.addressBuildingNo || initialMindeeData.customer_address_building_number || '');
                    setAddressFloorNo(matchedBuyer.addressFloorNo || initialMindeeData.customer_address_floor || '');
                    setAddressUnitNo(matchedBuyer.addressUnitNo || initialMindeeData.customer_address_unit || '');
                    setIsBuyerApproved(true);
                    setBuyerApprovalError('');
                    if (isRecheck) {
                        toast.success("Success! Buyer matched and pre-selected.");
                    }
                } else {
                    setSelectedKycBuyerId('');
                    setIsBuyerApproved(false);
                    const errorMessage = `The extracted buyer: "${initialMindeeData.customerName}" does not match your approved buyers. Please add this buyer via 'My Buyers'.`;
                    setBuyerApprovalError(errorMessage);
                    if (isRecheck) {
                        toast.error("The new buyer was added but still doesn't match the invoice.");
                    }
                }
            } else {
                setIsBuyerApproved(false);
                setBuyerApprovalError("Buyer name could not be extracted from the invoice. Please select one manually.");
            }
        } catch (error) {
            console.error("Error fetching/validating KYC buyers:", error);
            toast.error("Could not load your buyers list. Please check manually.");
            setBuyerApprovalError("Could not verify buyer against your approved list.");
            setIsBuyerApproved(false);
        }
    }, [userId, initialMindeeData]);

    useEffect(() => {
        if (!initialMindeeData || !mainInvoiceFileFromPrevPage) {
            toast.error("Invoice data not found. Please re-upload.");
            history.push('/dashboard');
            setCurrentPage('my-invoices');
            return;
        }

        setInvoiceDate(parseAndFormatDate(initialMindeeData.invoiceDate));
        setDueDate(parseAndFormatDate(initialMindeeData.dueDate));
        setTotalAmount(initialMindeeData.totalAmount || '');
        setCurrency(initialMindeeData.currency || 'QAR');
        setCustomerNameForDisplay(initialMindeeData.customerName || '');
        setBusinessEmail(initialMindeeData.customerEmail || '');
        setAddressZone(initialMindeeData.customer_address_zone || initialMindeeData.addressZone || '');
        setAddressStreetNo(initialMindeeData.customer_address_street_number || initialMindeeData.addressStreetNo || '');
        setAddressBuildingNo(initialMindeeData.customer_address_building_number || initialMindeeData.addressBuildingNo || '');
        setAddressFloorNo(initialMindeeData.customer_address_floor || initialMindeeData.addressFloorNo || '');
        setAddressUnitNo(initialMindeeData.customer_address_unit || initialMindeeData.addressUnitNo || '');

        fetchAndValidateBuyer(false); // Call the new function here

    }, [initialMindeeData, mainInvoiceFileFromPrevPage, history, userId, setCurrentPage, fetchAndValidateBuyer]);

    const openAddBuyerModal = () => {
        setNewBuyerData({
            buyerName: initialMindeeData?.customerName || '', // Pre-fill name from invoice
            registrationNumber: '',
            contactPerson: '',
            contactPhone: '',
            contactEmail: '',
        });
        setBuyerModalError('');
        setIsAddBuyerModalOpen(true);
    };

    const closeAddBuyerModal = () => {
        setIsAddBuyerModalOpen(false);
    };

    const handleAddBuyerModalInputChange = (e) => {
        const { name, value } = e.target;
        setNewBuyerData(prev => ({ ...prev, [name]: value }));
    };

    const handleSaveNewBuyer = async (e) => {
        e.preventDefault();
        setBuyerModalError('');
        if (!newBuyerData.buyerName.trim() || !newBuyerData.contactEmail.trim()) {
            setBuyerModalError("Buyer Name and Contact Email are required.");
            return;
        }

        setIsSavingNewBuyer(true);
        const updatedBuyersListForAPI = [...kycBuyersList, newBuyerData];

        try {
            await axios.post(`${config.apiUrl}/ops/invoiceFinancing/updateKyc`,
                { userId, kyc: { buyers: updatedBuyersListForAPI } },
                { headers: { 'Content-Type': 'application/json', 'x-auth-token': token } }
            );

            toast.success("New buyer added!");
            closeAddBuyerModal();

            // This is the key step: re-run the validation logic
            await fetchAndValidateBuyer(true);

        } catch (error) {
            console.error("Error saving new buyer:", error);
            const errorMsg = error.response?.data?.message || "Could not save the new buyer.";
            setBuyerModalError(errorMsg);
            toast.error(errorMsg);
        } finally {
            setIsSavingNewBuyer(false);
        }
    };

    const handleCustomerSelectChange = (e) => {
        const buyerId = e.target.value;
        setSelectedKycBuyerId(buyerId);
        if (buyerId) {
            const selectedBuyer = kycBuyersList.find(c => c._id === buyerId);
            if (selectedBuyer) {
                setCustomerNameForDisplay(selectedBuyer.buyerName);
                setBusinessEmail(selectedBuyer.contactEmail || '');
                // Populate address from selected KYC buyer
                setAddressZone(selectedBuyer.addressZone || '');
                setAddressStreetNo(selectedBuyer.addressStreetNo || '');
                setAddressBuildingNo(selectedBuyer.addressBuildingNo || '');
                setAddressFloorNo(selectedBuyer.addressFloorNo || '');
                setAddressUnitNo(selectedBuyer.addressUnitNo || '');
                setIsBuyerApproved(true);
                setBuyerApprovalError('');
            }
        } else {
            // Corrected: Fallback to OCR'd customer name
            setCustomerNameForDisplay(initialMindeeData?.customerName || '');
            // Fallback to OCR'd customer address and email
            setBusinessEmail(initialMindeeData.customerEmail || initialMindeeData.supplierEmail || '');
            setAddressZone(initialMindeeData.customer_address_zone || initialMindeeData.addressZone || '');
            setAddressStreetNo(initialMindeeData.customer_address_street_number || initialMindeeData.addressStreetNo || '');
            setAddressBuildingNo(initialMindeeData.customer_address_building_number || initialMindeeData.addressBuildingNo || '');
            setAddressFloorNo(initialMindeeData.customer_address_floor || initialMindeeData.addressFloorNo || '');
            setAddressUnitNo(initialMindeeData.customer_address_unit || initialMindeeData.addressUnitNo || '');
            setIsBuyerApproved(false);
            setBuyerApprovalError("Please select an approved buyer or ensure the typed name matches an approved one.");
        }
    };

    const handleSupportingFileSelect = (docKey, file, errorMsg) => {
        setSupportingDocuments(prevDocs => {
            const updatedDocs = prevDocs.map(doc =>
                doc.key === docKey ? { ...doc, file, fileName: file ? file.name : '', status: file ? 'selected' : (errorMsg ? 'error' : 'empty'), error: errorMsg } : doc
            );

            const justFilledIndex = updatedDocs.findIndex(doc => doc.key === docKey);
            const isLastDocFilledSuccessfully = (justFilledIndex === updatedDocs.length - 1) && (updatedDocs[justFilledIndex]?.status === 'selected');

            // Only add a new slot if the very last card was just filled successfully
            if (isLastDocFilledSuccessfully) {
                const newDocKey = `doc${updatedDocs.length + 1}`;
                return [...updatedDocs, { key: newDocKey, file: null, fileName: '', status: 'empty', error: null }];
            }

            return updatedDocs;
        });
    };

    const handleViewMainInvoice = () => {
        if (mainInvoiceFileFromPrevPage) {
            try {
                const url = URL.createObjectURL(mainInvoiceFileFromPrevPage);
                window.open(url, '_blank', 'noopener,noreferrer');
                setTimeout(() => URL.revokeObjectURL(url), 100); // Clean up object URL
            } catch (e) {
                console.error("Error creating object URL for main invoice:", e);
                toast.error(`Could not open preview for the main invoice.`);
            }
        } else {
            toast.warn("Main invoice file not available for viewing.");
        }
    };

    const handleViewSupportingFile = (docKey) => {
        const doc = supportingDocuments.find(d => d.key === docKey);
        if (doc && doc.file) {
            try {
                const url = URL.createObjectURL(doc.file);
                window.open(url, '_blank', 'noopener,noreferrer');
                setTimeout(() => URL.revokeObjectURL(url), 100);
            } catch (e) {
                console.error("Error creating object URL:", e);
                toast.error(`Could not open preview for ${doc.fileName}`);
            }
        } else if (doc && doc.fileName && !doc.file) {
            toast.info(`Preview for existing server file "${doc.fileName}" would require an API call.`);
        }
    };

    const handleReplaceSupportingFile = (docKey) => {
        const docIndex = supportingDocuments.findIndex(d => d.key === docKey);
        if (docIndex !== -1 && docInputRefs.current[docIndex]?.current) {
            docInputRefs.current[docIndex].current.click();
        }
    };

    const triggerFirstEmptyUpload = () => {
        // Find the first empty slot in the current list of supporting documents
        const firstEmptySlot = supportingDocuments.find(doc => !doc.file && doc.status !== 'selected');

        if (firstEmptySlot) {
            // Get its index
            const indexToClick = supportingDocuments.indexOf(firstEmptySlot);
            if (indexToClick !== -1 && docInputRefs.current[indexToClick]) {
                docInputRefs.current[indexToClick].click();
            } else {
                console.warn("Could not find ref for first empty slot. This shouldn't happen if state and refs are in sync.");
                toast.error("An unexpected error occurred. Please try again.");
            }
        } else {
            // If there are no empty slots, it means all existing slots are filled.
            // A new card should only appear when the last one is filled via direct upload.
            // So, this button does nothing in this case.
            toast.info("All documents uploaded. To add more, please upload to the last available slot.");
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setGeneralFormError('');

        // Ensure buyer (customerNameForDisplay) is validated
        if (!isBuyerApproved && !selectedKycBuyerId) {
            const typedNameLower = customerNameForDisplay?.trim();
            const matchInKyc = findBestBuyerMatch(typedNameLower, kycBuyersList, {
                exactMatchThreshold: 0.9, // Slightly lower threshold for final validation
                goodMatchThreshold: 0.75,
                minMatchThreshold: 0.6
            });
            if (matchInKyc) {
                setSelectedKycBuyerId(matchInKyc._id); // Set the ID for submission
                // Update details from KYC match again before submission if needed
                setCustomerNameForDisplay(matchInKyc.buyerName);
                setBusinessEmail(matchInKyc.contactEmail || '');
                setAddressZone(matchInKyc.addressZone || '');
                setAddressStreetNo(matchInKyc.addressStreetNo || '');
                setAddressBuildingNo(matchInKyc.addressBuildingNo || '');
                setAddressFloorNo(matchInKyc.addressFloorNo || '');
                setAddressUnitNo(matchInKyc.addressUnitNo || '');
                setIsBuyerApproved(true);
                setBuyerApprovalError('');
            } else {
                toast.error(buyerApprovalError || "Buyer details are not approved or selected. Please resolve.");
                return;
            }
        } else if (!isBuyerApproved && selectedKycBuyerId) { // This case might be redundant if selection implies approval
            toast.error(buyerApprovalError || "Buyer details are not approved. Please resolve.");
            return;
        } else if (!selectedKycBuyerId && !customerNameForDisplay) {
            toast.error("Customer name is required. Please select or enter an approved buyer.");
            return;
        }


        if (!consentAccepted) {
            toast.error("Please accept the Invoice Financing Consent terms.");
            return;
        }
        if (!invoiceDate || !dueDate || !totalAmount || !customerNameForDisplay) {
            toast.error("Please fill in all required invoice details (Date, Due Date, Amount, Customer).");
            return;
        }

        setIsSubmitting(true);
        let newInvoiceId = null;
        try {
            const mainFormData = new FormData();
            mainFormData.append('pdfFile', mainInvoiceFileFromPrevPage);
            mainFormData.append('userId', userId);
            if (user.gstin) mainFormData.append('gstin', user.gstin);
            mainFormData.append('status', "APPOVAL_PENDING");
            mainFormData.append('extractionOnly', 'false');
            mainFormData.append('invoiceNumber', initialMindeeData.invoiceNumber);
            mainFormData.append('invoiceDate', invoiceDate);
            mainFormData.append('dueDate', dueDate);
            mainFormData.append('totalAmount', totalAmount);
            mainFormData.append('currency', currency);

            // Corrected field mapping for submission
            mainFormData.append('supplierName', initialMindeeData.supplierName); // This is the MSME's name (Seller - e.g., GLOBAL)
            mainFormData.append('customerName', customerNameForDisplay);       // This is the Buyer's Name (e.g., BIG TRADERS)

            const fullBillingAddress = [ // This should be the BUYER's address
                addressStreetNo ? `Street ${addressStreetNo}` : null,
                addressUnitNo ? `Unit/Flat ${addressUnitNo}` : null,
                addressFloorNo ? `Floor ${addressFloorNo}` : null,
                addressBuildingNo ? `Building ${addressBuildingNo}` : null,
                addressZone ? `Zone ${addressZone}` : null,
                "Qatar" // Assuming this is for the buyer's address context
            ].filter(Boolean).join(', ');
            mainFormData.append('billingAddress', fullBillingAddress); // Buyer's billing address

            // supplierEmail should be the MSME's email, customerEmail (businessEmail state) for buyer contact
            mainFormData.append('supplierEmail', initialMindeeData.supplierEmail); // MSME's email
            mainFormData.append('customerEmail', businessEmail); // Buyer's email (used for invitation)

            // These address fields are for the BUYER
            mainFormData.append('addressZone', addressZone);
            mainFormData.append('addressStreetNo', addressStreetNo);
            mainFormData.append('addressBuildingNo', addressBuildingNo);
            mainFormData.append('addressFloorNo', addressFloorNo);
            mainFormData.append('addressUnitNo', addressUnitNo);

            mainFormData.append('netAmount', initialMindeeData.netAmount || '');
            mainFormData.append('totalTaxAmount', initialMindeeData.totalTaxAmount || '');
            if (initialMindeeData.lineItems) {
                mainFormData.append('lineItems', JSON.stringify(initialMindeeData.lineItems));
            }
            mainFormData.append('kycBuyerId', selectedKycBuyerId); // ID of the matched buyer (customer)

            const uploadResponse = await axios.post(`${config.apiUrl}/ops/invoiceFinancing/uploadInvoice`, mainFormData, { headers: { 'Content-Type': 'multipart/form-data', 'x-auth-token': token } });

            if (!uploadResponse.data?.success || !uploadResponse.data?.invoice?._id) {
                if (uploadResponse.data?.error === 'Duplicate invoice') {
                    setGeneralFormError(`DUPLICATE INVOICE: Invoice No. ${initialMindeeData.invoiceNumber} already exists.`);
                    toast.error("Duplicate invoice detected.");
                } else {
                    setGeneralFormError(uploadResponse.data?.details || uploadResponse.data?.message || "Failed to save main invoice.");
                    toast.error(uploadResponse.data?.details || uploadResponse.data?.message || "Failed to save main invoice.");
                }
                setIsSubmitting(false);
                return;
            }
            newInvoiceId = uploadResponse.data.invoice._id;

            if (isChangeRequestDrafted && newInvoiceId) {
                const ticketData = {
                    userId,
                    subject: `${initialMindeeData.invoiceNumber}: ${ticketSubject}`,
                    description: ticketDescription,
                    category: 'Invoice Financing',
                    relatedEntityType: 'Invoice',
                    relatedEntityId: newInvoiceId, // Use the ACTUAL new invoice ID
                };

                try {
                    await axios.post(`${config.apiUrl}/ops/invoiceFinancing/support/submit-ticket`, ticketData, {
                        headers: { 'x-auth-token': token }
                    });
                    // The success toast from your original code is already good.
                    // We can add another one just for the ticket.
                    toast.success("Your change request ticket was also submitted!");
                } catch (ticketError) {
                    console.error("The change request ticket failed to send:", ticketError);
                    // The invoice was created, so just warn the user about the ticket failure.
                    toast.warn("Invoice submitted, but the change request failed to send. Please contact support.");
                }
            }

            for (const doc of supportingDocuments) {
                if (doc.file) {
                    try { await uploadAdditionalInvoiceDocument(newInvoiceId, doc.file); }
                    catch (additionalError) { toast.warn(`Failed to upload ${doc.fileName}. You can add it later.`); }
                }
            }

            // Email for invitation should be the BUYER's email (current 'businessEmail' state)
            // User.email is the MSME's email
            if (newInvoiceId && businessEmail && user.email) {
                try {
                    // Parameters: msmeEmail, buyerEmail, invoiceId, invoiceNumber
                    await sendBuyerInvitationEmail(user.email, businessEmail, newInvoiceId, initialMindeeData.invoiceNumber);
                } catch (emailError) { console.error("Buyer invitation email failed:", emailError); }
            }

            toast.success("Invoice submitted successfully! Redirecting...");
            history.push("/dashboard");
            setCurrentPage('my-invoices');

        } catch (error) {
            console.error('Error during final submission:', error);
            setGeneralFormError(error.response?.data?.details || error.response?.data?.message || error.message || 'An unknown error occurred.');
            toast.error(error.response?.data?.details || error.response?.data?.message || error.message || 'An unknown error occurred during submission.');
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleSaveChangeRequest = (e) => {
        e.preventDefault();

        if (!ticketSubject || !ticketDescription) {
            toast.error("Please provide a subject and description for your change request.");
            return;
        }

        // Mark the request as drafted and close the modal.
        setIsChangeRequestDrafted(true);
        setIsRequestChangesModalOpen(false);
        toast.info("Your change request has been saved. It will be submitted along with the invoice.");
    };

    if (!initialMindeeData || !mainInvoiceFileFromPrevPage) {
        return <div className="p-8 text-center text-red-500">Error: Missing invoice data. Please go back and re-upload.</div>;
    }

    return (
        <div className="min-h-screen bg-gray-100 py-6 px-4 sm:px-6 lg:px-8">
            <button
                onClick={() => {
                    history.push("dashboard");
                    setCurrentPage('my-invoices');
                }}
                className="mb-4 inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#004141]"
            >
                <ArrowDownIcon className="w-4 h-4 mr-2 rotate-[90deg]" /> Back to My Invoices
            </button>
            <div className="mb-6 flex justify-between items-center">
                <h1 className="text-2xl font-semibold text-gray-900">Add Invoice</h1>
                <button
                    type="button"
                    onClick={() => setIsRequestChangesModalOpen(true)}
                    className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors ${isChangeRequestDrafted
                        ? 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
                        : 'bg-[#209c54] hover:bg-[#1a8245] focus:ring-[#209c54]'
                        }`}
                >
                    {isChangeRequestDrafted ? (
                        <CheckCircleIcon className="h-5 w-5 mr-2" />
                    ) : (
                        <PencilSquareIcon className="h-5 w-5 mr-2" />
                    )}
                    {isChangeRequestDrafted ? 'Changes Requested' : 'Request Changes'}
                </button>
            </div>
            <form onSubmit={handleSubmit} className="space-y-6">
                <div className="bg-white shadow-lg rounded-lg overflow-hidden max-w-full mx-auto">
                    <div className="bg-white px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h2 className="text-lg font-medium text-gray-900">
                            Invoice #{initialMindeeData?.invoiceNumber || 'N/A'}
                        </h2>
                        {mainInvoiceFileFromPrevPage && (
                            <button
                                type="button"
                                onClick={handleViewMainInvoice}
                                className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#004141]"
                            >
                                <EyeIcon className="h-4 w-4 mr-1.5" />
                                View Invoice
                            </button>
                        )}
                    </div>

                    <div className="bg-gray-50 p-4 sm:p-6">
                        <div className="bg-white rounded-lg p-4 sm:p-6 space-y-3">
                            {!isBuyerApproved && buyerApprovalError && (
                                <div className="p-4 bg-red-50 border-l-4 border-red-500 rounded-md mb-4">
                                    <div className="flex items-center">
                                        <div className="flex-shrink-0">
                                            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" aria-hidden="true" />
                                        </div>
                                        <div className="ml-3 flex-1">
                                            <p className="text-sm text-red-700">{buyerApprovalError}</p>
                                        </div>
                                        {/* This button is added here */}
                                        <div className="ml-auto pl-3">
                                            <button
                                                type="button"
                                                onClick={openAddBuyerModal}
                                                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-[#209c54] hover:bg-[#1a8245] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#209c54]"
                                            >
                                                <PlusIcon className="h-4 w-4 mr-1" />
                                                Add Buyer
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            )}
                            {generalFormError && (
                                <div className="p-3 bg-red-100 border border-red-300 text-red-700 text-sm rounded-md mb-4">
                                    {generalFormError}
                                </div>
                            )}

                            <FormRow label="Invoice Date:">
                                <LockedInput value={invoiceDate} />
                            </FormRow>

                            <FormRow label="Due Date:">
                                <LockedInput value={dueDate} />
                            </FormRow>

                            <FormRow label="Total Amount:">
                                <LockedInput value={totalAmount ? `${currency} ${totalAmount}` : ''} />
                            </FormRow>

                            {/* This 'Customer' field is for selecting/displaying the BUYER (e.g., BIG TRADERS) */}
                            <FormRow label="Customer (Buyer):">
                                {isBuyerApproved ? (
                                    <LockedInput value={customerNameForDisplay} />
                                ) : (
                                    <select
                                        name="customerName"
                                        disabled
                                        value={selectedKycBuyerId}
                                        onChange={handleCustomerSelectChange}
                                        className="w-full max-w-md px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-100 text-gray-700 cursor-not-allowed focus:outline-none focus:ring-0"
                                    >
                                        <option value="">Select an Approved Buyer...</option>
                                        {kycBuyersList.map((buyer) => (
                                            <option key={buyer._id} value={buyer._id}>
                                                {buyer.buyerName}
                                            </option>
                                        ))}
                                    </select>
                                )}
                            </FormRow>

                            {/* These address fields are for the BUYER (Customer) */}
                            {/* <FormRow label="Buyer Business Address:">
                                <div className="w-full bg-[#eff7f7] p-4 rounded-md space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <AddressInput
                                            placeholder="Zone"
                                            value={addressZone}
                                            onChange={handleAddressZoneChange}
                                        />
                                        <AddressInput
                                            placeholder="Street No."
                                            value={addressStreetNo}
                                            onChange={handleAddressStreetNoChange}
                                        />
                                        <AddressInput
                                            placeholder="Building No."
                                            value={addressBuildingNo}
                                            onChange={handleAddressBuildingNoChange}
                                        />
                                    </div>
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <AddressInput
                                            placeholder="Floor No."
                                            value={addressFloorNo}
                                            onChange={handleAddressFloorNoChange}
                                        />
                                        <AddressInput
                                            placeholder="Unit No. / Flat No."
                                            value={addressUnitNo}
                                            onChange={handleAddressUnitNoChange}
                                            className="md:col-span-2"
                                        />
                                    </div>
                                </div>
                            </FormRow> */}

                            {/* This email is for the BUYER (Customer) */}
                            <FormRow label="Buyer Business Email ID:">
                                <LockedInput value={businessEmail} />
                            </FormRow>

                            <div className="bg-[#eff7f7] p-4 rounded-md mt-6">
                                <div className="flex flex-col sm:flex-row justify-between sm:items-center mb-3">
                                    <div>
                                        <h3 className="text-md font-semibold text-gray-800">Upload Supporting Documents</h3>
                                        <p className="text-xs text-gray-600 max-w-md">You may upload the documents one by one.</p>
                                    </div>
                                    <button
                                        type="button"
                                        onClick={triggerFirstEmptyUpload}
                                        className="mt-3 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-[#209c54] hover:bg-[#1a8245] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#209c54]"
                                    >
                                        <DocumentPlusIcon className="h-5 w-5 mr-2" />
                                        Upload Documents
                                    </button>
                                </div>
                                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                                    {supportingDocuments.map((doc, index) => (
                                        <FileUploadField
                                            key={doc.key}
                                            label={doc.file ? `Document ${index + 1}` : `Document ${index + 1}`}
                                            docKey={doc.key}
                                            fileData={doc}
                                            onFileSelect={handleSupportingFileSelect}
                                            onViewClick={() => handleViewSupportingFile(doc.key)}
                                            onReplaceClick={() => handleReplaceSupportingFile(doc.key)}
                                            inputRef={el => docInputRefs.current[index] = el}
                                            disabled={isSubmitting}
                                        />
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="bg-white shadow-lg rounded-lg p-6 max-w-full mx-auto">
                    <h3 className="text-md font-semibold text-gray-800 mb-3">Invoice Financing Consent</h3>
                    <div className="prose prose-sm max-w-none text-gray-600 space-y-1 text-xs">
                        <p>1. I/We hereby undertake and confirm that the invoice submitted is genuine, accurate, and legally valid.</p>
                        <p>2. The invoice represents a legitimate business transaction and is free from any fraudulent or misleading information.</p>
                        <p>3. I/We authorize the financing platform to verify the details of this invoice with the concerned parties (supplier, customer, etc).</p>
                        <p>4. I/We understand that any false representation may lead to legal action and potential disqualification from future financing services.</p>
                        <p>5. The invoice is eligible for financing under the applicable commercial laws and regulations of the State of Qatar.</p>
                        <p>6. I/We agree to the terms and conditions associated with the invoice financing service provided.</p>
                    </div>
                    <div className="mt-4">
                        <label htmlFor="consentAccepted" className="flex items-center">
                            <input
                                id="consentAccepted"
                                name="consentAccepted"
                                type="checkbox"
                                checked={consentAccepted}
                                onChange={(e) => setConsentAccepted(e.target.checked)}
                                className="h-4 w-4 text-[#004141] border-gray-300 rounded focus:ring-[#003030]"
                            />
                            <span className="ml-2 text-sm text-gray-700">I have read, understood, and accept the consent terms.</span>
                        </label>
                    </div>
                </div>

                <div className="pt-2 pb-6 flex justify-center">
                    <button
                        type="submit"
                        disabled={isSubmitting || !consentAccepted || (!isBuyerApproved && !selectedKycBuyerId && !customerNameForDisplay)} // Adjusted disabled logic slightly
                        className="w-full sm:w-auto inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-[#004141] hover:bg-[#003030] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#004141] disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {isSubmitting ? <LoadingSpinner /> : 'Submit Invoice'}
                    </button>
                </div>
            </form>
            {isSubmitting && <GeneralLoadingModal title={"Please wait a moment"} message={"Your invoice is being submitted. Please wait a moment."} />}
            <Modal isOpen={isAddBuyerModalOpen} onClose={closeAddBuyerModal} title="Add New Buyer">
                <form onSubmit={handleSaveNewBuyer} className="space-y-4">
                    {buyerModalError && (
                        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-2 rounded-md text-sm" role="alert">
                            {buyerModalError}
                        </div>
                    )}
                    <p className="text-sm text-gray-500">Add the buyer's details. Fields marked with <span className="text-red-500">*</span> are required.</p>

                    {/* Buyer Name Input */}
                    <div>
                        <label htmlFor="buyerName" className="block text-sm font-medium text-gray-700">Buyer Name <span className="text-red-500">*</span></label>
                        <input disabled type="text" name="buyerName" id="buyerName" value={newBuyerData.buyerName} onChange={handleAddBuyerModalInputChange} required className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-sm focus:ring-indigo-500 focus:border-indigo-500" />
                    </div>

                    {/* Contact Email Input */}
                    <div>
                        <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700">Contact Email <span className="text-red-500">*</span></label>
                        <input type="email" name="contactEmail" id="contactEmail" value={newBuyerData.contactEmail} onChange={handleAddBuyerModalInputChange} required className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-sm focus:ring-indigo-500 focus:border-indigo-500" />
                    </div>

                    {/* CR Number Input */}
                    <div>
                        <label htmlFor="registrationNumber" className="block text-sm font-medium text-gray-700">CR Number</label>
                        <input type="text" name="registrationNumber" id="registrationNumber" value={newBuyerData.registrationNumber} onChange={handleAddBuyerModalInputChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-sm focus:ring-indigo-500 focus:border-indigo-500" />
                    </div>

                    {/* ADDED: Contact Person Input */}
                    <div>
                        <label htmlFor="contactPerson" className="block text-sm font-medium text-gray-700">Contact Person</label>
                        <input type="text" name="contactPerson" id="contactPerson" value={newBuyerData.contactPerson} onChange={handleAddBuyerModalInputChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-sm focus:ring-indigo-500 focus:border-indigo-500" />
                    </div>

                    {/* ADDED: Contact Phone Input */}
                    <div>
                        <label htmlFor="contactPhone" className="block text-sm font-medium text-gray-700">Contact Phone</label>
                        <input type="tel" name="contactPhone" id="contactPhone" value={newBuyerData.contactPhone} onChange={handleAddBuyerModalInputChange} className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-sm focus:ring-indigo-500 focus:border-indigo-500" />
                    </div>

                    {/* Action Buttons */}
                    <div className="pt-2 flex justify-end space-x-3">
                        <button type="button" onClick={closeAddBuyerModal} disabled={isSavingNewBuyer} className="px-4 py-2 rounded-lg text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200">
                            Cancel
                        </button>
                        <button type="submit" disabled={isSavingNewBuyer} className="min-w-[120px] flex items-center justify-center px-4 py-2 rounded-lg shadow-md text-sm font-medium text-white bg-[#004141] hover:bg-[#005050] disabled:opacity-50">
                            {isSavingNewBuyer ? <ArrowPathIcon className="w-5 h-5 animate-spin" /> : 'Save Buyer'}
                        </button>
                    </div>
                </form>
            </Modal>
            <Modal isOpen={isRequestChangesModalOpen} onClose={() => setIsRequestChangesModalOpen(false)} title="Request Invoice Changes">
                <form onSubmit={handleSaveChangeRequest} className="space-y-4">
                    <p className="text-sm text-gray-600 mb-4">
                        You may request changes in the invoice extracted fields in case of incorrect extraction.
                    </p>

                    {/* Locked Fields */}
                    <div className="space-y-2">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Category</label>
                            <LockedInput value="Invoice Financing" />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Related Entity</label>
                            <LockedInput value="Invoice" />
                        </div>
                    </div>

                    {/* User Editable Fields */}
                    <div>
                        <label htmlFor="ticket-subject" className="block text-sm font-medium text-gray-700 mb-1">Subject <span className="text-red-500">*</span></label>
                        <input
                            type="text"
                            id="ticket-subject"
                            value={ticketSubject}
                            onChange={(e) => setTicketSubject(e.target.value)}
                            className="w-full p-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm"
                            placeholder="e.g., Correction for Invoice Amount"
                            required
                        />
                    </div>

                    <div>
                        <label htmlFor="ticket-description" className="block text-sm font-medium text-gray-700 mb-1">Description <span className="text-red-500">*</span></label>
                        <textarea
                            id="ticket-description"
                            rows="4"
                            value={ticketDescription}
                            onChange={(e) => setTicketDescription(e.target.value)}
                            className="w-full p-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm resize-y"
                            placeholder="Please describe the changes you need."
                            required
                        ></textarea>
                    </div>

                    <div className="pt-2 flex justify-end">
                        <button
                            type="button"
                            onClick={() => setIsRequestChangesModalOpen(false)}
                            disabled={isTicketSubmitting}
                            className="mr-3 px-4 py-2 rounded-lg text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 transition-colors"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled={isTicketSubmitting}
                            className="min-w-[120px] flex items-center justify-center px-4 py-2 rounded-lg shadow-md text-sm font-medium transition-colors bg-[#004141] text-white hover:bg-[#005050] disabled:opacity-50"
                        >
                            {isTicketSubmitting ? <LoadingSpinner /> : 'Submit Request'}
                        </button>
                    </div>
                </form>
            </Modal>
        </div>
    );
};

export default UploadInvoicePage;