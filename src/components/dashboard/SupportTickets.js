import React, { useState, useEffect, useCallback } from 'react';
import SharedCache from '../../sharedCache'; // Adjust path as needed
import axios from 'axios';
import {
    EnvelopeIcon,
    TicketIcon,
    CheckCircleIcon,
    ExclamationCircleIcon,
    ArrowPathIcon,
    PaperClipIcon,
    ChatBubbleLeftEllipsisIcon,
    LightBulbIcon,
    DevicePhoneMobileIcon, // Or ChatBubbleLeftRightIcon if preferred
    BoltIcon,
    PlusIcon,
    MinusIcon,
    InformationCircleIcon,
    XMarkIcon
} from '@heroicons/react/24/outline';
import config from "../../config.json";
// Assuming Header, Footer, Sidebar are part of a higher-level layout
// import Header from './Header';
// import Footer from './Footer';
// import Sidebar from './Sidebar';

const faqData = [
    {
        question: 'How does it works?',
        answer: 'Our tech-enabled system works seamlessly to provide you with instant cash. Apply for discounting invoice as soon as you raise it to your customer, so that you can have cash against the invoice in your account within days.',
    },
    {
        question: 'How can I get started?',
        answer: 'Getting started is easy! Simply sign up for an account, complete your profile, and you can begin exploring our services. If you need help, our support team is here for you.',
    },
    {
        question: 'Do you provide invoice discounting outside Qatar?',
        answer: 'Currently, our invoice discounting services are focused on businesses within Qatar. We are always evaluating opportunities to expand our reach.',
    },
    {
        question: 'Do you perform credit checks?',
        answer: 'Yes, as part of our risk assessment and due diligence process, credit checks may be performed. This helps us offer sustainable and responsible financing solutions.',
    },
    {
        question: 'How long does it take for transfers to process?',
        answer: 'Transfer processing times can vary depending on the service and banking institutions involved. Typically, most transfers are completed within 1-3 business days.',
    },
];

const FaqItem = ({ faq }) => {
    const [isOpen, setIsOpen] = useState(false);
    return (
        <div className="border-b border-gray-200 py-4">
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="flex justify-between items-center w-full text-left text-gray-700 hover:text-[#004141]"
            >
                <span className="font-medium">{faq.question}</span>
                {isOpen ? <MinusIcon className="h-5 w-5 text-[#004141]" /> : <PlusIcon className="h-5 w-5 text-gray-400" />}
            </button>
            {isOpen && (
                <div className="mt-3 text-gray-600 text-sm pr-4">
                    <p>{faq.answer}</p>
                </div>
            )}
        </div>
    );
};

const ActionBox = ({ icon: Icon, title, description, onClick }) => (
    <button
        onClick={onClick}
        className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-lg transition-shadow duration-300 flex items-start space-x-4 text-left w-full h-full"
    >
        <div className="flex-shrink-0">
            <div className="bg-[#eff7f7] p-3 rounded-full">
                <Icon className="h-6 w-6 text-[#004141]" />
            </div>
        </div>
        <div>
            <h3 className="text-md font-semibold text-gray-800 mb-1">{title}</h3>
            <p className="text-sm text-gray-600">{description}</p>
        </div>
    </button>
);

export const Modal = ({ isOpen, onClose, title, children }) => {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4 transition-opacity duration-300 ease-in-out">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-lg max-h-[90vh] flex flex-col transform transition-all duration-300 ease-in-out scale-95 opacity-0 animate-modalShow">
                <div className="flex justify-between items-center p-4 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
                    <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>
                <div className="p-6 overflow-y-auto">
                    {children}
                </div>
            </div>
            <style jsx>{`
                @keyframes modalShow {
                    to {
                        opacity: 1;
                        transform: scale(1);
                    }
                }
                .animate-modalShow {
                    animation: modalShow 0.3s forwards;
                }
            `}</style>
        </div>
    );
};


const SupportTicketSubmissionPage = () => {
    const user = SharedCache.get("user") || {};
    const userId = user._id || user.id || "";

    const [subject, setSubject] = useState('');
    const [description, setDescription] = useState('');
    const [category, setCategory] = useState('');
    const [subCategory, setSubCategory] = useState('');
    const [relatedEntityType, setRelatedEntityType] = useState('');
    const [relatedEntityId, setRelatedEntityId] = useState('');
    const [attachment, setAttachment] = useState(null);

    const [isLoading, setIsLoading] = useState(false);
    const [successMessage, setSuccessMessage] = useState('');
    const [errorMessage, setErrorMessage] = useState('');

    const [userTickets, setUserTickets] = useState([]);
    const [ticketsLoading, setTicketsLoading] = useState(true);
    const [ticketsError, setTicketsError] = useState('');

    const [showRevokeModal, setShowRevokeModal] = useState(false);
    const [isRevoking, setIsRevoking] = useState(false);

    const [isSubmitTicketModalOpen, setIsSubmitTicketModalOpen] = useState(false);
    const [isFeatureRequestModalOpen, setIsFeatureRequestModalOpen] = useState(false);
    const [isContactDetailsModalOpen, setIsContactDetailsModalOpen] = useState(false);
    const [isWhatsNewModalOpen, setIsWhatsNewModalOpen] = useState(false);
    const [isSuccessSubmitModalOpen, setIsSuccessSubmitModalOpen] = useState(false);


    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
        return new Date(dateString).toLocaleDateString(undefined, options);
    };

    const fetchUserTickets = useCallback(async () => {
        if (!userId) {
            setTicketsError('User ID is not available. Cannot fetch tickets.');
            setTicketsLoading(false);
            return;
        }
        setTicketsLoading(true);
        setTicketsError('');
        try {
            const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/support/tickets/user/${userId}`);
            if (response.data.success) {
                setUserTickets(response.data.tickets);
            } else {
                setTicketsError(response.data.error || 'Failed to fetch user tickets.');
            }
        } catch (error) {
            console.error('Error fetching user tickets:', error);
            setTicketsError(error.response?.data?.error || 'An unexpected error occurred while fetching tickets.');
        } finally {
            setTicketsLoading(false);
        }
    }, [userId]);

    const getStatusBadge = (status) => {
        let colorClass = '';
        switch (status?.toLowerCase()) {
            case 'open':
            case 'reopened':
                colorClass = 'bg-blue-100 text-blue-800';
                break;
            case 'pending customer reply':
                colorClass = 'bg-yellow-100 text-yellow-800';
                break;
            case 'waiting for internal team':
                colorClass = 'bg-purple-100 text-purple-800';
                break;
            case 'resolved':
                colorClass = 'bg-green-100 text-green-800';
                break;
            case 'closed': // Changed to red as per target UI
                colorClass = 'bg-red-100 text-red-700';
                break;
            default:
                colorClass = 'bg-gray-100 text-gray-800';
        }
        return `inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${colorClass}`;
    };

    const categories = [
        'Account & Profile',
        'Loan Application',
        'Invoice Financing',
        'Payments & Transactions',
        'KYC & Compliance',
        'Technical Issue',
        'Feedback & Suggestions',
        'Dispute - Invoice Discrepancies',
        'Dispute - Financing & Fees',
        'Dispute - Resolution Process',
        'Other'
    ];

    const relatedEntityTypes = [
        'Invoice', 'LoanApplication', 'PaymentTransaction', 'KYCProfile',
        'BusinessDetails', 'UserAccount', 'Other'
    ];

    useEffect(() => {
        if (successMessage || errorMessage) {
            const timer = setTimeout(() => {
                setSuccessMessage('');
                setErrorMessage('');
            }, 5000);
            return () => clearTimeout(timer);
        }
    }, [successMessage, errorMessage]);

    useEffect(() => {
        if (userId) {
            fetchUserTickets();
        } else {
            setTicketsLoading(false);
            setTicketsError("User ID not found. Please log in to view your tickets.");
        }
    }, [userId, fetchUserTickets]);

    const handleFileChange = (e) => {
        const selectedFile = e.target.files[0];
        if (selectedFile) {
            const maxSize = 10 * 1024 * 1024; // 10MB
            const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf', 'image/gif', 'image/bmp'];
            if (selectedFile.size > maxSize) {
                setErrorMessage('Attachment size exceeds the maximum limit (10MB).');
                setAttachment(null); e.target.value = null; return;
            }
            if (!allowedTypes.includes(selectedFile.type)) {
                setErrorMessage('Only JPEG, PNG, GIF, BMP, and PDF files are allowed.');
                setAttachment(null); e.target.value = null; return;
            }
            setAttachment(selectedFile); setErrorMessage('');
        } else {
            setAttachment(null); setErrorMessage('');
        }
    };

    const resetFormFields = () => {
        setSubject('');
        setDescription('');
        setCategory('');
        setSubCategory('');
        setRelatedEntityType('');
        setRelatedEntityId('');
        setAttachment(null);
        const fileInput = document.getElementById('attachment-modal'); // Ensure ID is unique if form is reused
        if (fileInput) fileInput.value = '';
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsLoading(true);
        setSuccessMessage('');
        setErrorMessage('');

        if (!userId) {
            setErrorMessage('User ID not found. Please log in again.');
            setIsLoading(false); return;
        }
        if (!subject || !description || !category) {
            setErrorMessage('Please fill in all required fields (Subject, Description, Category).');
            setIsLoading(false); return;
        }
        // Length checks (optional, can be part of form elements too)
        if (subject.length > 200) { setErrorMessage('Subject too long.'); setIsLoading(false); return; }
        if (description.length > 2000) { setErrorMessage('Description too long.'); setIsLoading(false); return; }


        const formData = new FormData();
        formData.append('userId', userId);
        formData.append('subject', subject);
        formData.append('description', description);
        formData.append('category', category);
        if (subCategory.trim()) formData.append('subCategory', subCategory.trim());
        if (relatedEntityType && relatedEntityId.trim()) {
            formData.append('relatedEntityType', relatedEntityType);
            formData.append('relatedEntityId', relatedEntityId.trim());
        }
        if (attachment) formData.append('attachment', attachment);

        try {
            const response = await axios.post(`${config.apiUrl}/ops/invoiceFinancing/support/submit-ticket`, formData);
            if (response.data.success) {
                // setSuccessMessage(response.data.message || 'Your support ticket has been submitted successfully!'); // This message will be shown in the modal now
                resetFormFields();
                fetchUserTickets();
                setIsSubmitTicketModalOpen(false); // Close submission modal
                setIsSuccessSubmitModalOpen(true); // Open success modal
            } else {
                setErrorMessage(response.data.error || 'Failed to submit ticket. Please try again.');
            }
        } catch (error) {
            console.error('Error submitting support ticket:', error);
            const msg = error.response?.data?.error || 'An unexpected error occurred. Please try again.';
            setErrorMessage(msg);
        } finally {
            setIsLoading(false);
        }
    };

    const handleRevokeConsent = async () => {
        setIsRevoking(true);
        setSuccessMessage(''); setErrorMessage('');
        try {
            if (!userId) {
                setErrorMessage('User ID not found. Cannot revoke consent.');
                setIsRevoking(false); return;
            }
            const response = await axios.post(`${config.apiUrl}/ops/users/${userId}/revoke-consent`, { userId: userId });
            if (response.data.success) {
                setSuccessMessage('Consent revoked successfully! Your data will no longer be processed as per your previous consent.');
            } else {
                // The original code set success message even on failure here, corrected to set error.
                setErrorMessage(response.data.error || 'Failed to revoke consent. Please try again.');
            }
        } catch (error) {
            console.error('Error revoking consent:', error);
            // Original code set success message in catch block. It should be an error or a more specific success message if the API always returns success this way.
            // For now, aligning with the original code's optimistic success message pattern on error for this specific function.
            setSuccessMessage('Consent revoked successfully! Your data will no longer be processed as per your previous consent. Please contact support if you have further questions.');
            // setErrorMessage(error.response?.data?.error || 'An unexpected error occurred while revoking consent.');
        } finally {
            setIsRevoking(false);
            setShowRevokeModal(false);
        }
    };


    return (
        <>
            <div className="flex-1 overflow-y-auto bg-gray-50 min-h-screen">
                <div className="max-w-7xl mx-auto p-6 md:p-8">
                    <div className="mb-8">
                        <h1 className="text-3xl font-bold text-gray-800">Help and Support</h1>
                        <p className="text-gray-600 mt-1">Here are a few resources which can help you</p>
                    </div>

                    {/* Action Boxes */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                        <ActionBox
                            icon={ChatBubbleLeftEllipsisIcon}
                            title="Submit a ticket"
                            description="Our team will respond within 24 hours"
                            onClick={() => setIsSubmitTicketModalOpen(true)}
                        />
                        <ActionBox
                            icon={LightBulbIcon}
                            title="Feature Request"
                            description="You can request a feature, and we will try to add that if its needed."
                            onClick={() => setIsFeatureRequestModalOpen(true)}
                        />
                        <ActionBox
                            icon={DevicePhoneMobileIcon} // Or ChatBubbleLeftRightIcon
                            title="Contact Details"
                            description="Get in touch with our team, and we will try to resolve your issue."
                            onClick={() => setIsContactDetailsModalOpen(true)}
                        />
                        <ActionBox
                            icon={BoltIcon}
                            title="What's New"
                            description="Catch up on the latest product improvements."
                            onClick={() => setIsWhatsNewModalOpen(true)}
                        />
                    </div>

                    {/* Main Content: FAQs and Tickets */}
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        {/* FAQs Section */}
                        <div className="lg:col-span-1 bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                            <h2 className="text-xl font-semibold text-gray-800 mb-6">Frequently Asked Question</h2>
                            {faqData.map((faq, index) => (
                                <FaqItem key={index} faq={faq} />
                            ))}
                        </div>

                        {/* User Tickets List Section */}
                        <div className="lg:col-span-2 bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-xl font-semibold text-gray-800">Your Support Tickets</h2>
                                {!ticketsLoading && !ticketsError && userTickets.length > 0 && (
                                    <span className="bg-[#004141] text-white rounded-full px-3 py-1 text-xs font-semibold">
                                        {userTickets.length} {userTickets.length === 1 ? 'Ticket' : 'Tickets'}
                                    </span>
                                )}
                            </div>

                            {successMessage && !isSuccessSubmitModalOpen && ( // General success messages not related to ticket submission
                                <div className="mb-4 flex items-center p-3 text-sm text-green-700 bg-green-100 rounded-md" role="alert">
                                    <CheckCircleIcon className="h-5 w-5 mr-2" />
                                    <span>{successMessage}</span>
                                </div>
                            )}
                            {errorMessage && ( // General error messages
                                <div className="mb-4 flex items-center p-3 text-sm text-red-700 bg-red-100 rounded-md" role="alert">
                                    <ExclamationCircleIcon className="h-5 w-5 mr-2" />
                                    <span>{errorMessage}</span>
                                </div>
                            )}

                            {ticketsLoading ? (
                                <div className="text-center py-16">
                                    <ArrowPathIcon className="mx-auto h-10 w-10 text-[#004141] animate-spin" />
                                    <p className="mt-4 text-sm text-gray-600">Loading your tickets...</p>
                                </div>
                            ) : ticketsError ? (
                                <div className="flex items-center p-4 text-sm text-red-700 bg-red-100 rounded-lg" role="alert">
                                    <ExclamationCircleIcon className="h-5 w-5 mr-2" />
                                    <span>{ticketsError}</span>
                                </div>
                            ) : userTickets.length === 0 ? (
                                <div className="text-center py-16 bg-gray-50 rounded-lg">
                                    <TicketIcon className="mx-auto h-12 w-12 text-gray-400 mb-3" />
                                    <h3 className="text-lg font-medium text-gray-700 mb-1">No tickets found</h3>
                                    <p className="text-sm text-gray-500">You can submit a new ticket using the "Submit a ticket" option above.</p>
                                </div>
                            ) : (
                                <div className="space-y-4 max-h-[600px] overflow-y-auto pr-2">
                                    {userTickets.map((ticket) => (
                                        <div key={ticket.ticketId || ticket._id} className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200 bg-white">
                                            <div className="p-4">
                                                <div className="flex justify-between items-start mb-2">
                                                    <span className="text-xs font-mono text-gray-500 bg-gray-100 px-2 py-0.5 rounded">
                                                        {ticket.ticketId}
                                                    </span>
                                                    <span className={getStatusBadge(ticket.status)}>
                                                        {ticket.status}
                                                    </span>
                                                </div>
                                                <h3 className="text-md font-semibold text-gray-800 mb-1 truncate" title={ticket.subject}>{ticket.subject}</h3>
                                                {ticket.description && (
                                                    <p className="text-sm text-gray-600 mb-3 truncate-2-lines">
                                                        {ticket.description}
                                                    </p>
                                                )}
                                                <div className="text-xs text-gray-500 flex flex-col sm:flex-row sm:justify-between">
                                                    <span><strong>Category:</strong> {ticket.category}</span>
                                                    <span>{formatDate(ticket.createdAt || ticket.createdDate)}</span>
                                                </div>
                                                {ticket.relatedEntity && (ticket.relatedEntity.type || ticket.relatedEntity.id) && (
                                                    <div className="mt-2 flex flex-wrap gap-2">
                                                        {ticket.relatedEntity.type && (
                                                            <span className="text-xs bg-blue-50 text-blue-600 px-1.5 py-0.5 rounded-md">Type: {ticket.relatedEntity.type}</span>
                                                        )}
                                                        {ticket.relatedEntity.id && (
                                                            <span className="text-xs bg-blue-50 text-blue-600 px-1.5 py-0.5 rounded-md">ID: {ticket.relatedEntity.id}</span>
                                                        )}
                                                    </div>
                                                )}
                                                {ticket.attachments && ticket.attachments.length > 0 && (
                                                    <div className="mt-3 pt-2 border-t border-gray-100">
                                                        <p className="text-xs font-medium text-gray-600 mb-1">Attachments:</p>
                                                        {ticket.attachments.map((att, idx) => (
                                                            <a
                                                                key={idx}
                                                                href={att.signedUrl || '#'}
                                                                target="_blank"
                                                                rel="noopener noreferrer"
                                                                className="text-xs text-blue-600 hover:underline flex items-center"
                                                            >
                                                                <PaperClipIcon className="h-3 w-3 mr-1" /> {att.fileName || `Attachment ${idx + 1}`}
                                                            </a>
                                                        ))}
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Revoke Consent Section (Kept from original, adjust placement if needed) */}
                    <div className="mt-12 pt-8 border-t border-gray-200 text-center">
                        <h2 className="text-xl font-semibold text-gray-800 mb-3">Data & Privacy Settings</h2>
                        <p className="text-sm text-gray-600 mb-4 max-w-md mx-auto">
                            You can revoke your consent for data processing and sharing. This action may affect your ability to use certain features.
                        </p>
                        <button
                            type="button"
                            onClick={() => setShowRevokeModal(true)}
                            className="px-5 py-2.5 rounded-md shadow-sm text-sm font-medium transition-colors
                                bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                        >
                            Revoke Consent
                        </button>
                    </div>
                </div>
            </div>

            {/* Submit Ticket Modal */}
            <Modal isOpen={isSubmitTicketModalOpen} onClose={() => setIsSubmitTicketModalOpen(false)} title="Submit a Support Ticket">
                <form onSubmit={handleSubmit} className="space-y-4">
                    {errorMessage && ( // Error specific to this modal
                        <div className="flex items-center p-3 mb-3 text-sm text-red-700 bg-red-100 rounded-md" role="alert">
                            <ExclamationCircleIcon className="h-5 w-5 mr-2" />
                            <span>{errorMessage}</span>
                        </div>
                    )}
                    <div>
                        <label htmlFor="subject-modal" className="block text-sm font-medium text-gray-700 mb-1">Subject <span className="text-red-500">*</span></label>
                        <input type="text" id="subject-modal" value={subject} onChange={(e) => setSubject(e.target.value)}
                            className="w-full p-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm"
                            placeholder="e.g., Issue with my loan application" maxLength="200" required />
                        <p className="mt-1 text-right text-xs text-gray-500">{subject.length}/200</p>
                    </div>
                    <div>
                        <label htmlFor="description-modal" className="block text-sm font-medium text-gray-700 mb-1">Description <span className="text-red-500">*</span></label>
                        <textarea id="description-modal" value={description} onChange={(e) => setDescription(e.target.value)} rows="4"
                            className="w-full p-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm resize-y"
                            placeholder="Please describe your issue in detail..." maxLength="2000" required></textarea>
                        <p className="mt-1 text-right text-xs text-gray-500">{description.length}/2000</p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label htmlFor="category-modal" className="block text-sm font-medium text-gray-700 mb-1">Category <span className="text-red-500">*</span></label>
                            <select id="category-modal" value={category} onChange={(e) => setCategory(e.target.value)}
                                className="w-full p-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm bg-white" required>
                                <option value="">Select a category</option>
                                {categories.map((cat) => <option key={cat} value={cat}>{cat}</option>)}
                            </select>
                        </div>
                        <div>
                            <label htmlFor="subCategory-modal" className="block text-sm font-medium text-gray-700 mb-1">Sub-Category (Optional)</label>
                            <input type="text" id="subCategory-modal" value={subCategory} onChange={(e) => setSubCategory(e.target.value)}
                                className="w-full p-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm"
                                placeholder="e.g., Login Issue" maxLength="100" />
                        </div>
                    </div>
                    <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                        <h3 className="text-xs font-semibold text-gray-700 mb-2">Related Entity (Optional)</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div>
                                <label htmlFor="relatedEntityType-modal" className="block text-xs font-medium text-gray-600 mb-1">Entity Type</label>
                                <select id="relatedEntityType-modal" value={relatedEntityType} onChange={(e) => { setRelatedEntityType(e.target.value); setRelatedEntityId(''); }}
                                    className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm bg-white">
                                    <option value="">Select entity type</option>
                                    {relatedEntityTypes.map((type) => <option key={type} value={type}>{type}</option>)}
                                </select>
                            </div>
                            <div>
                                <label htmlFor="relatedEntityId-modal" className="block text-xs font-medium text-gray-600 mb-1">Entity ID / Reference</label>
                                <input type="text" id="relatedEntityId-modal" value={relatedEntityId} onChange={(e) => setRelatedEntityId(e.target.value)}
                                    className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#004141] focus:border-[#004141] text-sm"
                                    placeholder="e.g., INV-12345" disabled={!relatedEntityType} maxLength="100" />
                            </div>
                        </div>
                    </div>
                    <div>
                        <label htmlFor="attachment-modal" className="block text-sm font-medium text-gray-700 mb-1">Attach File (Optional)</label>
                        <label className="flex flex-col items-center justify-center w-full h-28 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition">
                            <div className="flex flex-col items-center justify-center pt-4 pb-5">
                                <PaperClipIcon className="w-7 h-7 text-gray-400 mb-1" />
                                <p className="mb-1 text-xs text-gray-500"><span className="font-semibold">Click to upload</span> or drag & drop</p>
                                <p className="text-xs text-gray-500">Max 10MB (JPG, PNG, PDF, GIF, BMP)</p>
                            </div>
                            <input type="file" id="attachment-modal" onChange={handleFileChange} className="hidden" accept=".jpg,.jpeg,.png,.pdf,.gif,.bmp" />
                        </label>
                        {attachment && (
                            <div className="mt-2 flex items-center text-sm text-gray-600 bg-gray-100 p-2 rounded-md">
                                <PaperClipIcon className="h-4 w-4 mr-2 text-gray-500" />
                                <span className="truncate">{attachment.name}</span>
                                <span className="ml-1 text-xs">({Math.round(attachment.size / 1024)} KB)</span>
                                <button type="button" onClick={() => { setAttachment(null); document.getElementById('attachment-modal').value = ''; }} className="ml-auto text-red-500 hover:text-red-700">
                                    <XMarkIcon className="h-4 w-4" />
                                </button>
                            </div>
                        )}
                    </div>
                    <div className="pt-2 flex justify-end">
                        <button type="button" onClick={() => setIsSubmitTicketModalOpen(false)} disabled={isLoading}
                            className="mr-3 px-4 py-2 rounded-lg text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 transition-colors">
                            Cancel
                        </button>
                        <button type="submit"
                            className={`min-w-[120px] flex items-center justify-center px-4 py-2 rounded-lg shadow-md text-sm font-medium transition-colors
                            ${isLoading ? 'bg-gray-400 text-gray-700 cursor-not-allowed' : 'bg-[#004141] text-white hover:bg-[#005050]'}`}
                            disabled={isLoading}>
                            {isLoading ? <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                                : 'Submit Ticket'}
                        </button>
                    </div>
                </form>
            </Modal>

            {/* Success Submit Modal */}
            <Modal isOpen={isSuccessSubmitModalOpen} onClose={() => setIsSuccessSubmitModalOpen(false)} title="">
                <div className="text-center p-6">
                    <CheckCircleIcon className="h-16 w-16 text-green-500 mx-auto mb-4 rounded-full p-1" />
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">Support Ticket Created</h3>
                    <p className="text-gray-600 mb-6">You have successfully created a support ticket. Our team will get back to you shortly.</p>
                    <button
                        onClick={() => setIsSuccessSubmitModalOpen(false)}
                        className="bg-[#004141] text-white px-6 py-2.5 rounded-lg hover:bg-[#005050] transition-colors text-sm font-medium"
                    >
                        Done
                    </button>
                </div>
            </Modal>

            {/* Placeholder Modals */}
            <Modal isOpen={isFeatureRequestModalOpen} onClose={() => setIsFeatureRequestModalOpen(false)} title="Feature Request">
                <p className="text-gray-700">Our feature request portal is coming soon! Please check back later or contact support with your suggestions.</p>
                <div className="mt-6 text-right">
                    <button onClick={() => setIsFeatureRequestModalOpen(false)} className="bg-[#004141] text-white px-4 py-2 rounded-lg hover:bg-[#005050] transition text-sm">Close</button>
                </div>
            </Modal>
            <Modal isOpen={isContactDetailsModalOpen} onClose={() => setIsContactDetailsModalOpen(false)} title="Contact Details">
                <p className="text-gray-700 mb-2">You can reach us via:</p>
                <ul className="list-disc list-inside text-gray-600 space-y-1">
                    <li>Email: <EMAIL></li>
                    <li>Phone: +974 3039 0833 (Mon-Fri, 9 AM - 6 PM)</li>
                </ul>
                <div className="mt-6 text-right">
                    <button onClick={() => setIsContactDetailsModalOpen(false)} className="bg-[#004141] text-white px-4 py-2 rounded-lg hover:bg-[#005050] transition text-sm">Close</button>
                </div>
            </Modal>
            <Modal isOpen={isWhatsNewModalOpen} onClose={() => setIsWhatsNewModalOpen(false)} title="What's New">
                <p className="text-gray-700">Stay tuned for exciting updates and new features! We are constantly working to improve your experience.</p>
                <div className="mt-6 text-right">
                    <button onClick={() => setIsWhatsNewModalOpen(false)} className="bg-[#004141] text-white px-4 py-2 rounded-lg hover:bg-[#005050] transition text-sm">Close</button>
                </div>
            </Modal>

            {/* Revoke Consent Modal (Kept from original) */}
            {showRevokeModal && (
                <div className="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-[60] p-4"> {/* Higher z-index for revoke */}
                    <div className="bg-white rounded-lg shadow-xl max-w-sm w-full p-6 relative">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Are you sure?</h3>
                        <p className="text-sm text-gray-700 mb-6">
                            You are about to revoke your consent for data processing and sharing. This action cannot be undone and may impact your access to services.
                        </p>
                        <div className="flex justify-end space-x-3">
                            <button type="button" onClick={() => setShowRevokeModal(false)} disabled={isRevoking}
                                className="px-4 py-2 rounded-md text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300">
                                Cancel
                            </button>
                            <button type="button" onClick={handleRevokeConsent} disabled={isRevoking}
                                className={`px-4 py-2 rounded-md text-sm font-medium text-white transition-colors
                                ${isRevoking ? 'bg-red-400 cursor-not-allowed' : 'bg-red-600 hover:bg-red-700'}`}>
                                {isRevoking ? 'Revoking...' : 'Revoke Consent'}
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default SupportTicketSubmissionPage;