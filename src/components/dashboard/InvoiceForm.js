import React, { useState } from 'react';
import axios from 'axios';
import config from "../../config.json"

const InvoiceForm = ({ onClose, panNo, gstin }) => {

  console.log("panNo", panNo, gstin)
  const [invoiceData, setInvoiceData] = useState({
    invoiceFile: null,
    anchorCompany: '',
    invoiceDate: '',
    dueDate: '',
    amount: '',
    billingAddress: '',
    customerAddress: '',
  });
  const [uploadedFileName, setUploadedFileName] = useState('');
  const [pdfPreview, setPdfPreview] = useState('');
  const [invoiceDetails, setInvoiceDetails] = useState({});

  // Handle file selection (but don't upload yet)
  const handleChange = (e) => {
    const { name, value, files } = e.target;
    setInvoiceData({
      ...invoiceData,
      [name]: files ? files[0] : value, // Handle file inputs
    });

    if (name === 'invoiceFile' && files.length > 0) {
      // Preview the uploaded PDF file
      const fileUrl = URL.createObjectURL(files[0]);
      setPdfPreview(fileUrl);
      setUploadedFileName(files[0].name);
    }
  };

  // Handle form submission and file upload
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!invoiceData.invoiceFile) {
      alert('Please upload an invoice file');
      return;
    }

    try {
      // Step 1: Send file to Mindee for extracting invoice details
      const formData1 = new FormData();
      formData1.append('document', invoiceData.invoiceFile);
      const response = await axios.post(
        'https://api.mindee.net/v1/products/mindee/invoices/v4/predict',
        formData1,
        {
          headers: {
            Authorization: 'a22f7e4051a9178de0f37d3d7a49b17c',
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      // Extract and set invoice details from Mindee API response
      const data = response.data.document.inference.prediction;
      console.log("Data", data)
      setInvoiceDetails({
        invoiceNumber: data.invoice_number.value,
        invoiceDate: data.date.value,
        dueDate: data.due_date.value,
        totalAmount: data.total_amount.value,
        supplierName: data.supplier_name.value,
        customerName: data.customer_name.value,
        billingAddress: data.supplier_address.value,
        customerAddress: data.customer_address.value,
      });

      // Step 2: Set the extracted details and upload the invoice to the backend API
      const formData = new FormData();
      formData.append('pdfFile', invoiceData.invoiceFile);
      formData.append('invoiceDate', invoiceDetails.invoiceDate);
      formData.append('dueDate', invoiceDetails.dueDate);
      formData.append('panNo', panNo);
      formData.append('gstin', gstin);
      formData.append('status', "pending");
      formData.append('totalAmount', invoiceDetails.totalAmount);
      formData.append('billingAddress', invoiceDetails.billingAddress);
      formData.append('customerAddress', invoiceDetails.customerAddress);
      formData.append('invoiceNumber', invoiceDetails.invoiceNumber);
      formData.append('supplierName', invoiceDetails.supplierName);
      formData.append('customerName', invoiceDetails.customerName);

      const uploadResponse = await axios.post(
        `${config.apiUrl}/invoiceFinancing/uploadInvoice`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      console.log('Invoice uploaded successfully:', uploadResponse.data);
      onClose();
    } catch (error) {
      console.error('Error uploading invoice:', error);
      alert('Failed to upload invoice.');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center">
      <div className="bg-white p-6 rounded-md shadow-lg w-full max-w-md">
        <h2 className="text-2xl mb-4">Create New Invoice</h2>
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block text-gray-700">Upload Invoice</label>
            <input
              type="file"
              name="invoiceFile"
              className="border p-2 w-full"
              onChange={handleChange}  // Handle file selection only
              accept="application/pdf"
            />
            {pdfPreview && (
              <iframe
                src={pdfPreview}
                title="PDF Preview"
                width="100%"
                height="400px"
              />
            )}
          </div>
          <div className="mb-4">
            <label className="block text-gray-700">Buyer Company</label>
            <select
              name="anchorCompany"
              className="border p-2 w-full"
              onChange={handleChange}
            >
              <option value="">Select Buyer</option>
              <option value="Company A">Company A</option>
              <option value="Company B">Company B</option>
            </select>
          </div>
          <div className="mb-4">
            <label className="block text-gray-700">Invoice Date</label>
            <input
              type="date"
              name="invoiceDate"
              className="border p-2 w-full"
              onChange={handleChange}
            />
          </div>
          <div className="mb-4">
            <label className="block text-gray-700">Due Date</label>
            <input
              type="date"
              name="dueDate"
              className="border p-2 w-full"
              onChange={handleChange}
            />
          </div>
          <div className="mb-4">
            <label className="block text-gray-700">Amount</label>
            <input
              type="number"
              name="amount"
              className="border p-2 w-full"
              onChange={handleChange}
            />
          </div>
          <div className="flex justify-end">
            <button
              type="button"
              className="mr-4 bg-gray-500 text-white py-2 px-4 rounded-md"
              onClick={onClose}
            >
              Cancel
            </button>
            <button type="submit" className="bg-[#004141] text-white py-2 px-4 rounded-md">
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default InvoiceForm;
