import React, { useState, useEffect } from 'react'; // Added useMemo
import { useHistory, useLocation } from "react-router-dom";
import axios from 'axios';
import config from '../../config.json';
import SharedCache from '../../sharedCache';
import ActivationJourney from './ActivationJourney';
import { generateRevolvingCreditPdf } from './CreditLineContract';
// Import Recharts components
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
  PieChart, // Add this
  Pie,      // Add this
  Cell      // Add this
} from 'recharts';
import LoadingModal from '../Reusable/Loading';
// Helper function to fetch lender details (Keep existing)
const fetchLenderDetails = async (lenderId, token) => {
  try {
    const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/lenders/${lenderId}`, {
      headers: { 'x-auth-token': token }
    });
    return response.data;
  } catch (error) {
    console.error(`Error fetching lender details for ID ${lenderId}:`, error);
    return null;
  }
};

const LoadingIndicator = () => {
  return <LoadingModal message='Please wait while we load your dashboard data for you.' />
};

const DashboardOverview = ({ setCurrentPage }) => {
  const [creditLine, setCreditLine] = useState(null);
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pendingPayments, setPendingPayments] = useState([]);
  // Initialize metrics with 0 or appropriate defaults, not hardcoded examples
  const [dashboardMetrics, setDashboardMetrics] = useState({
    totalAmountFunded: 0,
    invoicesFunded: 0,
    outstandingPayments: 0,
    invoicesDue: 0,
    invoiceApprovalsPending: 0,
    amountPendingForApproval: 0,
    disbursalsPending: 0,
    // Dates and amounts should ideally be null or undefined initially
    nextDueDate: null,
    nextDueAmount: 0,
    // New metric for graph
    monthlyInvoicesDisbursed: 0,
  });
  const [activeTab, setActiveTab] = useState('overview');
  const [merchantOffers, setMerchantOffers] = useState([]);
  const [lenderDetails, setLenderDetails] = useState({});
  const [acceptedOfferId, setAcceptedOfferId] = useState(null);
  const [acceptedLenderDetails, setAcceptedLenderDetails] = useState(null);
  const [isAccepting, setIsAccepting] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [contractPdfUrl, setContractPdfUrl] = useState(''); // Default URL, will be updated dynamically
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false); // Track PDF generation state
  const [selectedOfferId, setSelectedOfferId] = useState(null); // Track the selected offer ID
  const [isKfsModalOpen, setIsKfsModalOpen] = useState(false);
  const [kfsOfferData, setKfsOfferData] = useState(null);
  const history = useHistory();
  const location = useLocation(); // ADDED LINE: Initialize useLocation
  const [isExportDropdownOpen, setIsExportDropdownOpen] = useState(false);
  const [isSortDropdownOpen, setIsSortDropdownOpen] = useState(false);
  const [isCreditLineOverviewOpen, setIsCreditLineOverviewOpen] = useState(true); // New state for dropdown
  // --- STATE FOR CHART DATA ---
  const [invoiceChartData, setInvoiceChartData] = useState([]);
  const [disbursalChartData, setDisbursalChartData] = useState([]);
  // --- END CHART DATA STATE ---

  const tabBaseStyle = "py-3 px-5 text-sm font-medium cursor-pointer transition-colors duration-150 ease-in-out focus:outline-none border-b-2";
  const activeTabStyle = "border-blue-600 text-blue-600";
  const inactiveTabStyle = "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300";
  useEffect(() => {
    if (location.state?.initialTab === 'journey') {
      setActiveTab('journey');
      // Optional: If you want to clear this state after setting the tab
      // so it doesn't persist on refresh or subsequent navigations without this state,
      // you can replace the history state.
      // history.replace(location.pathname, { ...location.state, initialTab: undefined });
    }
  }, [location.state, history]);

  const handleOpenModal = async () => {
    setIsGeneratingPdf(true);

    try {
      // Get the accepted offer ID from the credit line
      let offerId = creditLine?.acceptedOfferId;

      // If no offer ID in credit line, try to get it from local storage as fallback
      if (!offerId) {
        try {
          const storedOfferId = localStorage.getItem('acceptedOfferId');
          if (storedOfferId) {
            console.log("Using offer ID from local storage:", storedOfferId);
            offerId = storedOfferId;
          }
        } catch (storageError) {
          console.warn("Error accessing local storage:", storageError);
        }
      }

      if (!offerId) {
        console.error("No accepted offer ID found in credit line or local storage");
        setIsGeneratingPdf(false);
        setIsModalOpen(true);
        return;
      }

      // Store the selected offer ID for reference
      setSelectedOfferId(offerId);
      console.log("Selected offer ID for contract generation:", offerId);

      // Find the offer in merchantOffers if available
      let offerDetails = null;
      if (merchantOffers.length > 0) {
        offerDetails = merchantOffers.find(offer => offer._id === offerId);
      }

      // If we couldn't find the specific offer, use the creditLine data
      // but add the necessary fields that the API expects
      if (!offerDetails) {
        offerDetails = {
          _id: offerId,
          creditLimit: creditLine.creditLimit,
          interestRate: creditLine.interestRate,
          tenureDays: creditLine.tenure || 90,
          advanceRate: creditLine.advanceRate || 80,
          maxInvoiceAge: creditLine.maxInvoiceAge || 90,
          lenderId: creditLine.lenderId,
          processingFee: {
            type: 'percentage',
            value: 1 // Default value to ensure it's always present
          },
          lateFee: {
            rate: creditLine.lateFeeRate || 1
          }
        };
      }

      console.log("Using offer details for contract generation:", offerDetails);

      // Only proceed if we have the necessary data
      if (offerDetails && acceptedLenderDetails) {
        // Create agreement flags object (all true for simplicity)
        const agreementFlags = {
          agreedAccurate: true,
          readTerms: true,
          readFinancialConsent: true
        };

        // Generate the PDF
        const result = await generateRevolvingCreditPdf(
          offerDetails,       // The offer details with the correct ID
          user,               // user
          acceptedLenderDetails, // lenderDetails
          token,              // token
          config,             // config
          agreementFlags      // agreementFlags
        );

        if (result.success && result.signedUrl) {
          // Update the PDF URL with the dynamically generated one
          setContractPdfUrl(result.signedUrl);
          console.log("Contract PDF URL updated:", result.signedUrl);
        } else {
          console.error("Failed to generate contract PDF:", result.error);
          // Keep using the default URL if generation fails
        }
      } else {
        console.error("Missing required data for contract generation");
      }
    } catch (error) {
      console.error("Error generating contract PDF:", error);
      // Continue with default PDF if generation fails
    } finally {
      setIsGeneratingPdf(false);
      setIsModalOpen(true);
    }
  };

  const handleCloseModal = () => setIsModalOpen(false);
  const handleOpenKfsModal = (offerId) => {
    const offer = merchantOffers.find(o => o._id === offerId);
    const lender = lenderDetails[offer?.lenderId];
    if (offer && lender) {
      setKfsOfferData({ ...offer, lender });
      setIsKfsModalOpen(true);
    } else {
      console.error("Could not find offer or lender details for KFS modal.");
    }
  };
  const handleCloseKfsModal = () => {
    setIsKfsModalOpen(false);
    setKfsOfferData(null);
  };

  const handleModalConfirmAccept = async () => {
    // (Keep existing modal confirm logic)
    if (!kfsOfferData?._id) return;
    setIsAccepting(true);
    handleCloseKfsModal();
    console.log(`Attempting to accept offer via modal: ${kfsOfferData._id}`);
    // --- Simulation for UI ---
    await new Promise(resolve => setTimeout(resolve, 700)); // Simulate network delay
    setAcceptedOfferId(kfsOfferData._id);
    setCreditLine(prev => ({ ...prev, offerAccepted: true }));
    setIsAccepting(false); // Stop loading state
    console.log(`Offer ${kfsOfferData._id} accepted.`);
    // --- End Simulation ---
  };


  const handleContractSigningNavigation = (offerId) => {
    console.log("Navigating to contract signing for offerId:", offerId);
    history.push(`/creditLineContract/${offerId}`);
  };

  const user = SharedCache.get("user") || {};
  const userId = user._id || user.id || "";
  const token = SharedCache.get("token") || "placeholdertoken";

  useEffect(() => {
    const fetchAllData = async () => {
      console.log("Starting fetchAllData function");
      setLoading(true);
      setError(null);
      // Reset states
      setCreditLine(null);
      setMerchantOffers([]);
      setLenderDetails({});
      setAcceptedLenderDetails(null);
      setInvoices([]);
      setInvoiceChartData([]);
      setDisbursalChartData([]);
      setPendingPayments([]);

      // Initialize dashboardMetrics with all keys defined to avoid undefined issues in UI
      setDashboardMetrics({
        approvedLimit: 0,
        presentOutstanding: 0,
        disbursalsPendingCount: 0,
        amountPendingApproval: 0,
        invoicesDisbursedCount: 0,
        invoicesThisMonthCount: 0,
        monthlyDisbursedAmount: 0, // For "Invoices Disbursed This Month" box
        nextDueDate: null,
        nextDueAmount: 0,
        totalInvoices: 0,
        invoicesDueCount: 0,
        invoiceApprovalsPendingCount: 0,
        // Old keys if still used elsewhere temporarily, ensure they are reset
        totalAmountFunded: 0,
        invoicesFunded: 0,
        outstandingPayments: 0,
        invoicesDue: 0,
        invoiceApprovalsPending: 0,
        disbursalsPending: 0,
        monthlyInvoicesDisbursed: 0,
      });

      let fetchedCreditLineData = null;
      let shouldFetchOffersForTable = false;
      let shouldFetchLenderDetailsForCreditLine = false;
      let creditLineLenderId = null;

      // Declare response variables at a higher scope
      let offersResponse = null;
      let invoicesFromApi = []; // Store raw invoices from /fetchInvoices
      let allFetchedOffersData = []; // Store raw offers from /offers

      try {
        if (!userId) {
          setError("User ID not found. Cannot fetch data.");
          setLoading(false);
          return;
        }

        // 1. Fetch Credit Line
        try {
          const creditLineRes = await axios.get(
            `${config.apiUrl}/ops/invoiceFinancing/fetchCreditLine/${userId}`,
            { headers: { 'x-auth-token': token } }
          );
          fetchedCreditLineData = creditLineRes.data;
          setCreditLine(fetchedCreditLineData);
          if (fetchedCreditLineData.lenderId) creditLineLenderId = fetchedCreditLineData.lenderId;
          const statusesForLenderDetails = ['REJECTED', 'ACTIVE', 'SUSPENDED', 'EXPIRED', 'APPROVED'];
          if (fetchedCreditLineData && statusesForLenderDetails.includes(fetchedCreditLineData.creditLineStatus)) {
            shouldFetchLenderDetailsForCreditLine = true;
          }
          if (fetchedCreditLineData && (fetchedCreditLineData.creditLineStatus === 'APPROVED' || fetchedCreditLineData.creditLineStatus === 'ACTIVE') && !fetchedCreditLineData.offerAccepted) {
            shouldFetchOffersForTable = true;
          } else if (fetchedCreditLineData && fetchedCreditLineData.creditLineStatus === 'UNDER_REVIEW') {
            shouldFetchOffersForTable = true;
          }
        } catch (err) {
          if (err.response && err.response.status === 404) {
            shouldFetchOffersForTable = true; // Might still be general offers
            setCreditLine({ creditLineStatus: 'NOT_FOUND', offerAccepted: false });
          } else console.error("Error fetching credit line:", err);
        }

        // 2. Fetch Lender Details for Credit Line
        if (shouldFetchLenderDetailsForCreditLine && creditLineLenderId) {
          try {
            const lenderData = await fetchLenderDetails(creditLineLenderId, token);
            if (lenderData) setAcceptedLenderDetails(lenderData);
          } catch (lenderErr) { console.error("Error fetching accepted lender details:", lenderErr); }
        }

        // 3. Fetch ALL Offers (for chart data AND credit line offer table)
        console.log("Fetching ALL offers...");
        try {
          offersResponse = await axios.get( // Assign to higher scoped variable
            `${config.apiUrl}/ops/invoiceFinancing/offers/${userId}`,
            { headers: { 'x-auth-token': token } }
          );
          if (offersResponse.data?.success && Array.isArray(offersResponse.data.offers)) {
            allFetchedOffersData = offersResponse.data.offers; // Store for metrics
            console.log(`Fetched ${allFetchedOffersData.length} total offers.`, JSON.stringify(allFetchedOffersData, null, 2));

            const chartDataOutput = processOffersForCharts(allFetchedOffersData);
            setInvoiceChartData(chartDataOutput.invoiceMonthlyData);
            setDisbursalChartData(chartDataOutput.pieChartDataForDisbursal);
            console.log("Processed Line Chart Data (from offers):", JSON.stringify(chartDataOutput.invoiceMonthlyData, null, 2));
            console.log("Processed Pie Chart Data (from offers):", JSON.stringify(chartDataOutput.pieChartDataForDisbursal, null, 2));

            if (shouldFetchOffersForTable) {
              const creditLineOffers = allFetchedOffersData.filter(offer => offer.offerType === 'creditLineOffer');
              setMerchantOffers(creditLineOffers);
              if (creditLineOffers.length > 0) {
                // ... (fetch lender details for creditLineOffers - existing logic)
              }
            }
          } else {
            console.log("No offers found or API call not successful for offers:", offersResponse?.data);
            setInvoiceChartData([]); setDisbursalChartData([]);
            if (shouldFetchOffersForTable) setMerchantOffers([]);
          }
        } catch (offersErr) {
          console.error("Error fetching offers:", offersErr);
          setInvoiceChartData([]); setDisbursalChartData([]);
          if (shouldFetchOffersForTable) setMerchantOffers([]);
        }

        // 4. Fetch general Invoices (from /fetchInvoices - for metrics and pending invoices table)
        console.log("Fetching general invoices data (for metrics and table)...");
        try {
          const generalInvoicesResponse = await axios.get( // Assign to higher scoped variable
            `${config.apiUrl}/ops/invoiceFinancing/fetchInvoices${userId ? `?userId=${userId}` : ''}`,
            { headers: { 'x-auth-token': token } }
          );
          if (generalInvoicesResponse.data && Array.isArray(generalInvoicesResponse.data)) {
            invoicesFromApi = generalInvoicesResponse.data.map(invoice => invoice._doc || invoice); // Store for metrics
            setInvoices(invoicesFromApi); // For "Pending Invoices" table
            console.log(`Fetched ${invoicesFromApi.length} general invoices.`);

            if (invoicesFromApi.length > 0) {
              const pending = invoicesFromApi
                .filter(inv => inv.status === 'APPLIED_FOR_DISCOUNTING' || inv.status === 'VERIFICATION_PENDING_ANCHOR')
                .map(inv => ({ /* ... mapping ... */ }));
              setPendingPayments(pending);
            }
          } else {
            console.error("Unexpected general invoices response format:", generalInvoicesResponse.data);
            setInvoices([]);
          }
        } catch (invoicesErr) {
          console.error("Error fetching general invoices:", invoicesErr);
          setInvoices([]);
        }

        // 5. Calculate and Set Dashboard Metrics (using both allFetchedOffersData and invoicesFromApi)
        // This is called ONCE after all data fetching attempts.
        const finalMetrics = calculateDashboardMetrics(invoicesFromApi, allFetchedOffersData, creditLine); // Pass creditLine too
        setDashboardMetrics(finalMetrics);
        console.log("Final Dashboard Metrics Set:", finalMetrics);

      } catch (err) {
        console.error("Critical error in fetchAllData's main try block:", err);
        setError("Failed to load essential dashboard data.");
      } finally {
        console.log("Completed fetchAllData function");
        setLoading(false);
      }
    };

    fetchAllData();
  }, [userId, token, location.state]);



  const processOffersForCharts = (offersData) => {
    const monthlyInvoiceCountDataMap = {}; // Key: "YYYY-MM", Value: { monthLabel: "Mon YYYY", count: N }
    let totalActuallyDisbursedFromOffers = 0;
    let totalOriginalValueOfDiscountedInvoices = 0;

    if (!Array.isArray(offersData)) {
      console.warn("processOffersForCharts: offersData is not an array, defaulting to empty.");
      offersData = [];
    }

    // console.log("Processing offers for charts. Number of offers received:", offersData.length);

    offersData.forEach(offer => {
      if (offer && offer.offerType === "invoiceDiscountingOffer") {
        // console.log("Processing invoiceDiscountingOffer:", JSON.stringify(offer, null, 2));
        let offerDateSource = offer.contractDetails?.invoiceDate || offer.createdAt;
        // console.log(`Offer ID ${offer._id}, Date Source: ${offerDateSource}`);

        if (offerDateSource) {
          try {
            const date = new Date(offerDateSource);
            if (!isNaN(date.getTime())) {
              const year = date.getFullYear();
              // Ensure month is 2 digits (e.g., "01" for January, "12" for December)
              const monthKey = (date.getMonth() + 1).toString().padStart(2, '0');
              const yearMonth = `${year}-${monthKey}`; // e.g., "2023-03"

              if (!monthlyInvoiceCountDataMap[yearMonth]) {
                monthlyInvoiceCountDataMap[yearMonth] = {
                  // Use toLocaleString for a more standard month label format like "Mar 2023"
                  monthLabel: date.toLocaleString('default', { month: 'short', year: 'numeric' }),
                  count: 0
                };
              }
              monthlyInvoiceCountDataMap[yearMonth].count++;
              // console.log(`Incremented count for ${yearMonth}. New count: ${monthlyInvoiceCountDataMap[yearMonth].count}`);
            } else {
              // console.warn(`Invalid date parsed for line chart: ${offerDateSource} (became ${date}) in offer ID ${offer._id}`);
            }
          } catch (e) {
            // console.warn(`Error processing date for line chart in offer ID ${offer._id}:`, e);
          }
        } // else {
        // console.warn(`No valid date source (contractDetails.invoiceDate or createdAt) for offer ID ${offer._id}`);
        // }

        // Aggregate Data for Pie Chart
        const originalInvoiceAmount = parseFloat(offer.contractDetails?.totalAmountParsed || 0);
        if (originalInvoiceAmount > 0) {
          totalOriginalValueOfDiscountedInvoices += originalInvoiceAmount;
        }
        if (offer.disbursementInfo?.disbursedAmount) {
          totalActuallyDisbursedFromOffers += parseFloat(offer.disbursementInfo.disbursedAmount);
        }
      }
    });

    // console.log("Final monthlyInvoiceCountDataMap for Line Chart (before fill):", JSON.stringify(monthlyInvoiceCountDataMap, null, 2));

    // --- Helper to prepare monthly data for line chart (last 6 calendar months) ---
    // This helper replaces the previous complex `fillMissingMonthsForLine`
    const fillMissingMonthsForLineChart = (dataMap, valueKey) => {
      const today = new Date(); // Use current date as reference
      const desiredDataPoints = 6;
      const finalChartData = [];

      for (let i = desiredDataPoints - 1; i >= 0; i--) {
        // Create a date cursor for each of the last 6 months
        // Handles month/year rollovers correctly
        const dateCursor = new Date(today.getFullYear(), today.getMonth() - i, 1);

        const year = dateCursor.getFullYear();
        const month = (dateCursor.getMonth() + 1).toString().padStart(2, '0'); // Key for dataMap "YYYY-MM"
        const yearMonthKey = `${year}-${month}`;

        // Label for the chart X-axis, e.g., "May 2025"
        const monthLabel = dateCursor.toLocaleString('default', { month: 'short', year: 'numeric' });

        let value = 0; // Default to 0
        // Check if dataMap has an entry for this yearMonthKey and count is a number
        if (dataMap[yearMonthKey] && typeof dataMap[yearMonthKey].count === 'number') {
          value = dataMap[yearMonthKey].count;
        }
        finalChartData.push({ name: monthLabel, [valueKey]: value });
      }
      return finalChartData;
    };

    const invoiceMonthlyData = fillMissingMonthsForLineChart(monthlyInvoiceCountDataMap, 'Invoices');
    // console.log("Data for Line Chart (invoiceMonthlyData) after fillMissingMonths:", JSON.stringify(invoiceMonthlyData, null, 2));

    // --- Prepare Pie Chart Data ---
    const pieData = [];
    const balanceAmount = Math.max(0, totalOriginalValueOfDiscountedInvoices - totalActuallyDisbursedFromOffers);
    if (totalActuallyDisbursedFromOffers > 0 || totalOriginalValueOfDiscountedInvoices > 0) {
      pieData.push({ name: 'Disbursed', value: totalActuallyDisbursedFromOffers });
      pieData.push({ name: 'Invoiced', value: balanceAmount });
    }

    return {
      invoiceMonthlyData,
      pieChartDataForDisbursal: pieData,
    };
  };

  const calculateDashboardMetrics = (generalInvoicesInput, offersDataInput, currentCreditLine) => {
    const generalInvoices = Array.isArray(generalInvoicesInput) ? generalInvoicesInput : [];
    const offersData = Array.isArray(offersDataInput) ? offersDataInput : [];

    console.log("Calculating Dashboard Metrics with generalInvoices count:", generalInvoices.length, "and offersData count:", offersData.length);

    const now = new Date();
    const todayDateStr = now.toISOString().split('T')[0];
    const currentCalendarMonth = now.getMonth();
    const currentCalendarYear = now.getFullYear();

    let metrics = {
      approvedLimit: currentCreditLine?.creditLimit || 0,
      presentOutstanding: 0,
      disbursalsPendingCount: 0,
      amountPendingApproval: 0,
      invoicesDisbursedCount: 0,
      invoicesThisMonthCount: 0,
      monthlyDisbursedAmount: 0,
      nextDueDate: null,
      nextDueAmount: 0,
      totalInvoices: generalInvoices.length,
      invoicesDueCount: 0,
      invoiceApprovalsPendingCount: 0,
      totalAmountFunded: 0, // This will be correctly calculated now
      invoicesFunded: 0,
      outstandingPayments: 0,
      invoicesDue: 0,
      invoiceApprovalsPending: 0,
      disbursalsPending: 0,
      monthlyInvoicesDisbursed: 0,
    };

    const disbursedOrActiveOfferInvoiceIds = new Set();
    let allUpcomingPendingEmis = [];
    let localTotalActuallyDisbursedFromOffers = 0; // Initialize a local variable here

    offersData.forEach(offer => {
      if (offer.offerType === "invoiceDiscountingOffer") {
        if (offer.invoiceId && (offer.status === 'LOAN_IN_PROGRESS' || offer.status === 'PAID' || (offer.disbursementInfo && offer.disbursementInfo.disbursedAmount > 0))) {
          disbursedOrActiveOfferInvoiceIds.add(offer.invoiceId);
        }

        if (offer.status === 'LOAN_IN_PROGRESS' && Array.isArray(offer.emiDetails)) {
          let currentLoanPrincipalOutstanding = 0;
          for (let i = offer.emiDetails.length - 1; i >= 0; i--) {
            const emi = offer.emiDetails[i];
            if (emi.rePaymentStatus === 'PENDING' || emi.rePaymentStatus === 'OVERDUE') {
              currentLoanPrincipalOutstanding = parseFloat(emi.principalOutstanding || 0) + parseFloat(emi.principalRecovered || 0);
              break;
            } else if (emi.rePaymentStatus === 'PAID' && i === offer.emiDetails.length - 1) {
              currentLoanPrincipalOutstanding = 0;
              break;
            }
          }
          metrics.presentOutstanding += currentLoanPrincipalOutstanding;
        }

        if (offer.status === 'LOAN_IN_PROGRESS' && Array.isArray(offer.emiDetails)) {
          offer.emiDetails.forEach(emi => {
            if (emi.rePaymentStatus === 'PENDING' && emi.rePaymentDate) {
              const emiDueDate = new Date(emi.rePaymentDate);
              if (emiDueDate >= now) {
                allUpcomingPendingEmis.push({
                  date: emiDueDate,
                  amount: parseFloat(emi.rePaymentAmount || 0),
                });
              }
            }
          });
        }

        if (offer.status === 'READY_FOR_DISBURSAL' || offer.status === 'INITIATED_FUND_TRANSFER') {
          metrics.disbursalsPendingCount++;
        }

        // Calculate Monthly Disbursed Amount
        if (offer.disbursementInfo?.disbursedAmount && offer.disbursementInfo?.disbursedOn) {
          try {
            const disbursedDate = new Date(offer.disbursementInfo.disbursedOn);
            if (disbursedDate.getFullYear() === currentCalendarYear && disbursedDate.getMonth() === currentCalendarMonth) {
              metrics.monthlyDisbursedAmount += parseFloat(offer.disbursementInfo.disbursedAmount);
            }
          } catch (e) { console.warn("Error parsing disbursal date from offer: ", offer.disbursementInfo.disbursedOn); }
        }

        // Calculate Total Actually Disbursed Amount from Offers
        if (offer.disbursementInfo?.disbursedAmount) { // Add this block
          localTotalActuallyDisbursedFromOffers += parseFloat(offer.disbursementInfo.disbursedAmount);
        }
      }
    });

    metrics.invoicesDisbursedCount = disbursedOrActiveOfferInvoiceIds.size;

    if (allUpcomingPendingEmis.length > 0) {
      allUpcomingPendingEmis.sort((a, b) => a.date - b.date);
      metrics.nextDueDate = allUpcomingPendingEmis[0].date.toISOString().split('T')[0];
      metrics.nextDueAmount = allUpcomingPendingEmis[0].amount;
    }

    generalInvoices.forEach(invoice => {
      if (invoice.status && (invoice.status.startsWith('VERIFICATION_PENDING_') || invoice.status.startsWith('MORE_INFO_NEEDED_'))) {
        metrics.amountPendingApproval += parseFloat(invoice.totalAmount || 0);
      }
      if (invoice.status === 'VERIFICATION_PENDING_ANCHOR' || invoice.status === 'VERIFICATION_PENDING_LENDER') {
        metrics.invoiceApprovalsPendingCount++;
      }
      if (invoice.dueDate && new Date(invoice.dueDate).toISOString().split('T')[0] < todayDateStr) {
        const isHandledByOffer = offersData.some(offer =>
          offer.invoiceId === invoice._id &&
          offer.offerType === "invoiceDiscountingOffer" &&
          (offer.status === 'LOAN_IN_PROGRESS' || offer.status === 'PAID' || (offer.disbursementInfo && offer.disbursementInfo.disbursedAmount > 0))
        );
        const isInvoiceDirectlySettled = ['PAID', 'DISBURSED', 'WRITTEN_OFF_PAID', 'LOAN_CANCELLED'].includes(invoice.status);
        if (!isHandledByOffer && !isInvoiceDirectlySettled) {
          metrics.invoicesDueCount++;
        }
      }
      if (invoice.invoiceDate) {
        try {
          const invDate = new Date(invoice.invoiceDate);
          if (invDate.getFullYear() === currentCalendarYear && invDate.getMonth() === currentCalendarMonth) {
            metrics.invoicesThisMonthCount++;
          }
        } catch (e) { /* ignore invalid dates */ }
      }
    });

    // Use the locally calculated sum for totalAmountFunded
    metrics.totalAmountFunded = localTotalActuallyDisbursedFromOffers;
    metrics.invoicesFunded = metrics.invoicesDisbursedCount;
    metrics.outstandingPayments = metrics.presentOutstanding;
    metrics.invoicesDue = metrics.invoicesDueCount;
    metrics.invoiceApprovalsPending = metrics.invoiceApprovalsPendingCount;
    // Assuming dashboardMetrics.disbursalsPending is supposed to be a count from your setup
    // If it's meant to be an amount, you'll need to sum offer.disbursementInfo.disbursedAmount where status is 'READY_FOR_DISBURSAL' etc.
    // For now, assuming your earlier logic for disbursalsPendingCount is what populates the QAR box, which seems like a mismatch.
    // If 'Disbursals Pending' box should show an amount, you'll need to sum that up similarly to totalAmountFunded but for pending offers.
    // However, your UI for "Disbursals Pending" box shows formatCurrency(dashboardMetrics.disbursalsPending)
    // and you set dashboardMetrics.disbursalsPending = metrics.disbursalsPendingCount.
    // Let's assume for now the box 'Disbursals Pending' should show count and you'll adjust the UI text later if it's amount.
    // Or, if it's amount, you should sum it from offersData.
    // For clarity, if it's an amount:
    let pendingDisbursalAmount = 0;
    offersData.forEach(offer => {
      if (offer.offerType === "invoiceDiscountingOffer" && (offer.status === 'READY_FOR_DISBURSAL' || offer.status === 'INITIATED_FUND_TRANSFER')) {
        pendingDisbursalAmount += parseFloat(offer.contractDetails?.financedAmount || offer.disbursementInfo?.disbursedAmount || 0); // or another relevant amount field
      }
    });
    metrics.disbursalsPending = pendingDisbursalAmount; // If the box is for amount
    // If the box is for COUNT, then your original metrics.disbursalsPending = metrics.disbursalsPendingCount; is correct.
    // Given your UI shows QAR for 'Disbursals Pending', I'll use the pendingDisbursalAmount:

    metrics.monthlyInvoicesDisbursed = metrics.monthlyDisbursedAmount;

    console.log("Final calculated metrics:", metrics);
    return metrics;
  };

  // --- Chart Data Processing Function ---
  const processInvoiceDataForCharts = (invoicesData) => {
    const monthlyData = {}; // { 'YYYY-MM': { count: 0, disbursedAmount: 0 } }

    invoicesData.forEach(invoice => {
      if (!invoice.invoiceDate) return; // Skip if no date

      try {
        const date = new Date(invoice.invoiceDate);
        // Check if date is valid before proceeding
        if (isNaN(date.getTime())) {
          console.warn(`Invalid invoiceDate found: ${invoice.invoiceDate} for invoice ${invoice._id}`);
          return; // Skip this invoice
        }
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 01, 02, ... 12
        const yearMonth = `${year}-${month}`;

        if (!monthlyData[yearMonth]) {
          monthlyData[yearMonth] = { monthLabel: `${date.toLocaleString('default', { month: 'short' })} ${year}`, count: 0, disbursedAmount: 0 };
        }

        // Increment invoice count for the month
        monthlyData[yearMonth].count++;

        // Add to disbursed amount if status matches
        if (['DISBURSED', 'ACTIVE'].includes(invoice.status)) { // Adjust statuses as needed
          monthlyData[yearMonth].disbursedAmount += parseFloat(invoice.totalAmount || 0);
        }
      } catch (dateError) {
        console.error(`Error processing date ${invoice.invoiceDate} for invoice ${invoice._id}:`, dateError);
        // Continue processing other invoices
      }
    });

    // Convert to array and sort chronologically
    const sortedMonths = Object.keys(monthlyData).sort();
    const invoiceMonthlyData = sortedMonths.map(monthKey => ({
      name: monthlyData[monthKey].monthLabel, // e.g., "Apr 2025"
      Invoices: monthlyData[monthKey].count,
    }));
    const disbursalMonthlyData = sortedMonths.map(monthKey => ({
      name: monthlyData[monthKey].monthLabel,
      Disbursed: monthlyData[monthKey].disbursedAmount,
    }));

    // Ensure at least 6 months of data points, filling missing ones with 0
    const fillMissingMonths = (data, valueKey) => {
      if (data.length >= 6) return data.slice(-6); // Take last 6 if enough data

      const filledData = [];
      const lastDate = data.length > 0 ? new Date(data[data.length - 1].name.split(' ')[1], ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'].indexOf(data[data.length - 1].name.split(' ')[0])) : new Date();
      lastDate.setMonth(lastDate.getMonth() + 1); // Start from next month for filling backwards if needed

      for (let i = 0; i < 6; i++) {
        const targetDate = new Date(lastDate);
        targetDate.setMonth(lastDate.getMonth() - (6 - 1 - i)); // Calculate past month dates
        const year = targetDate.getFullYear();
        const monthLabel = `${targetDate.toLocaleString('default', { month: 'short' })} ${year}`;

        const existing = data.find(d => d.name === monthLabel);
        if (existing) {
          filledData.push(existing);
        } else {
          filledData.push({ name: monthLabel, [valueKey]: 0 });
        }
      }
      return filledData;
    };


    return {
      invoiceMonthlyData: fillMissingMonths(invoiceMonthlyData, 'Invoices'),
      disbursalMonthlyData: fillMissingMonths(disbursalMonthlyData, 'Disbursed')
    };
  };


  // --- Other Helper Functions (Keep existing) ---
  const calculateUtilizationPercentage = () => {
    // (Keep existing logic)
    if (!creditLine || !creditLine.creditLimit || creditLine.creditLimit === 0) {
      return 0;
    }
    const utilized = creditLine.utilizedAmount ?? (creditLine.creditLimit - creditLine.availableBalance);
    return Math.max(0, Math.min(100, (utilized / creditLine.creditLimit) * 100)); // Clamp between 0-100
  };

  const formatCurrency = (amount) => {
    // (Keep existing logic)
    const num = Number(amount);
    return !isNaN(num) ? `${num.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}` : '0.00'; // Added formatting
  };

  const shouldShowCreditLineDetails = () => {
    console.log("CREIT LINE HERE", creditLine);
    return creditLine && creditLine.creditLineStatus === 'ACTIVE'; // Corrected return condition
  };

  const shouldShowOfferTable = () => {
    // (Keep existing logic)
    return creditLine &&
      (creditLine.creditLineStatus === 'UNDER_REVIEW' ||
        ((creditLine.creditLineStatus === 'APPROVED' || creditLine.creditLineStatus === 'ACTIVE') && !creditLine.offerAccepted) ||
        creditLine.creditLineStatus === 'NOT_FOUND'
      ) &&
      merchantOffers.length > 0;
  };

  // --- Render Logic ---

  if (loading && activeTab === 'overview') {
    // (Keep existing loading spinner)
    return <LoadingIndicator />
  }
  const PIE_CHART_COLORS = ['#97e9c6', '#27c686'];

  const utilizationPercent = calculateUtilizationPercentage();
  const availablePercent = Math.max(0, Math.min(100, 100 - utilizationPercent));

  const getCreditLineStatusColor = (status) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-[#3ac387] text-white font-bold';
      case 'APPROVED':
        return 'bg-blue-100 text-blue-800 font-bold';
      case 'UNDER_REVIEW':
        return 'bg-yellow-100 text-yellow-800 font-bold';
      case 'REJECTED':
        return 'bg-red-100 text-red-800 font-bold';
      case 'SUSPENDED':
      case 'ON_HOLD':
        return 'bg-purple-100 text-purple-800 font-bold';
      case 'EXPIRED':
        return 'bg-gray-100 text-gray-800 font-bold';
      default:
        return 'bg-gray-100 text-gray-800 font-bold';
    }
  };

  return (
    <>
      <div className='flex flex-col h-full'> {/* Removed overflow-hidden */}
        {/* Header / Top Row */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between p-4 md:px-6 border-b border-gray-200"> {/* Removed bg-white, Adjusted for mobile */}
          <h1 className="text-xl font-bold text-gray-800 mb-3 sm:mb-0">Welcome, {SharedCache.get("user")?.firstName}</h1> {/* Added margin-bottom for mobile */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-3 w-full sm:w-auto"> {/* Adjusted for mobile */}
            <button
              onClick={() => {
                history.push('/dashboard');
                setCurrentPage('my-invoices');
              }}
              className="flex items-center px-4 py-2 bg-[#139d54] text-white text-sm font-medium rounded-md hover:bg-[#1a6e2f] transition-colors w-full sm:w-auto" // Full width on mobile
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
              </svg>
              Add Invoice
            </button>

            {/* Export Dropdown */}
            <div className="relative w-full sm:w-auto"> {/* Full width on mobile */}
              <button
                onClick={() => setIsExportDropdownOpen(!isExportDropdownOpen)}
                className="flex items-center justify-between px-4 py-2 bg-white border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-full" // Full width on mobile
              >
                Export
                <svg className={`ml-2 h-4 w-4 transform transition-transform ${isExportDropdownOpen ? 'rotate-180' : 'rotate-0'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path></svg>
              </button>
              {isExportDropdownOpen && (
                <div className="absolute right-0 mt-2 w-full sm:w-40 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10"> {/* Full width on mobile */}
                  <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                    <button
                      onClick={() => { /* Implement CSV export logic here */ setIsExportDropdownOpen(false); }}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      role="menuitem"
                    >
                      Export as CSV
                    </button>
                    <button
                      onClick={() => { /* Implement Excel export logic here */ setIsExportDropdownOpen(false); }}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      role="menuitem"
                    >
                      Export as Excel
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Sort Dropdown */}
            <div className="relative w-full sm:w-auto"> {/* Full width on mobile */}
              <button
                onClick={() => setIsSortDropdownOpen(!isSortDropdownOpen)}
                className="flex items-center justify-between px-4 py-2 bg-white border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-full" // Full width on mobile
              >
                This Year
                <svg className={`ml-2 h-4 w-4 transform transition-transform ${isSortDropdownOpen ? 'rotate-180' : 'rotate-0'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path></svg>
              </button>
              {isSortDropdownOpen && (
                <div className="absolute right-0 mt-2 w-full sm:w-40 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10"> {/* Full width on mobile */}
                  <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                    <button
                      onClick={() => { /* Implement "This Year" sorting logic */ setIsSortDropdownOpen(false); }}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      role="menuitem"
                    >
                      This Year
                    </button>
                    <button
                      onClick={() => { /* Implement "This Month" sorting logic */ setIsSortDropdownOpen(false); }}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      role="menuitem"
                    >
                      This Month
                    </button>
                    <button
                      onClick={() => { /* Implement "This Week" sorting logic */ setIsSortDropdownOpen(false); }}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      role="menuitem"
                    >
                      This Week
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className='flex-grow px-4 md:px-6 py-6'> {/* Removed overflow-y-auto and maxHeight */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-2.5 text-lg rounded-md mb-4">
                  {error}
                </div>
              )}

              {/* --- NEW: 2nd Row - 6 Metric Boxes --- */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 w-full">
                {[
                  {
                    title: "Approved Limit",
                    value: formatCurrency(creditLine?.creditLimit),
                    img: require("../../images/approved-limit.png"),
                  },
                  {
                    title: "Present Outstanding",
                    value: formatCurrency(dashboardMetrics.outstandingPayments),
                    img: require("../../images/outstanding.png"),
                  },
                  {
                    title: "Disbursals Pending",
                    value: formatCurrency(dashboardMetrics.disbursalsPending),
                    img: require("../../images/disbursals-pending.png"),
                  },
                  {
                    title: "Amount Pending for Approval",
                    value: formatCurrency(dashboardMetrics.amountPendingForApproval),
                    img: require("../../images/amount-pending.png"),
                  },
                  {
                    title: "Invoices Disbursed This Month",
                    value: formatCurrency(dashboardMetrics.monthlyInvoicesDisbursed),
                    img: require("../../images/invoices-disbursed.png"),
                  },
                  {
                    title: "Next Due Amount",
                    value: formatCurrency(dashboardMetrics.nextDueAmount),
                    img: require("../../images/next-due-amount.png"),
                  },
                ].map(({ title, value, img }) => (
                  <div
                    key={title}
                    className="bg-white p-4 rounded-lg shadow flex flex-col justify-between h-28 overflow-hidden"
                  >
                    <div className="flex items-center mb-1">
                      <img src={img} alt={title} className="h-6 w-6 mr-2 shrink-0" />
                      <h3 className="text-gray-700 text-xs font-medium uppercase leading-tight break-words">
                        {title}
                      </h3>
                    </div>
                    <div className="text-gray-900 font-bold text-xl sm:text-xl leading-snug whitespace-normal break-words">
                      {value}
                      <span className="text-base sm:text-lg font-normal text-gray-600 ml-1">QAR</span>
                    </div>
                  </div>
                ))}
              </div>

              {/* --- NEW: 3rd Row Layout (Left and Right Columns) --- */}
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                {/* Left Column (Approx 25%) */}
                <div className="lg:col-span-1 space-y-6">
                  {/* Credit Line Overview - Droppable Section */}
                  {shouldShowCreditLineDetails() && creditLine && (
                    <div className="bg-white rounded-lg shadow-lg p-6">
                      {/* Always visible part */}
                      <div className="flex justify-between items-center mb-4 md:cursor-default cursor-pointer" onClick={(e) => window.innerWidth < 768 && setIsCreditLineOverviewOpen(!isCreditLineOverviewOpen)}>
                        <h2 className="text-md font-semibold text-gray-800">Credit Line Overview</h2>
                        <div className="flex items-center">
                          <span className={`px-3 py-1 rounded-md text-xs font-medium ${getCreditLineStatusColor(creditLine.creditLineStatus)}`}>
                            {creditLine.creditLineStatus.replace(/_/g, ' ')}
                          </span>
                          {/* SVG button - only visible on mobile (md and below) */}
                          <svg className={`ml-2 h-4 w-4 transform transition-transform duration-300 md:hidden ${isCreditLineOverviewOpen ? 'rotate-180' : 'rotate-0'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                          </svg>
                        </div>
                      </div>
                      {/* Interest Rate, Tenure, Processing Fee Box (Always visible as per request) */}
                      <div className="border border-gray-300 bg-gray-50 rounded-lg p-4 mb-4">
                        <div className="grid grid-cols-3 gap-4">
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Interest Rate</p>
                            <p className="font-semibold text-gray-800">{`${creditLine.interestRate ?? 'N/A'}%`}</p>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Tenure</p>
                            <p className="font-semibold text-gray-800">{`${creditLine.tenure ?? 'N/A'} Days`}</p>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Processing Fee</p>
                            <p className="font-semibold text-gray-800">
                              {creditLine.processingFeeType === 'percentage'
                                ? `${creditLine.processingFee}%`
                                : `${formatCurrency(creditLine.processingFee || 0)} QAR`}
                            </p>
                          </div>
                        </div>
                      </div>
                      {/* Collapsible content */}
                      <div className={`overflow-hidden transition-all duration-300 ease-in-out md:max-h-none md:opacity-100 md:pt-4 ${isCreditLineOverviewOpen ? 'max-h-screen opacity-100 pt-4' : 'max-h-0 opacity-0'}`}>
                        {/* Utilization Bar */}
                        <div className="mb-4">
                          <div className="flex justify-between items-center mb-1">
                            <span className="text-xs text-gray-600">Credit Available</span> {/* Changed Label */}
                            <span className="text-xs font-medium">{`${availablePercent.toFixed(0)}% Available`}</span> {/* Use availablePercent */}
                          </div>
                          {/* Track for the bar - light red indicating used potential or just a neutral gray */}
                          <div className="h-3 bg-red-100 rounded-full overflow-hidden w-full"> {/* Can use bg-gray-200 for neutral track */}
                            {/* The green "available" portion */}
                            <div
                              style={{ width: `${availablePercent}%` }}
                              className="h-full bg-green-500 rounded-full transition-all duration-500" // Green for available
                              role="progressbar"
                              aria-valuenow={availablePercent}
                              aria-valuemin="0"
                              aria-valuemax="100"
                            >
                            </div>
                          </div>
                          <p className="text-[10px] text-gray-500 mt-1 text-right">
                            {formatCurrency(creditLine?.availableBalance || (creditLine?.creditLimit || 0) * (availablePercent / 100))} QAR Available
                            {/* Display availableBalance if present, otherwise calculate from percent */}
                          </p>
                        </div>
                        {/* Divider Line */}
                        <hr className="border-t border-gray-200 my-4" />
                        {/* Bank Info & View Contract */}
                        {acceptedLenderDetails && (
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex items-center">
                              {acceptedLenderDetails.logoUrl ? (
                                <img src={acceptedLenderDetails.logoUrl} alt={`${acceptedLenderDetails.lenderName} logo`} className="h-10 w-10 object-contain rounded-full mr-3" />
                              ) : (
                                <div className="h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center text-gray-500 text-xs mr-3">No Logo</div>
                              )}
                              <div>
                                <p className="font-semibold text-xs text-gray-900">{acceptedLenderDetails.lenderName || 'N/A'}</p>
                                <p className="text-xs text-gray-600">Bank</p>
                              </div>
                            </div>
                            <button onClick={handleOpenModal} className="px-2 py-1 bg-[#00393b] text-white text-xs font-medium rounded-md hover:bg-[#002a2c] transition-colors">View Contract</button>
                          </div>
                        )}
                        {/* Contact Person Box */}
                        {acceptedLenderDetails && (
                          <div className="bg-gray-50 border border-gray-300 rounded-lg p-2">
                            <p className="text-md text-gray-500">Contact Person</p>
                            <p className="font-semibold text-gray-800 text-sm">{acceptedLenderDetails.contactPersonName || 'John Doe'}</p>
                            <p className="font-semibold text-gray-800 text-sm">{acceptedLenderDetails.contactPersonEmail || '<EMAIL>'}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  {/* Status messages for non-active credit line */}
                  {!shouldShowCreditLineDetails() && (
                    <div className="bg-white rounded-lg shadow-lg p-4 text-center text-gray-600">
                      <p className="text-md font-semibold mb-2">Credit Line Status</p>
                      {(creditLine?.creditLineStatus === 'UNDER_REVIEW') && <p className="text-sm">Your credit line application is currently under review.</p>}
                      {creditLine?.creditLineStatus === 'NOT_FOUND' && <p className="text-sm">You do not have an active credit line yet.</p>}
                      {creditLine && creditLine.creditLineStatus === 'APPROVED' && !creditLine.offerAccepted && <p className="text-sm">Congratulations! Your credit line application has been approved.</p>}
                      {merchantOffers.length > 0 && !creditLine?.offerAccepted && (
                        <p className="text-sm mt-2">Check 'Available Offers' to proceed.</p>
                      )}
                      {!loading && merchantOffers.length === 0 && !shouldShowOfferTable() && !creditLine?.offerAccepted && (
                        <p className="text-sm mt-2 italic">No credit line offers currently available for you.</p>
                      )}
                    </div>
                  )}


                  {/* Instant Cash from Invoices Card */}
                  <div className="bg-[#27c686] text-white rounded-lg shadow p-4 md:p-6">
                    <div className="flex items-center justify-center md:justify-start mb-3 md:mb-4 md:pl-4">
                      {/* You'll need to define `briefcaseMoney` image path or import */}
                      <img src={require("../../images/briefcase_money.png")} alt="Briefcase with money" className="h-24 md:h-32 w-auto" />
                    </div>
                    <h2 className="text-lg md:text-xl font-bold mb-2 text-center md:text-left">Instant cash from your invoices</h2>
                    <p className="text-sm text-center md:text-left">
                      Turn your unpaid invoices into immediate working capital. Access funds in a few clicks—no waiting, no hassle.
                    </p>
                  </div>
                </div>

                {/* Right Column (Approx 75%) */}
                <div className="lg:col-span-3 space-y-6">
                  {/* Top 4 Metric Boxes */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    {/* Total Invoices */}
                    <div className="bg-[#e1f2f5] p-4 rounded-lg shadow flex flex-col justify-between h-28">
                      <h3 className="text-gray-700 text-xs font-medium uppercase">Total Invoices</h3>
                      <p className="text-4xl font-bold text-gray-900">{invoices.length}</p>
                    </div>
                    {/* Invoices Due */}
                    <div className="bg-white p-4 rounded-lg shadow flex flex-col justify-between h-28">
                      <h3 className="text-gray-700 text-xs font-medium uppercase">Invoices Due</h3>
                      <p className="text-4xl font-bold text-gray-900">{dashboardMetrics.invoicesDue}</p>
                    </div>
                    {/* Invoices Approval Pending */}
                    <div className="bg-white p-4 rounded-lg shadow flex flex-col justify-between h-28">
                      <h3 className="text-gray-700 text-xs font-medium uppercase">Invoices Approval Pending</h3>
                      <p className="text-4xl font-bold text-gray-900">{dashboardMetrics.invoiceApprovalsPending}</p>
                    </div>
                    {/* Next Due Date */}
                    <div className="bg-white p-4 rounded-lg shadow flex flex-col justify-between h-28">
                      <h3 className="text-gray-700 text-xs font-medium uppercase">Next Due Date</h3>
                      <p className="text-4xl font-bold text-gray-900">{dashboardMetrics.nextDueDate ? new Date(dashboardMetrics.nextDueDate).toLocaleDateString('en-US', { day: '2-digit', month: 'short' }) : '-'}</p>
                    </div>
                  </div>

                  {/* Charts Section */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

                    <div className="bg-white p-6 rounded-lg shadow">
                      <h3 className="text-gray-700 text-sm font-medium uppercase mb-4">Invoices Discounted</h3>
                      {invoiceChartData && invoiceChartData.length > 0 && invoiceChartData.some(d => d.Invoices > 0) ? (
                        <ResponsiveContainer width="100%" height={250}>
                          <LineChart data={invoiceChartData} margin={{ top: 5, right: 30, left: 0, bottom: 5 }}>
                            <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
                            <XAxis dataKey="name" tick={{ fontSize: 10 }} />
                            <YAxis tick={{ fontSize: 10 }} allowDecimals={false} />
                            <Tooltip formatter={(value, name) => [value, "Invoices"]} />
                            <Legend wrapperStyle={{ fontSize: "12px" }} />
                            <Line
                              type="monotone"
                              dataKey="Invoices"
                              stroke="#007bff"
                              strokeWidth={2}
                              activeDot={{ r: 6, strokeWidth: 0, fill: '#007bff' }}
                              dot={{ stroke: '#007bff', strokeWidth: 1, r: 3, fill: '#fff' }}
                              name="No. of Invoices Discounted"
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      ) : (
                        <div style={{ width: '100%', height: 250, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f9fafb', borderRadius: '0.5rem' }}>
                          <img src={require("../../images/sailor.png")} alt="No data" className="h-20 w-auto mb-2" />
                          <p className="text-gray-500 text-sm">No discounted invoice data to display.</p>
                        </div>
                      )}
                    </div>

                    {/* Disbursal on this platform (Pie Chart) */}
                    <div className="bg-white p-6 rounded-lg shadow">
                      <h3 className="text-gray-700 text-sm font-medium uppercase mb-4">Disbursal on this platform (QAR)</h3>
                      {disbursalChartData && disbursalChartData.length > 0 && disbursalChartData.some(item => item.value > 0) ? (
                        <ResponsiveContainer width="100%" height={250}>
                          <PieChart>
                            <Pie
                              data={disbursalChartData}
                              cx="50%"
                              cy="50%"
                              labelLine={false}
                              label={({ name, value, percent }) => value > 0 ? `${formatCurrency(value)} (${(percent * 100).toFixed(0)}%)\n${name}` : null}
                              outerRadius={85}
                              fill="#8884d8"
                              dataKey="value"
                              nameKey="name"
                              paddingAngle={1}
                            >
                              {disbursalChartData.map((entry, index) => {
                                const colors = ['#27AE60', '#98e9c8']; // Disbursed, Balance
                                return <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />;
                              })}
                            </Pie>
                            <Tooltip formatter={(value) => `${formatCurrency(value)} QAR`} />
                          </PieChart>
                        </ResponsiveContainer>
                      ) : (
                        <div style={{ width: '100%', height: 250, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f9fafb', borderRadius: '0.5rem' }}>
                          <img src={require("../../images/sailor.png")} alt="No data" className="h-20 w-auto mb-2" />
                          <p className="text-gray-500 text-sm">No disbursal data to display.</p>
                        </div>
                      )}
                    </div>
                  </div>
                  {/* Pending Invoices Section (Re-styled) */}
                  <div className="bg-white p-6 rounded-lg shadow">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-semibold text-gray-800">Pending Invoices</h3>
                      <button className="text-sm text-blue-600 hover:underline">View All</button> {/* Add link to view all */}
                    </div>
                    {pendingPayments.length === 0 ? (
                      <p className="text-gray-500 italic text-center py-4">No pending invoice payments.</p>
                    ) : (
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice ID</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice Date</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Age</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice Amount</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {invoices
                              .filter(inv => inv.status === 'VERIFICATION_PENDING_ANCHOR' || inv.status === 'VERIFICATION_PENDING_LENDER')
                              .slice(0, 5)
                              .map((invoice) => (
                                <tr key={invoice._id}>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{invoice.invoiceNumber || invoice._id.slice(-6)}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{invoice.invoiceDate ? new Date(invoice.invoiceDate).toLocaleDateString('en-US', { day: '2-digit', month: 'short', year: 'numeric' }) : 'N/A'}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {invoice.invoiceDate ? `${Math.floor((new Date() - new Date(invoice.invoiceDate)) / (1000 * 60 * 60 * 24))} days ago` : 'N/A'}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{invoice.customerName || 'Sunkey Mills Pvt. Ltd.'}</td> {/* Placeholder customer */}
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(invoice.totalAmount)}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${invoice.status === 'VERIFIED' || invoice.status === 'ACTIVE' ? 'bg-green-100 text-green-800' : invoice.status === 'VERIFICATION_PENDING' ? 'bg-yellow-100 text-yellow-800' : invoice.status === 'APPLIED_FOR_DISCOUNTING' ? 'bg-blue-100 text-blue-800' : invoice.status === 'REJECTED' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'}`}>
                                      {invoice.status && typeof invoice.status === 'string' ? invoice.status.replace(/_/g, ' ') : 'UNKNOWN'}
                                    </span>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button className="text-gray-500 hover:text-gray-900 focus:outline-none">
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                                      </svg>
                                    </button>
                                  </td>
                                </tr>
                              ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* KFS Modal (Keep as is, no changes needed for this UI) */}
      {isKfsModalOpen && kfsOfferData && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60 backdrop-blur-sm" onClick={handleCloseKfsModal} role="dialog" aria-modal="true">
          <div className="bg-white rounded-lg shadow-xl p-0 max-w-lg w-full mx-4 my-8 overflow-hidden flex flex-col" onClick={(e) => e.stopPropagation()}>
            <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200">
              <h2 id="kfsModalTitle" className="text-xl font-semibold text-gray-800">Key Fact Statement</h2>
              <button onClick={handleCloseKfsModal} className="text-gray-400 hover:text-gray-700 text-3xl leading-none font-semibold focus:outline-none" aria-label="Close modal" disabled={isAccepting}>&times;</button>
            </div>
            <div className="px-6 py-5 space-y-5">
              <div className="flex items-center space-x-4 pb-4 border-b">
                {kfsOfferData.lender?.logoUrl ? <img className="h-12 w-12 rounded-md object-contain flex-shrink-0" src={kfsOfferData.lender.logoUrl} alt={`${kfsOfferData.lender.lenderName} Logo`} /> : <div className="h-12 w-12 bg-gray-200 rounded-md flex items-center justify-center text-gray-500 text-xs flex-shrink-0">No Logo</div>}
                <div>
                  <p className="text-lg font-semibold text-gray-900">{kfsOfferData.lender?.lenderName || 'Lender'}</p>
                  <p className="text-sm text-gray-500">{kfsOfferData.lender?.lenderType || 'Financial Institution'}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-x-6 gap-y-4">
                <div><p className="text-sm text-gray-500 mb-0.5">Credit Limit</p><p className="text-md font-semibold text-gray-800">QAR {formatCurrency(kfsOfferData.creditLimit)}</p></div>
                <div><p className="text-sm text-gray-500 mb-0.5">Annual Percentage Rate (APR)</p><p className="text-md font-semibold text-gray-800">{kfsOfferData.interestRate}%</p></div>
                <div><p className="text-sm text-gray-500 mb-0.5">Tenure</p><p className="text-md font-semibold text-gray-800">{kfsOfferData.tenureDays} days</p></div>
                <div><p className="text-sm text-gray-500 mb-0.5">Processing Fee</p><p className="text-md font-semibold text-gray-800">{kfsOfferData.processingFeeType === 'percentage' ? `${kfsOfferData.processingFee}%` : `QAR ${formatCurrency(kfsOfferData.processingFee)}`}</p></div>
              </div>
              <p className="text-sm text-gray-600 pt-3">By clicking "Accept Offer", you confirm your agreement...</p>
            </div>
            <div className="flex justify-end items-center px-6 py-4 bg-gray-50 border-t border-gray-200 space-x-3">
              <button onClick={handleCloseKfsModal} type="button" className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${isAccepting ? 'bg-gray-200 text-gray-400 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`} disabled={isAccepting}>Cancel</button>
              <button onClick={handleModalConfirmAccept} type="button" className={`px-5 py-2 rounded-md text-sm font-medium text-white transition-colors flex items-center justify-center ${isAccepting ? 'bg-green-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700'}`} disabled={isAccepting}>
                {isAccepting ? <><svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Processing...</> : 'Accept Offer'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Contract Viewer Modal (Keep as is, no changes needed for this UI) */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-start justify-center bg-black bg-opacity-60 backdrop-blur-sm"
          style={{ paddingTop: '10px' }}
          onClick={handleCloseModal}
          role="dialog"
          aria-modal="true">
          <div className="bg-white rounded-lg shadow-xl p-0 max-w-4xl w-full mx-4 my-0 overflow-hidden flex flex-col"
            style={{ height: '95vh' }}
            onClick={(e) => e.stopPropagation()}>
            <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200">
              <h2 id="contractModalTitle" className="text-xl font-semibold text-gray-800">Credit Line Contract</h2>
              <button onClick={handleCloseModal} className="text-gray-400 hover:text-gray-700 text-3xl leading-none font-semibold focus:outline-none" aria-label="Close modal">&times;</button>
            </div>
            <div className="p-0 flex-grow" style={{ height: 'calc(95vh - 60px)' }}>
              {isGeneratingPdf && (
                <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
                  <div className="text-center">
                    <div className="w-12 h-12 border-4 border-[#208039] border-t-transparent rounded-full animate-spin mx-auto"></div>
                    <p className="mt-4 text-md text-gray-600">Generating contract...</p>
                  </div>
                </div>
              )}
              <iframe
                src={contractPdfUrl || '/creditLineContract.pdf'}
                title="Credit Line Contract PDF"
                className="w-full h-full border-0"
                style={{ display: 'block', margin: 0, padding: 0 }}>
                <div className="p-4 text-center text-gray-600">
                  <p>Your browser may not support viewing PDFs directly...</p>
                  <p className="mt-2">You can <a
                    href={contractPdfUrl || '/creditLineContract.pdf'}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline font-medium"
                  >download the contract here</a> instead.</p>
                </div>
              </iframe>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default DashboardOverview;