import axios from 'axios';
import config from "../config.json"
import { useRef, useState } from 'react';
import SharedCache from '../sharedCache';
import { uploadKycDocument } from '../api/kyc';
import { validateDocumentType } from '../utils/documentValidator';

export const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
};

/**
 * Shortens a document name if it's too long
 * @param {string} filename - The full document filename
 * @param {number} maxLength - Maximum length for the name part (default: 30)
 * @returns {string} - Shortened document name with extension
 */
export const shortenDocumentName = (filename, maxLength = 30) => {
    if (!filename) return 'Unknown';

    // Extract the extension
    const lastDotIndex = filename.lastIndexOf('.');

    // If there's no extension, just check the whole filename
    if (lastDotIndex === -1) {
        return filename.length > maxLength ? filename.substring(0, maxLength) + '...' : filename;
    }

    // Get the name without extension
    const nameWithoutExt = filename.substring(0, lastDotIndex);
    const extension = filename.substring(lastDotIndex); // Include the dot

    // If the name part is already short enough, return the original filename
    if (nameWithoutExt.length <= maxLength) {
        return filename;
    }

    // Otherwise, truncate the name part to maxLength and add the extension
    return nameWithoutExt.substring(0, maxLength) + '...' + extension;
};

export const processInvoiceUpload = async (file, panNo, gstin) => {
    const formData = new FormData();
    formData.append("document", file);

    try {
        // Call Mindee API for invoice processing
        const response = await axios.post(
            "https://api.mindee.net/v1/products/mindee/invoices/v4/predict",
            formData,
            {
                headers: {
                    Authorization: "a22f7e4051a9178de0f37d3d7a49b17c",
                    "Content-Type": "multipart/form-data"
                }
            }
        );

        const data = response.data.document.inference.prediction;

        // Extract invoice details
        const invoiceDetails = {
            // panNo: panNo,
            panNo: "**********",
            gstin: gstin,
            invoiceNumber: data.invoice_number?.value || 'N/A',
            invoiceDate: data.date?.value || 'N/A',
            dueDate: data.due_date?.value || 'N/A',
            totalAmount: data.total_amount?.value || 'N/A',
            supplierName: data.supplier_name?.value || 'N/A',
            customerName: data.customer_name?.value || 'N/A',
            billingAddress: data.billing_address?.value || 'N/A',
            customerAddress: data.customer_address?.value || 'N/A',
        };

        // Save invoice to backend
        const uploadFormData = new FormData();
        uploadFormData.append('pdfFile', file);

        // Append invoice details to form data
        Object.entries(invoiceDetails).forEach(([key, value]) => {
            uploadFormData.append(key, value);
        });
        uploadFormData.append('status', "pending");

        // const saveResponse = await axios.post(
        //     "https://node-fundfinaapi-************.asia-south1.run.app/invoiceFinancing/uploadInvoice",
        //     uploadFormData,
        //     {
        //         headers: {
        //             'Content-Type': 'multipart/form-data',
        //         },
        //     }
        // );

        return {
            success: true,
            invoiceDetails,
            saveResponse: invoiceDetails
        };

    } catch (error) {
        console.error("Error processing invoice: ", error);
        return {
            success: false,
            error: error.message
        };
    }
};

export const processBankStatementUpload = async (file, panNo, gstin) => {
    const formData = new FormData();
    formData.append("document", file);

    try {
        // Call Mindee API for bank statement processing
        const response = await axios.post(
            "https://api.mindee.net/v1/products/mindee/financial_document/v1/predict",
            formData,
            {
                headers: {
                    Authorization: "a22f7e4051a9178de0f37d3d7a49b17c",
                    "Content-Type": "multipart/form-data"
                }
            }
        );

        const prediction = response.data.document.inference.prediction;

        // Extract and format line items
        const lineItems = prediction.line_items.map(item => ({
            description: item.description || 'N/A',
            totalAmount: item.total_amount || 0,
            confidence: item.confidence || 0
        }));

        // Calculate actual total amount from line items
        const calculatedTotalAmount = lineItems.reduce(
            (sum, item) => sum + (item.totalAmount || 0),
            0
        );

        // Extract bank statement details with proper defaults
        const bankDetails = {
            panNo: panNo,
            gstin: gstin,
            customerName: prediction.customer_name?.value || 'N/A',
            customerId: prediction.customer_id?.value || 'N/A',
            supplierName: prediction.supplier_name?.value || 'N/A',
            supplierAddress: prediction.supplier_address?.value || 'N/A',
            supplierEmail: prediction.supplier_email?.value || 'N/A',
            // Use calculated total instead of prediction.total_amount
            totalAmount: calculatedTotalAmount,
            category: prediction.category?.value || 'N/A',
            locale: prediction.locale?.value || 'N/A',
            lineItems: lineItems,
            documentType: prediction.document_type?.value || 'N/A',
            confidence: prediction.total_amount?.confidence || 0,
            processingTime: response.data.document.inference.processing_time || 0
        };

        // Save bank statement to backend
        const uploadFormData = new FormData();
        uploadFormData.append('pdfFile', file);

        // Append bank statement details to form data
        Object.entries(bankDetails).forEach(([key, value]) => {
            if (key === 'lineItems') {
                uploadFormData.append(key, JSON.stringify(value));
            } else {
                uploadFormData.append(key, value.toString());  // Ensure all values are strings
            }
        });

        // const saveResponse = await axios.post(
        //     "https://node-fundfinaapi-************.asia-south1.run.app/invoiceFinancing/uploadBankStatement",
        //     uploadFormData,
        //     {
        //         headers: {
        //             'Content-Type': 'multipart/form-data',
        //         },
        //     }
        // );

        return {
            success: true,
            bankDetails,
            saveResponse: bankDetails
        };

    } catch (error) {
        console.error("Error processing bank statement: ", error);
        return {
            success: false,
            error: error.message
        };
    }
};

export const fetchCreditLine = async () => {
    try {
        // Using hardcoded PAN for now
        const panNo = "**********";
        const response = await axios.get(`${config.apiUrl}/invoiceFinancing/fetchCreditLine/${panNo}`);
        return response.data;
    } catch (error) {
        console.error("Error fetching credit line: ", error);
        return null;
    }
};

export const prepareKycData = (formState, user) => {
    return {
        // Top-level user fields
        userId: user?._id,
        firstName: formState.firstName,
        middleName: formState.middleName,
        lastName: formState.lastName,
        email: formState.email,
        mobileNo: formState.mobileNo,

        // Commercial registration and license (outside kyc)
        commercialRegistration: formState.commercialRegistration,
        licenseNumber: formState.licenseNumber,

        // KYC Object
        kyc: {
            // Personal Identification
            dateOfBirth: formState.dateOfBirth,
            gender: formState.gender,
            verificationStatus: formState.verificationStatus,

            // Address Verification
            addressLine1: formState.addressLine1,
            addressLine2: formState.addressLine2,
            city: formState.city,
            country: formState.country,
            state: formState.state,
            postalCode: formState.postalCode,

            // Employment and Income
            incomeDetails: {
                monthlyIncome: formState.monthlyIncome,
                monthlyExpenses: formState.personalMonthlyExpenses,
                sourceOfWealth: formState.sourceOfWealth,
                accountNumber: formState.accountNumber,
                ifscCode: formState.ifscCode,
            },

            // Family & Assets
            maritalStatus: formState.maritalStatus,
            hasChildren: formState.hasChildren,
            homeOwnership: formState.homeOwnership,
            vehicleOwnership: formState.vehicleOwnership,

            // Education & Background
            education: formState.education,
            sectorExperience: formState.sectorExperience,

            // Business Details (if applicable)
            businessDetails: {
                businessName: formState.businessName,
                businessAddressLine1: formState.businessAddressLine1,
                businessAddressLine2: formState.businessAddressLine2,
                businessCity: formState.businessCity,
                businessCountry: formState.businessCountry,

                businessType: formState.businessType,
                sector: formState.sector,
                ownershipType: formState.ownershipType,
                natureOfBusiness: formState.natureOfBusiness,
                yearEstablished: formState.yearEstablished,
                monthlyTurnover: formState.monthlyTurnover
            },

            // Additional Preferences
            hasWhatsapp: formState.hasWhatsapp,
            recentMajorPurchases: formState.recentMajorPurchases
        },

        // Optional collections
        buyers: formState.buyers,
        directors: formState.directors,
        authorizedSignatories: formState.authorizedSignatories,
        beneficialOwners: formState.beneficialOwners,

        // Flags
        isResident: formState.isResident,
        isBankStatementUploaded: formState.isBankStatementUploaded,
        isFirstInvoiceUploaded: formState.isFirstInvoiceUploaded,
        isCreditLineAssigned: formState.isCreditLineAssigned
    };
};

export const useDocumentUpload = (documentType, userId, onUploadSuccess) => {
    const [uploadStatus, setUploadStatus] = useState('idle');
    const [validationError, setValidationError] = useState('');
    const fileInputRef = useRef(null);

    const handleDocumentChange = async (file) => {
        if (!file) {
            // Reset document
            onUploadSuccess(null);
            SharedCache.set(documentType, {});
            setValidationError('');
            return;
        }

        if (!userId) {
            console.error('User ID is not available');
            return;
        }

        // Validate if the document is of the expected type
        const validation = validateDocumentType(file, documentType);

        if (!validation.isValid) {
            setValidationError(validation.message);
            setUploadStatus('error');
            return;
        }

        // If there's a warning but the document is still valid, we can proceed but show the warning
        if (validation.warning) {
            console.warn(`Document validation warning for ${documentType}: ${validation.warning}`);
            // You could set a warning state here if you want to display it to the user
        }

        setValidationError('');
        setUploadStatus('uploading');

        try {
            const formData = new FormData();
            formData.append('documentType', documentType);
            formData.append('file', file);
            formData.append('userId', userId);

            const response = await uploadKycDocument(userId, documentType, file);

            if (response.success) {
                const documentData = {
                    filePath: response.filePath,
                    signedUrl: response.signedUrl,
                };

                setUploadStatus('success');
                onUploadSuccess(documentData);
                SharedCache.set(documentType, documentData);
            } else {
                setUploadStatus('error');
                onUploadSuccess(null);
                // If the backend returned an error message, use it
                if (response.message) {
                    setValidationError(response.message);
                }
            }
        } catch (error) {
            console.error(`Error uploading ${documentType}:`, error);
            setUploadStatus('error');
            onUploadSuccess(null);
            setValidationError('An error occurred while uploading the document');
        }
    };

    const resetUploadStatus = () => {
        setUploadStatus('idle');
        setValidationError('');
    };

    return {
        fileInputRef,
        handleDocumentChange,
        uploadStatus,
        validationError,
        resetUploadStatus
    };
};

// Enhanced render function for documents
export const RenderDocument = (
    document,
    label,
    documentKey,
    handleDocumentChange,
    openDocumentModal,
    fileInputRef,
    uploadStatus,
    validationError
) => {
    const [documentName, setDocumentName] = useState('');

    const handleFileSelect = (e) => {
        const file = e.target.files[0];
        if (file) {
            setDocumentName(file.name);
            handleDocumentChange(file);
        }
    };

    const handleUpload = () => {
        if (documentName) {
            // Trigger upload for the selected file
            const fileInput = fileInputRef.current;
            if (fileInput) {
                fileInput.click();
            }
        }
    };

    // Ensure document is an object and has a filePath
    const hasDocument = document && document.filePath;
    const hasError = uploadStatus === 'error' && validationError;

    return (
        <div className="bg-blue-50 p-4 rounded-md mt-6">
            <h3 className="text-lg font-semibold mb-4">{label}</h3>
            <p className="mb-4 text-sm text-gray-600">
                Please upload your {label}.
            </p>
            <div className={`border ${hasError ? 'border-red-500' : 'border-gray-300'} rounded-md p-4`}>
                <div className="flex items-center justify-between">
                    <div>
                        <h4 className="font-medium">{label}</h4>
                        <p className="text-sm text-gray-500">
                            Accepted formats: JPEG, PNG, PDF. Max size: 5MB
                        </p>
                    </div>
                    <input
                        type="file"
                        ref={fileInputRef}
                        onChange={handleFileSelect}
                        className="hidden"
                        accept=".jpg,.jpeg,.png,.pdf"
                    />
                    {hasDocument ? (
                        <div className="flex items-center text-[#004141]">
                            <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span>Uploaded</span>
                            <button
                                type="button"
                                onClick={() => openDocumentModal(document.signedUrl || document.filePath)}
                                className="ml-2 px-3 py-1 bg-[#004141] text-white rounded-md  text-sm"
                            >
                                View
                            </button>
                            <button
                                type="button"
                                onClick={() => handleDocumentChange(null)}
                                className="ml-2 px-3 py-1 bg-[#004141] text-white rounded-md  text-sm"
                            >
                                Re-upload
                            </button>
                        </div>
                    ) : (
                        <button
                            type="button"
                            onClick={() => fileInputRef.current.click()}
                            className="px-4 py-2 bg-[#004141] text-white rounded-md"
                        >
                            Select File
                        </button>
                    )}
                </div>

                {documentName && !hasDocument && !hasError && (
                    <div className="mt-2 flex items-center justify-between">
                        <span className="text-sm">{documentName}</span>
                        <button
                            type="button"
                            onClick={handleUpload}
                            disabled={uploadStatus === 'uploading'}
                            className={`px-3 py-1 ${uploadStatus === 'uploading'
                                ? 'bg-gray-400 cursor-not-allowed'
                                : 'bg-[#004141] '
                                } text-white rounded-md text-sm`}
                        >
                            {uploadStatus === 'uploading' ? 'Uploading...' : 'Upload Now'}
                        </button>
                    </div>
                )}

                {hasError && (
                    <div className="mt-2">
                        <div className="flex items-center text-red-600">
                            <svg className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                            <span className="text-sm font-medium">{validationError}</span>
                        </div>
                        <button
                            type="button"
                            onClick={() => fileInputRef.current.click()}
                            className="mt-2 px-3 py-1 bg-red-600 text-white rounded-md text-sm"
                        >
                            Upload Different Document
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

// Updated renderDocumentsSection to pass additional props
export const renderDocumentsSection = (
    userData,
    openDocumentModal,
    handleDocumentChanges,
    fileInputRefs,
    uploadStatuses,
    validationErrors
) => {
    // Ensure userData exists before accessing its properties
    if (!userData) {
        return null;
    }

    const documentTypes = [
        { key: 'qatariId', label: 'Qatari ID' },
        { key: 'utilityBill', label: 'Utility Bill' },
        { key: 'ccrDocument', label: 'CCR Document' },
        { key: 'cashFlowDocument', label: 'Cash Flow Document' },
        { key: 'commercialRegistration', label: 'Commercial Registration' },
        { key: 'bankStatement', label: 'Business Bank Statement' }
    ];

    return (
        <div className="space-y-4">
            {documentTypes.map(({ key, label }) => (
                RenderDocument(
                    userData[key] || {},
                    label,
                    key,
                    handleDocumentChanges[key],
                    openDocumentModal,
                    fileInputRefs[key],
                    uploadStatuses[key],
                    validationErrors?.[key] || ''
                )
            ))}
        </div>
    );
};


//affinda working
// export const processBankStatementUpload = async (file, panNo, gstin) => {
//     try {
//         // Create form data for file upload
//         const formData = new FormData();
//         formData.append('file', file);
//         formData.append('collection', 'CtMXWgyQ');
//         formData.append('wait', 'true');

//         // Call Affinda API for bank statement processing
//         const response = await axios.post(
//             'https://api.affinda.com/v3/documents',
//             formData,
//             {
//                 headers: {
//                     'Authorization': 'Bearer aff_012230be267d6c8c99f24e2437b020c7ab590a0b',
//                     'Accept': 'application/json'
//                 }
//             }
//         );

//         // Extract data from Affinda response
//         const documentData = response.data.data;

//         // Extract line items (transactions)
//         const lineItems = [];
//         if (documentData.tablesBeta && documentData.tablesBeta.length > 0) {
//             documentData.tablesBeta.forEach(table => {
//                 if (table.parsed && table.parsed.rows) {
//                     table.parsed.rows.forEach(row => {
//                         if (row.parsed) {
//                             // Extract amounts and descriptions
//                             const amount = row.parsed.itemBaseTotalBeta?.parsed ||
//                                 row.parsed.itemPaidAmount?.parsed ||
//                                 row.parsed.itemTotalBeta?.parsed;
//                             const description = row.raw;
//                             const confidence = row.parsed.itemBaseTotalBeta?.confidence ||
//                                 row.parsed.itemPaidAmount?.confidence ||
//                                 row.parsed.itemTotalBeta?.confidence || 0;

//                             if (amount && description) {
//                                 lineItems.push({
//                                     description: description,
//                                     totalAmount: parseFloat(amount),
//                                     confidence: confidence
//                                 });
//                             }
//                         }
//                     });
//                 }
//             });
//         }

//         // Calculate total amount from line items
//         const totalAmount = lineItems.reduce((sum, item) => sum + item.totalAmount, 0);

//         // Prepare bank statement details
//         const bankDetails = {
//             panNo,
//             gstin,
//             customerName: documentData.customerCompanyName?.parsed || 'N/A',
//             customerId: documentData.customerNumber?.parsed || 'N/A',
//             supplierName: documentData.supplierCompanyName?.parsed || 'N/A',
//             supplierAddress: documentData.supplierAddress?.parsed?.formatted || 'N/A',
//             supplierEmail: documentData.supplierEmail?.parsed || 'N/A',
//             totalAmount: totalAmount,
//             category: 'BANK_STATEMENT',
//             locale: documentData.currencyCode?.parsed?.value || 'N/A',
//             lineItems: lineItems,
//             documentType: 'BANK_STATEMENT',
//             confidence: documentData.ocrConfidence || 0,
//             processingTime: response.data.meta.readyDt ?
//                 new Date(response.data.meta.readyDt) - new Date(response.data.meta.createdDt) : 0,
//             status: 'VERIFICATION_PENDING'
//         };

//         // Save bank statement to backend
//         const uploadFormData = new FormData();
//         uploadFormData.append('pdfFile', file);

//         // Append bank statement details to form data
//         Object.entries(bankDetails).forEach(([key, value]) => {
//             if (key === 'lineItems') {
//                 uploadFormData.append(key, JSON.stringify(value));
//             } else {
//                 uploadFormData.append(key, value.toString());
//             }
//         });

//         // const saveResponse = await axios.post(
//         //     "https://node-fundfinaapi-************.asia-south1.run.app/invoiceFinancing/uploadBankStatement",
//         //     uploadFormData,
//         //     {
//         //         headers: {
//         //             'Content-Type': 'multipart/form-data',
//         //         },
//         //     }
//         // );

//         return {
//             success: true,
//             bankDetails,
//             saveResponse: bankDetails
//         };

//     } catch (error) {
//         console.error("Error processing bank statement: ", error);
//         if (error.response) {
//             console.error("API Error Details:", error.response.data);
//         }
//         return {
//             success: false,
//             error: error.message,
//             details: error.response?.data
//         };
//     }
// };