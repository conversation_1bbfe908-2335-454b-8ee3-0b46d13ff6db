import config from "../../config.json"

export const API_ENDPOINTS = {
  MINDEE_INVOICE: "https://api.mindee.net/v1/products/mindee/invoices/v4/predict",
  UPLOAD_INVOICE: `${config.apiUrl}/invoiceFinancing/uploadInvoice`
};

export const BANKS = [
  { id: 'bank1', name: 'Bank 1' },
  { id: 'bank2', name: 'Bank 2' },
  { id: 'bank3', name: 'Bank 3' }
];

export const FILE_TYPES = {
  INVOICE: '.pdf',
  BANK_STATEMENT: ['pdf', 'csv']
};

export const DEFAULT_TIMER = 119; // 2 minutes in seconds

export const STATUS = {
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected'
};