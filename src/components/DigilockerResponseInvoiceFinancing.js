import React, { useEffect, useState } from 'react';
import { useLocation, Redirect } from 'react-router-dom';
import queryString from 'query-string';
import axios from 'axios';
import config from '../config.json';
import SharedCache from '../sharedCache';

const SpinnerComponent = ({ showSpinner }) => {
    if (!showSpinner) return null;
    return (
        <div className="flex justify-center items-center">
            <div className="spinner">Loading...</div>
        </div>
    );
};

const DigilockerResponseInvoiceFinancing = () => {
    const location = useLocation();
    const [state, setState] = useState({
        digio_doc_id: '',
        status: '',
        message: '',
        showSpinner: false,
        redirectPath: null,
        redirectState: null,
        error: null
    });

    const updateDigilockerResponse = async (digio_doc_id, status, message) => {
        try {
            const token = SharedCache.get("user")?.token;
            const response = await axios.post(config.apiUrl + '/ops/auth/updateDigilockerResponse', {
                digio_doc_id,
                status,
                message
            }, {
                headers: {
                    'x-auth-token': token
                }
            });

            // Update KYC status in SharedCache
            const currentUser = SharedCache.get("user");
            if (currentUser) {
                currentUser.kycCompleted = true;
                SharedCache.update("user", currentUser);
            }

            return response.data;
        } catch (error) {
            console.error('Error updating digilocker response:', error);
            return {
                success: false,
                message: error.response?.data?.message || 'KYC verification failed'
            };
        }
    };

    useEffect(() => {
        const handleDigilockerResponse = async () => {
            const values = queryString.parse(location.search);
            const digio_doc_id = values.digio_doc_id || '';
            const status = values.status || '';
            const message = values.message || '';

            setState(prev => ({
                ...prev,
                digio_doc_id,
                status,
                showSpinner: true
            }));

            if (!digio_doc_id) {
                setState(prev => ({
                    ...prev,
                    error: 'Invalid parameters provided',
                    showSpinner: false
                }));
                return;
            }

            try {
                const result = await updateDigilockerResponse(digio_doc_id, status, message);

                if (result.success) {
                    setState(prev => ({
                        ...prev,
                        redirectPath: '/invoice-financing/personal-details',
                        redirectState: {
                            status: 'success',
                            message: 'KYC verification completed successfully',
                            kycCompleted: true
                        },
                        showSpinner: false
                    }));
                } else {
                    setState(prev => ({
                        ...prev,
                        error: result.message || 'KYC verification failed',
                        showSpinner: false
                    }));
                }
            } catch (error) {
                setState(prev => ({
                    ...prev,
                    error: 'Something went wrong during KYC verification',
                    showSpinner: false
                }));
            }
        };

        handleDigilockerResponse();
    }, [location]);

    if (state.redirectPath) {
        return (
            <Redirect
                to={{
                    pathname: state.redirectPath,
                    state: state.redirectState
                }}
            />
        );
    }

    if (state.error) {
        return (
            <div className="flex justify-center items-center min-h-screen bg-[#208039]">
                <div className="error-container bg-white p-8 rounded-md shadow-lg max-w-md w-full m-4">
                    <h2 className="text-2xl font-bold text-red-600 mb-4">KYC Verification Failed</h2>
                    <p className="text-gray-700 mb-6">{state.error}</p>
                    <button
                        onClick={() => window.location.href = '/login'}
                        className="w-full bg-[#004141] text-white py-2 px-4 rounded-md hover:bg-neutral-800 transition-colors"
                    >
                        Return to Login
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="flex justify-center items-center min-h-screen bg-[#208039]">
            <div className="bg-white p-8 rounded-md shadow-lg max-w-md w-full m-4">
                <SpinnerComponent showSpinner={state.showSpinner} />
                {state.status === 'success' && (
                    <div className="text-center">
                        <p className="text-lg font-bold text-gray-800 mb-4">
                            Please wait while we complete your KYC verification...
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default DigilockerResponseInvoiceFinancing;