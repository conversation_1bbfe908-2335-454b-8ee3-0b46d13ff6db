import React, { useState, useEffect } from "react";
import { useHistory, useLocation } from "react-router-dom";
import InvoiceUpload from "./screens/InvoiceUpload";
import BankStatementUpload from "./screens/BankStatements";
import Consent from "./screens/Consent";
import SharedCache from "../sharedCache";

const GSTNPage = () => {
    const location = useLocation();
    const panNo = location.state?.panNo ?? "**********";
    const history = useHistory();

    const [isConsentStep, setIsConsentStep] = useState(false);
    const [isBankStatementUploaded, setIsBankStatementUploaded] = useState(false);
    const [isInvoiceUploaded, setIsInvoiceUploaded] = useState(false);

    // Hardcoded GSTIN for testing - replace with actual GSTIN when integrating
    const gstin = "TEST123456789";

    useEffect(() => {
        const user = SharedCache.get("user");
        if (user) {
            setIsBankStatementUploaded(user.isBankStatementUploaded);
            setIsInvoiceUploaded(user.isFirstInvoiceUploaded);
        }
    }, []);

    const handleConsentProceed = () => {
        history.push({
            pathname: "/dashboard"
        });
    };

    const renderCurrentStep = () => {
        if (isConsentStep) {
            return (
                <Consent
                    onProceed={handleConsentProceed}
                />
            );
        }

        if (!isBankStatementUploaded) {
            return (
                <BankStatementUpload
                    panNo={panNo}
                    gstin={gstin}
                    setIsBankStatementUploaded={setIsBankStatementUploaded}
                    setIsInvoiceUploaded={setIsInvoiceUploaded}
                />
            );
        }

        if (!isInvoiceUploaded) {
            return (
                <InvoiceUpload
                    panNo={panNo}
                    gstin={gstin}
                    setIsConsentStep={setIsConsentStep}
                    setIsInvoiceUploaded={setIsInvoiceUploaded}
                />
            );
        }
    };

    return (
        <div className="flex justify-center items-center min-h-screen bg-[#208039] font-poppins relative">
            <div className="relative flex flex-col items-center px-6 py-6 max-w-full bg-white rounded-md w-[577px] max-md:px-4 h-[82vh] overflow-hidden">
                <div className="flex flex-col mt-4 items-center max-w-full w-[550px]">
                    <img
                        loading="lazy"
                        src={require("../images/logo.jpg")}
                        className="max-w-full w-[174px]"
                        alt="Header"
                    />
                    {renderCurrentStep()}
                </div>

                <div className="mt-auto w-full">
                    <div className="text-xs font-bold leading-6 text-stone-900 text-center">
                        Your data is 100% safe with us
                    </div>
                    <div className="text-xs font-bold leading-6 text-stone-900 text-center">
                        © Madad Fintech. All rights reserved
                    </div>
                </div>
            </div>
        </div>
    );
};

export default GSTNPage;