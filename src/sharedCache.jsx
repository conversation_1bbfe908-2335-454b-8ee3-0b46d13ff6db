class SharedCache {
    static tokenKey = "token";
    static user = "user"; // Generic user object
    static msmeUser = "msmeUser"; // <<< ADD THIS DEFINITION
    static buyerFullData = "buyerFullData";
    static partner = "partner";
    static hideFFLogo = "hideFFLogo";
    static userType = "userType";
    static app = null;
    static cache = null;

    static init(data) {
        if (this.cache == null) {
            this.cache = data;
        } else {
            this.cache = { ...this.cache, ...data };
        }

        if (data.token) {
            localStorage.setItem(this.tokenKey, data.token);
        }
        if (data.user) {
            localStorage.setItem(this.user, JSON.stringify(data.user));
        }
        if (data.msmeUser) { // <<< ADD THIS BLOCK
            localStorage.setItem(this.msmeUser, JSON.stringify(data.msmeUser));
        }
        if (data.buyerFullData) {
            localStorage.setItem(this.buyerFullData, JSON.stringify(data.buyerFullData));
        }
        if (data.partner) {
            localStorage.setItem(this.partner, JSON.stringify(data.partner));
        }
        if (data.userType) {
            localStorage.setItem(this.userType, data.userType);
        }
    }

    static get(key) {
        if (this.cache && this.cache[key] !== undefined) {
            return this.cache[key];
        }

        if (key === this.tokenKey || key === this.userType) {
            const data = localStorage.getItem(key);
            if (data) {
                if (!this.cache) this.cache = {};
                this.cache[key] = data;
                return data;
            }
         // Ensure `msmeUser` is handled similarly to other JSON objects
        } else if (key === this.user || key === this.partner || key === this.buyerFullData || key === this.msmeUser) { // <<< ADD this.msmeUser HERE
            const data = localStorage.getItem(key);
            if (data) {
                try {
                    const parsedData = JSON.parse(data);
                    if (!this.cache) this.cache = {};
                    this.cache[key] = parsedData;
                    return parsedData;
                } catch (e) {
                    console.error(`Error parsing ${key} from localStorage:`, e);
                }
            }
        }
        return null;
    }

    static set(key, value) {
        if (this.cache == null) {
            this.cache = {};
        }
        this.cache[key] = value;

        if (key === this.tokenKey || key === this.userType) {
            localStorage.setItem(key, value);
         // Ensure `msmeUser` is handled similarly to other JSON objects
        } else if (key === this.user || key === this.partner || key === this.buyerFullData || key === this.msmeUser) { // <<< ADD this.msmeUser HERE
            localStorage.setItem(key, JSON.stringify(value));
        }
    }

    static remove(key) {
        if (this.cache && this.cache[key] !== undefined) {
            delete this.cache[key];
        }
        localStorage.removeItem(key); // This is fine, will remove if exists
    }

    static clear() {
        this.cache = null;
        localStorage.removeItem(this.tokenKey);
        localStorage.removeItem(this.user);
        localStorage.removeItem(this.msmeUser); // <<< ADD THIS
        localStorage.removeItem(this.buyerFullData);
        localStorage.removeItem(this.partner);
        localStorage.removeItem(this.hideFFLogo);
        localStorage.removeItem(this.userType);
    }

    static isAuthenticated() {
        return !!this.get(this.tokenKey);
    }
}

export default SharedCache;