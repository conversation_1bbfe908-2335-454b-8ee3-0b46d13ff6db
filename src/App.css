html, body, #root {
  height: 100%;
  margin: 0;
}

.flex-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.flex-content {
  flex: 1;
}

.App {
  text-align: center;
}

.App-logo {
  animation: App-logo-spin infinite 20s linear;
  height: 80px;
}

.App-header {
  background-color: #222;
  height: 150px;
  padding: 20px;
  color: white;
}

.App-title {
  font-size: 1.5em;
}

.App-intro {
  font-size: large;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.partnerImage {
  width: 100%;
  filter: brightness(50%);
}

.developerLink {
  color: whitesmoke !important;
  text-decoration: underline;
  cursor: pointer;
}

@media screen and (max-width: 1900px) {
  .imgText {
    font-size: 15px;
    color: white !important;
    height: 60%;
    padding: 10px;
    width: 100%;
  }

  .img-title {
    position: absolute;
    color: white !important;
    text-align: center;
    left: 0;
    right: 0;
    top: 15%;
    letter-spacing: 3px;
  }

  .imgHeader {
    color: white !important;
    font-size: 40px;
    text-align: center;
    width: 100%;
  }

  .city-title {
    position: absolute;
    color: white !important;
    text-align: center;
    left: 0;
    right: 0;
    top: 60%;
    letter-spacing: 3px;
    font-size: 20px;
  }

  .img-responsive {
    min-height: 450px;
  }

  .img-responsive {
    max-height: 500px;
  }

  #menuDropdownList {
    overflow-y: auto;
    max-height: auto;
  }
}

@media screen and (max-width: 1300px) {
  .imgText {
    font-size: 14px;
    color: white !important;
    height: 45%;
    padding: 10px;
    width: 100%;
  }

  .img-title {
    position: absolute;
    color: white !important;
    text-align: center;
    left: 0;
    right: 0;
    letter-spacing: 3px;
  }

  .imgHeader {
    color: white !important;
    font-size: 30px;
    text-align: center;
    width: 100%;
  }

  .city-title {
    position: absolute;
    color: white !important;
    text-align: center;
    left: 0;
    right: 0;
    top: 60%;
    letter-spacing: 3px;
    font-size: 20px;
  }

  .img-responsive {
    min-height: 450px;
  }

  .img-responsive {
    max-height: 500px;
  }

  #menuDropdownList {
    overflow-y: auto;
    max-height: auto;
  }
}

@media screen and (max-width: 1000px) {
  .imgText {
    font-size: 14px;
    color: white !important;
    height: 45%;
    padding: 10px;
    width: 100%;
  }

  .img-title {
    position: absolute;
    color: white !important;
    text-align: center;
    left: 0;
    right: 0;
    top: 5%;
    letter-spacing: 3px;
  }

  .imgHeader {
    color: white !important;
    font-size: 20px;
    text-align: center;
    width: 100%;
  }

  .city-title {
    position: absolute;
    color: white !important;
    text-align: center;
    left: 0;
    right: 0;
    top: 60%;
    letter-spacing: 3px;
    font-size: 15px;
  }

  .img-responsive {
    min-height: 450px;
  }

  .img-responsive {
    max-height: 500px;
  }

  #menuDropdownList {
    overflow-y: auto;
    max-height: auto;
  }
}

@media screen and (max-width: 500px) {
  #menuDropdownList {
    overflow-y: scroll;
    max-height: 250px;
  }
}

.imgbox {
  position: relative;
}

.noBottom {
  margin-bottom: 0rem !important;
}

.parnterLogo {
  max-width: 200px !important;
  margin-right: 50px !important;
}

.react-autosuggest__container {
  position: relative;
}

.react-autosuggest__input {
  width: 240px;
  height: 30px;
  padding: 10px 20px;
  font-weight: 300;
  font-size: 16px;
  border: 1px solid #aaa;
  border-radius: 4px;
}

.react-autosuggest__input--focused {
  outline: none;
}

.react-autosuggest__input--open {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.react-autosuggest__suggestions-container {
  display: none;
}

.react-autosuggest__suggestions-container--open {
  display: block;
  position: absolute;
  width: 100%;
  border: 1px solid #aaa;
  background-color: #fff;
  font-weight: 300;
  font-size: 16px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  z-index: 2;
  max-height: 200px;
  overflow-y: scroll;
}

.react-autosuggest__suggestions-list {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

.react-autosuggest__suggestion {
  cursor: pointer;
  padding: 10px 20px;
}

.react-autosuggest__suggestion--highlighted {
  background-color: #ddd;
}

.custom-checkbox .custom-control-input:checked~.custom-control-label::before {
  background-color: black !important;
}

::-webkit-input-placeholder {
  text-transform: initial;
}

:-moz-placeholder {
  text-transform: initial;
}

::-moz-placeholder {
  text-transform: initial;
}

:-ms-input-placeholder {
  text-transform: initial;
}

.value {
  color: black !important;
}

.partnerLogo {
  max-width: 200px;
  margin-right: 50px;
}

.bold {
  font-weight: bold;
}

.modal-90w {
  width: 90%;
}

.disabled a {
  pointer-events: none;
  background-color: lightgray;
}

.black {
  color: black !important;
}

.header-border {
  width: 50px;
  height: 3px;
  background: #000;
  margin-left: -25px;
  left: 50%;
  bottom: 0;
  position: absolute;
}

.carousel .slide {
  background: transparent !important;
}

.stickyColumn {
  top: 0;
  position: sticky;
  z-index: 11;
}

.otpStyle {
  width: 2.4rem !important;
  height: 2.4rem;
  margin: 0 0.5rem;
  font-size: 1rem;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.3);
}

.otp-field {
  width: 38px;
  margin-right: 10px;
  padding-left: 12px;
  height: 38px;
}

.pincode-card {
  padding: 0.3rem;
  background: #d3d3d38f;
  border-radius: 0.375rem;
  margin-right: 1rem;
}

.month-array {
  border: 1px solid #d3d3d336;
}

.remove-icon {
  display: inline-block;
  border-radius: 10rem;
  background: white;
  cursor: pointer;
  padding: 0.2rem;
}

.active-month {
  color: white;
}

.month-card {
  position: absolute;
  border-radius: 4px;
  -webkit-box-shadow: 0 3px 15px rgb(0 0 0 / 20%);
  box-shadow: 0 3px 15px rgb(0 0 0 / 20%);
  width: 320px;
  margin: auto;
  padding: 15px;
  background-color: #fff;
  z-index: 99999;
  margin-left: 1rem;
  margin-top: 0.5rem;
}

.remove-icon-month-year {
  position: absolute;
  right: 0;
  top: 0;
  margin-top: 0.7rem;
  margin-right: 0.3rem;
  cursor: pointer;
  align-self: center;
}

.month-picker-container {
  position: relative;
}

.image-thumbnail {
  cursor: pointer;
  width: 100px;
  border-radius: 5px;
  max-height: 100px;
  border: 1px solid #b6b1b1;
}

.image-thumbnail:hover {
  /* opacity: 40%; */
}

.image-hover-wrapper {
  color: white;
  position: absolute;
  border-radius: 5px;
  left: 90px;
  top: -10px;
  z-index: 999;
}

.close-icon {
  color: red;
  cursor: pointer;
}

.horizontal-scrollable {
  overflow-x: auto;
  white-space: nowrap;
}

.max-width-250 {
  max-width: 300px;
}

.fit-content {
  width: -webkit-fill-available;
}

.border-dot-line {
  border-bottom: 2px solid gray;
  border-bottom-style: dashed;
}

.custom-size-modal {
  /* max-width: 992px !important; */
  /* width: 90% !important; */
}

.embed-click-wrapper {
  position: absolute;
  top: 0;
  height: 100px;
  width: 100px;
  z-index: 998;
}

.iframe-wrapper {
  height: 750px;
}

.font-20 {
  font-size: 20px;
}

.font-24 {
  font-size: 24px;
}

.bold {
  font-weight: 700;
}

.custom-tab {
  cursor: pointer;
  font-weight: 600;
  border-bottom: 2px solid #e9e6e6;
  font-size: 16px;

}

.custom-active-tab {
  border-bottom-color: #B70B18 !important;
  color: #B70B18 !important;
}

.chart-wrapper-md {
  width: 720px;
}

.border-lite {
  border: 1px solid #e9e6e6
}

.badge-red {
  background-color: #EE5762;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.vv-video-tutorial-web {
  display: relative;
}

.vv-video-tutorial-mobile {
  display: none;

}

.hr-lines:before {
  content: " ";
  display: block;
  height: 1px;
  width: 47%;
  position: absolute;
  top: 50%;
  left: 0;
  background: rgb(208, 208, 208);
}

.hr-lines:after {
  content: " ";
  height: 1px;
  width: 47%;
  background: rgb(208, 208, 208);
  display: block;
  position: absolute;
  top: 50%;
  right: 0;
}

@media only screen and (max-width: 600px) {
  .vv-video-tutorial-web {
    display: none;
  }

  .vv-video-tutorial-mobile {
    display: block;

  }
}

.value-center {
  display: flex;
  justify-content: center;
  align-items: center;
}