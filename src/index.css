@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@theme {
  --font-nunito: "Nunito", sans-serif;
}

body {
  margin: 0;
  padding: 0 0 0 0;
  font-family: "Nunito", sans-serif !important;
}

html,
body {
  max-width: 100% !important;
  overflow-x: hidden !important;
}

.container {
  margin-top: 70px;
}

.navbar {
  margin-bottom: 0px;
}

.clickable {
  cursor: pointer;
}

.carousel .item {
  height: 500px;
}

.item img {
  position: absolute;
  object-fit: cover;
  top: 0;
  left: 0;
  min-height: 500px;
}

a {
  color: #208039 !important;
}

.fundfinaColor {
  color: #208039 !important;
}

.fundfinaColorBold {
  color: #208039 !important;
  font-weight: 600;
}

h6 {
  color: #208039 !important;
  /* text-decoration: underline; */
}


.underline {
  text-decoration: underline;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}

.cardsImg {
  width: 100px;
  margin-left: 38%;
  height: 110px;
}

.testimonialImg {
  width: 80px;
  margin-left: 40%;
  height: 90px;
}

.testimonialSliderImg {
  width: 100px !important;
  height: 100px !important;
  padding-top: 10px;
}

.testimonialSliderImgHorizontal {
  border-radius: 50%;
  padding-top: 10px;
}

.cardsImgFarmer {
  width: 250px;
  margin-left: 18%;
  height: 110px;
}

.cardHeader {
  color: #208039 !important;
  font-weight: bolder;
}

.breadcrumListItem {
  display: flex;
  flex-wrap: wrap;
  padding: 0rem 1.5rem;
  list-style: none;
  color: white !important;
}

.breadcrumListItemLink {
  color: white !important;
}

.breadcrumHeader {
  background-color: #208039 !important;
}

.ff-button {
  background-color: #208039 !important;
  color: white !important;
}

.ff-button-success {
  color: white !important;
}

.trueScore1-button {
  background-color: #208039 !important;
  color: white !important;
  text-align: center;
  width: 70%;
  text-transform: none;
  white-space: pre-wrap;
}

.trueScore2-button {
  background-color: #208039 !important;
  color: white !important;
  text-align: center;
  width: 40%;
}

@media (min-width: 1200px) {
  .card-deck .card {
    display: -ms-flexbox;
    display: flex;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
    -ms-flex-direction: column;
    flex-direction: column;
    margin-right: 15px;
    margin-bottom: 0;
    margin-left: 15px;
  }
}

.ecww-more {
  background: #333;
  font: normal 13px/1 Helvetica, Arial, Verdana, Sans-serif;
  padding: 10px 0;
  color: #fff;
  text-align: center;
  width: 100%;
  clear: both;
  margin: 0;
  clear: both;
  float: left;
}

.ecww-widget {
  position: relative;
  padding-top: 0;
  padding-bottom: 280px;
  height: 0;
  overflow: hidden;
}

.ecww-widgetwrapper {
  min-width: 250px;
  width: 100%;
}

.list-inline li a i {
  font-size: 30px;
}

.fb {
  color: #3b5998 !important;
}

.twitter {
  color: #00acee !important;
}

.linkedIn {
  color: #0e76a8 !important;
}

.nav_link {
  width: 100%;
  text-align: left;
}

.nav_link:hover {
  background-color: linen;
}

@media (min-width: 1200px) {
  .nav_link {
    display: block;
    width: 100%;
    padding: 0.25rem 1.5rem;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: left !important;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
  }

  .nav_link_header {
    display: block;
    width: 100%;
    padding: 0.25rem 1.5rem 0rem 0.5rem;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: left !important;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
  }
}

@media (min-width: 100px) {
  .nav_link {
    display: block;
    width: 100%;
    padding: 0.25rem 1.5rem 0rem 0.5rem !important;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: left !important;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
  }

  .nav_link_header {
    display: block;
    width: 100%;
    padding: 0.25rem 1.5rem 0rem 0.5rem !important;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: left !important;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
  }

  .nav-link-fixed {
    text-align: center;
  }

  .navlink-anchor {
    padding: 0.5rem 1rem;
  }
}

.navlink-anchor {
  padding: 0.5rem 1rem;
}

.navlink-anchor:hover {
  text-decoration: none !important;
}

.activeHeader {
  color: darkred !important;
  font-weight: bolder;
}

.teamImg {
  border-radius: 50%;
}

.testimonialImg {
  border-radius: 50%;
}

.teamHeader {
  text-align: center;
  color: #208039 !important;
}

.teamText {
  font-size: 15px;
  min-height: 160px;
}

.testimonialText {
  min-height: 190px;
}

.partnertestimonialText {
  min-height: 330px !important;
}

.partnertestimonialFooter {
  min-height: 75px !important;
}

.hidden {
  display: none;
}

.spinner {
  position: fixed;
  z-index: 1031;
  top: 50%;
  left: 50%;
}

.faqCardsInvestImg {
  margin-top: auto;
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 70%;
}

.faqCardsBorrowImg {
  margin-top: 5%;
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 60%;
}

.faqCardsAboutImg {
  margin-top: 20%;
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 90%;
}

.faqCardsContactImg {
  margin-top: 15%;
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 50%;
}

.header-border {
  width: 50px;
  height: 3px;
  background: #000;
  margin-left: -25px;
  left: 50%;
  bottom: 0;
  position: absolute;
}

.linkEWS {
  color: white !important;
  text-decoration: underline;
}

.google {
  padding: 10px 10px 5px 0px;
}

@media (min-width: 300px) {
  .facebook {
    min-height: 80px !important;
  }
}

@media (max-width: 500px) {
  .testimonialSliderImg {
    margin-left: 35% !important;
  }

  .testimonialSliderImgHorizontal {
    margin-left: 35% !important;
  }
}



.dot {
  background-color: #208039 !important;
}

#mc_embed_signup {
  background: #fff;
  clear: left;
  font: 14px Helvetica, Arial, sans-serif;
}

/* .sticky-nav {
  position: sticky;
  top: 0;
} */

.green {
  text-decoration: underline;
}

.red {
  color: red !important;
  text-decoration: underline;
}

.orange {
  color: orange !important;
  text-decoration: underline;
}

.black {
  color: black !important;
  text-decoration: underline;
}

.contractPhotoThumb {
  width: 60px !important;
  height: 60px !important;
}

.navActive {
  color: #208039 !important;
  font-weight: bold;
}

.navInActive {
  color: grey !important;
}

.trBreakColor {
  background-color: white !important;
  border-left: 0px !important;
  border-right: 0px !important;
  width: 100% !important;
}

.page-item.active .page-link {
  background-color: #208039 !important;
  color: white !important;
}

.page-item .page-link {
  background-color: white !important;
  color: #208039 !important;
}

.filter-icon-wrapper {
  display: inline-block;
  padding: 5px 10px;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  border-radius: 5px;
  cursor: pointer;
  position: relative;

}

.filter-icon-active {
  box-shadow: rgb(0 0 0 / 24%) 3px 8px 5px 0px;
  border-radius: 5px 5px 0px 0px;
}

.filter-list {
  position: absolute;
  width: 500px;
  z-index: 9999;
  overflow: hidden;
  background: white;
  padding: 25px;
  right: 0;
  margin-right: 15px;
  box-shadow: rgb(0 0 0 / 24%) 3px 4px 8px;
  border-radius: 5px 0px 5px 5px;
}

.filter-dot {
  height: 5px;
  width: 5px;
  background-color: #208039 !important;
  border-radius: 50%;
  display: inline-block;
  position: absolute;
  right: 0;
  margin-right: 8px;
}

.text-selection-none {
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/**Hot fix for file browser text overflow*/
.custom-file-label {
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 80px;
}

/**Hot Fix for calander to avoid go behind in popup**/
.tether-element.tether-element-attached-top.tether-element-attached-center.tether-target-attached-bottom.tether-target-attached-center.tether-enabled {
  z-index: 999999 !important;
}

.error-row {
  background-color: rgba(244, 27, 27, 0.05) !important;
}

.cursor-pointer {
  cursor: pointer;
}

.invalid-input {
  border: 1px solid #208039 !important;
}

.tele-calling-tab .active {
  color: #208039 !important;
  border-bottom: 2px solid #208039;
}

.tele-calling-devider {
  border-bottom: 1px solid lightgray;
  padding-bottom: 1rem;
}

.settlement-model {
  width: auto;
}

.custom-menu {
  max-height: 30rem !important;
  overflow: auto;
}

.drop-area {
  background: #efefef;
  display: flex;
  width: 100%;
  height: 15rem;
  justify-content: center;
  align-items: center;
}

.paymentMode label {
  color: #000 !important;
}

.align-center {
  align-items: center;
}

.loan-repayment-table-view {
  max-height: 28rem;
}