// Advanced Buyer Name Matching Utility
// Handles complex string matching for buyer names with fuzzy matching capabilities

import _ from 'lodash';

/**
 * Normalizes a company name for comparison
 * @param {string} name - The company name to normalize
 * @returns {string} - Normalized name
 */
const normalizeCompanyName = (name) => {
    if (!name || typeof name !== 'string') return '';

    return name
        // Convert to lowercase
        .toLowerCase()
        // Remove all non-alphanumeric characters except spaces
        .replace(/[^a-z0-9\s]/g, '')
        // Replace multiple spaces with single space
        .replace(/\s+/g, ' ')
        // Remove common business suffixes and prefixes
        .replace(/\b(ltd|limited|llc|inc|incorporated|corp|corporation|co|company|group|enterprises|trading|traders|international|intl|pvt|private|pte|plc|sa|gmbh|bv|srl|spa|ag|ab|oy|as|kft|sro|doo|ood|eood|sl|sl|sarl|eurl|ei|eireli|ltda|s\.?a\.?|s\.?l\.?|b\.?v\.?|n\.?v\.?|a\.?g\.?|g\.?m\.?b\.?h\.?|o\.?o\.?o\.?|z\.?a\.?o\.?|t\.?o\.?v\.?|s\.?p\.?z\.?o\.?o\.?|a\.?s\.?|o\.?y\.?|a\.?b\.?|k\.?f\.?t\.?)\b/g, '')
        // Remove "the" at the beginning
        .replace(/^the\s+/, '')
        // Remove extra spaces and trim
        .replace(/\s+/g, ' ')
        .trim();
};

/**
 * Generates variations of a company name for matching
 * @param {string} name - The original company name
 * @returns {Array<string>} - Array of name variations
 */
const generateNameVariations = (name) => {
    if (!name) return [];

    const normalized = normalizeCompanyName(name);
    const variations = new Set([normalized]);

    // Add version without spaces
    variations.add(normalized.replace(/\s+/g, ''));

    // Add version with abbreviated words
    const words = normalized.split(' ').filter(word => word.length > 0);

    // Generate combinations by removing/abbreviating words
    if (words.length > 1) {
        // Try removing each word one at a time
        words.forEach((_, index) => {
            const withoutWord = words.filter((__, i) => i !== index).join(' ');
            if (withoutWord.length > 2) {
                variations.add(withoutWord);
                variations.add(withoutWord.replace(/\s+/g, ''));
            }
        });

        // Try first letters of each word
        const firstLetters = words.map(word => word.charAt(0)).join('');
        if (firstLetters.length >= 2) {
            variations.add(firstLetters);
        }

        // Try combinations of first word + first letters of others
        if (words.length > 2) {
            const firstWordPlusAbbrev = words[0] + words.slice(1).map(w => w.charAt(0)).join('');
            variations.add(firstWordPlusAbbrev);
        }

        // Try partial matches (first part of compound words)
        words.forEach(word => {
            if (word.length > 4) {
                // Add first 3-4 characters of longer words
                variations.add(word.substring(0, Math.min(4, word.length - 1)));
            }
        });
    }

    return Array.from(variations).filter(v => v.length > 1);
};

/**
 * Calculates Levenshtein distance between two strings
 * @param {string} str1 - First string
 * @param {string} str2 - Second string
 * @returns {number} - Edit distance
 */
const levenshteinDistance = (str1, str2) => {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
        for (let i = 1; i <= str1.length; i++) {
            const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
            matrix[j][i] = Math.min(
                matrix[j][i - 1] + 1,     // deletion
                matrix[j - 1][i] + 1,     // insertion
                matrix[j - 1][i - 1] + indicator  // substitution
            );
        }
    }

    return matrix[str2.length][str1.length];
};

/**
 * Calculates similarity ratio between two strings
 * @param {string} str1 - First string
 * @param {string} str2 - Second string
 * @returns {number} - Similarity ratio (0-1)
 */
const calculateSimilarity = (str1, str2) => {
    if (!str1 || !str2) return 0;
    if (str1 === str2) return 1;

    const maxLength = Math.max(str1.length, str2.length);
    if (maxLength === 0) return 1;

    const distance = levenshteinDistance(str1, str2);
    return (maxLength - distance) / maxLength;
};

/**
 * Calculates Jaccard similarity for sets of words
 * @param {string} str1 - First string
 * @param {string} str2 - Second string
 * @returns {number} - Jaccard similarity (0-1)
 */
const jaccardSimilarity = (str1, str2) => {
    const set1 = new Set(str1.split(' ').filter(w => w.length > 0));
    const set2 = new Set(str2.split(' ').filter(w => w.length > 0));

    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);

    return union.size === 0 ? 0 : intersection.size / union.size;
};

/**
 * Advanced matching function that finds the best match for a buyer name
 * @param {string} targetName - The name to match against
 * @param {Array<Object>} buyersList - Array of buyer objects with buyerName property
 * @param {Object} options - Matching options
 * @returns {Object|null} - Best matching buyer or null
 */
export const findBestBuyerMatch = (targetName, buyersList, options = {}) => {
    const {
        exactMatchThreshold = 0.95,    // Threshold for considering a match "exact"
        goodMatchThreshold = 0.8,      // Threshold for considering a match "good"
        minMatchThreshold = 0.6,       // Minimum threshold to consider a match
        enableFuzzyMatching = true,    // Enable fuzzy matching
        enableVariationMatching = true, // Enable variation matching
        maxResults = 1                 // Maximum number of results to return
    } = options;

    if (!targetName || !buyersList || !Array.isArray(buyersList)) {
        return null;
    }

    const targetVariations = enableVariationMatching ? generateNameVariations(targetName) : [normalizeCompanyName(targetName)];
    const matches = [];

    buyersList.forEach(buyer => {
        if (!buyer.buyerName) return;

        const buyerVariations = enableVariationMatching ? generateNameVariations(buyer.buyerName) : [normalizeCompanyName(buyer.buyerName)];
        let bestScore = 0;
        let bestMatchType = 'none';

        // Check for exact matches first
        for (const targetVar of targetVariations) {
            for (const buyerVar of buyerVariations) {
                if (targetVar === buyerVar) {
                    bestScore = 1;
                    bestMatchType = 'exact';
                    break;
                }
            }
            if (bestScore === 1) break;
        }

        // If no exact match and fuzzy matching is enabled
        if (bestScore < exactMatchThreshold && enableFuzzyMatching) {
            for (const targetVar of targetVariations) {
                for (const buyerVar of buyerVariations) {
                    // Calculate multiple similarity metrics
                    const editSimilarity = calculateSimilarity(targetVar, buyerVar);
                    const jaccardSim = jaccardSimilarity(targetVar, buyerVar);

                    // Combined score with weights
                    const combinedScore = (editSimilarity * 0.7) + (jaccardSim * 0.3);

                    if (combinedScore > bestScore) {
                        bestScore = combinedScore;
                        bestMatchType = combinedScore >= exactMatchThreshold ? 'exact' :
                            combinedScore >= goodMatchThreshold ? 'good' : 'partial';
                    }
                }
            }
        }

        if (bestScore >= minMatchThreshold) {
            matches.push({
                buyer,
                score: bestScore,
                matchType: bestMatchType,
                confidence: bestScore >= exactMatchThreshold ? 'high' :
                    bestScore >= goodMatchThreshold ? 'medium' : 'low'
            });
        }
    });

    // Sort by score descending
    matches.sort((a, b) => b.score - a.score);

    return maxResults === 1 ? (matches[0]?.buyer || null) : matches.slice(0, maxResults);
};

/**
 * Simple wrapper function for backward compatibility
 * @param {string} targetName - The name to match
 * @param {Array<Object>} buyersList - Array of buyers
 * @returns {Object|null} - Matched buyer or null
 */
export const matchBuyerName = (targetName, buyersList) => {
    return findBestBuyerMatch(targetName, buyersList);
};

/**
 * Get multiple potential matches with confidence scores
 * @param {string} targetName - The name to match
 * @param {Array<Object>} buyersList - Array of buyers
 * @param {number} maxResults - Maximum number of results
 * @returns {Array<Object>} - Array of potential matches with scores
 */
export const getPotentialMatches = (targetName, buyersList, maxResults = 5) => {
    return findBestBuyerMatch(targetName, buyersList, { maxResults });
};

/**
 * Debug function to see how names are being processed
 * @param {string} name - Name to analyze
 * @returns {Object} - Debug information
 */
export const debugNameProcessing = (name) => {
    const normalized = normalizeCompanyName(name);
    const variations = generateNameVariations(name);

    return {
        original: name,
        normalized,
        variations: variations.filter(v => v !== normalized),
        allVariations: variations
    };
};

// Example usage and test cases
export const testMatching = () => {
    const testBuyers = [
        { _id: '1', buyerName: 'BIG TRADERS LLC' },
        { _id: '2', buyerName: 'Global Systems International' },
        { _id: '3', buyerName: 'Tech Solutions Pvt Ltd' },
        { _id: '4', buyerName: 'ABC Trading Company' },
        { _id: '5', buyerName: 'XYZ Corp' }
    ];

    const testCases = [
        'bigtrader',
        'bigtrade',
        'bi trade',
        'BIG TRADERS',
        'big traders llc',
        'global systems',
        'globalsys',
        'tech solutions',
        'techsol',
        'abc trading',
        'xyz'
    ];

    console.log('=== Buyer Matching Test Results ===');
    testCases.forEach(testName => {
        const match = findBestBuyerMatch(testName, testBuyers);
        const debug = debugNameProcessing(testName);

        console.log(`\nTest: "${testName}"`);
        console.log(`Normalized: "${debug.normalized}"`);
        console.log(`Variations: [${debug.variations.join(', ')}]`);
        console.log(`Match: ${match ? `"${match.buyerName}" (ID: ${match._id})` : 'No match'}`);
    });

    // Test with multiple results
    console.log('\n=== Multiple Match Results ===');
    const multipleMatches = getPotentialMatches('big trade', testBuyers, 3);
    multipleMatches.forEach((match, index) => {
        console.log(`${index + 1}. "${match.buyer.buyerName}" (Score: ${match.score.toFixed(3)}, Confidence: ${match.confidence})`);
    });
};

// Default export for the main matching function
export default findBestBuyerMatch;