/**
 * Document Validator Utility
 *
 * This utility provides functions to validate if uploaded documents are of the expected type
 * using keyword/regex pattern matching on filenames and potentially document content.
 */

/**
 * Common keyword groups that can be reused across document types
 */
const COMMON_KEYWORDS = {
  // Common negative keywords for most document types
  RESUME_KEYWORDS: ['resume', 'cv', 'curriculum', 'vitae']
};

/**
 * Document type keyword patterns
 * Maps document types to arrays of keywords that might indicate the document is of that type
 * and arrays of keywords that might indicate the document is NOT of that type
 *
 * Using a more focused approach with fewer keywords for better accuracy
 */
const DOCUMENT_TYPE_PATTERNS = {
  // Business Documents
  commercialRegistration: {
    positiveKeywords: ['commercial', 'registration', 'certificate', 'incorporation', 'cr'],
    negativeKeywords: [...COMMON_KEYWORDS.RESUME_KEYWORDS]
  },
  tradeLicense: {
    positiveKeywords: ['trade', 'license', 'licence', 'permit'],
    negativeKeywords: [...COMMON_KEYWORDS.RESUME_KEYWORDS]
  },
  taxCard: {
    positiveKeywords: ['tax', 'card', 'vat', 'gst', 'tin'],
    negativeKeywords: [...COMMON_KEYWORDS.RESUME_KEYWORDS]
  },
  establishmentCard: {
    positiveKeywords: ['establishment', 'card'],
    negativeKeywords: [...COMMON_KEYWORDS.RESUME_KEYWORDS]
  },
  memorandumOfAssociation: {
    positiveKeywords: ['memorandum', 'association', 'moa'],
    negativeKeywords: [...COMMON_KEYWORDS.RESUME_KEYWORDS]
  },
  articleOfAssociation: {
    positiveKeywords: ['article', 'association', 'aoa', 'bylaws'],
    negativeKeywords: [...COMMON_KEYWORDS.RESUME_KEYWORDS]
  },

  // Financial Documents
  bankStatement: {
    positiveKeywords: ['bank', 'statement', 'account'],
    negativeKeywords: [...COMMON_KEYWORDS.RESUME_KEYWORDS]
  },
  auditedFinancialReport: {
    positiveKeywords: ['audit', 'financial', 'report'],
    negativeKeywords: [...COMMON_KEYWORDS.RESUME_KEYWORDS]
  },
  cashFlowLedger: {
    positiveKeywords: ['cash', 'flow', 'ledger'],
    negativeKeywords: [...COMMON_KEYWORDS.RESUME_KEYWORDS]
  },
  commercialCreditReport: {
    positiveKeywords: ['credit', 'report', 'score'],
    negativeKeywords: [...COMMON_KEYWORDS.RESUME_KEYWORDS]
  },

  // Personal Identity Documents
  qatariId: {
    positiveKeywords: ['qatar', 'qatari', 'id', 'national'],
    negativeKeywords: [...COMMON_KEYWORDS.RESUME_KEYWORDS]
  },
  passport: {
    positiveKeywords: ['passport', 'travel'],
    negativeKeywords: [...COMMON_KEYWORDS.RESUME_KEYWORDS]
  },
  utilityBill: {
    positiveKeywords: ['utility', 'bill', 'electricity', 'water'],
    negativeKeywords: [...COMMON_KEYWORDS.RESUME_KEYWORDS]
  },

  // Invoice Documents
  invoice: {
    positiveKeywords: ['invoice', 'bill', 'receipt'],
    negativeKeywords: [...COMMON_KEYWORDS.RESUME_KEYWORDS]
  },

  // Generic document type for additional documents
  otherDocument: {
    positiveKeywords: ['document', 'attachment', 'supporting', 'additional'],
    negativeKeywords: [...COMMON_KEYWORDS.RESUME_KEYWORDS]
  }
};

/**
 * Validates if a file is likely to be of the expected document type
 *
 * @param {File} file - The file object to validate
 * @param {string} expectedDocType - The expected document type
 * @returns {Object} - Validation result with success flag and message
 */
export const validateDocumentType = (file, expectedDocType) => {
  if (!file || !expectedDocType) {
    return {
      isValid: false,
      message: 'File or document type not provided'
    };
  }

  // Get the patterns for the expected document type
  const patterns = DOCUMENT_TYPE_PATTERNS[expectedDocType];
  if (!patterns) {
    console.warn(`No validation patterns defined for document type: ${expectedDocType}`);
    return {
      isValid: true,
      message: 'No validation patterns available for this document type'
    };
  }

  // Check filename for keywords
  const fileName = file.name.toLowerCase();

  // Check for negative keywords first (stronger signal)
  // Only check for resume keywords as they're the most problematic
  const containsNegativeKeyword = patterns.negativeKeywords.some(keyword => fileName.includes(keyword));
  if (containsNegativeKeyword) {
    const matchedKeyword = patterns.negativeKeywords.find(keyword => fileName.includes(keyword));
    return {
      isValid: false,
      message: `This document appears to be a resume/CV, not a ${formatDocTypeName(expectedDocType)}. Please upload the correct document.`,
      detectedKeyword: matchedKeyword
    };
  }

  // Check for positive keywords (weaker signal, as filenames might not contain these)
  const containsPositiveKeyword = patterns.positiveKeywords.some(keyword => fileName.includes(keyword));

  // If filename contains positive keywords, it's more likely to be the right document
  if (containsPositiveKeyword) {
    return {
      isValid: true,
      message: 'Document appears to be of the correct type'
    };
  }

  // If no clear signals from the filename, return a neutral result
  // Being more lenient here - if it's not clearly a resume, we'll accept it
  return {
    isValid: true,
    message: 'Document accepted',
    warning: 'Please ensure this is the correct document type'
  };
};

/**
 * Formats a document type key into a human-readable name
 *
 * @param {string} docType - The document type key
 * @returns {string} - Formatted document type name
 */
const formatDocTypeName = (docType) => {
  if (!docType) return '';

  // Handle special cases
  if (docType === 'qatariId') return 'Qatari ID';
  if (docType === 'utilityBill') return 'Utility Bill';

  // General case: convert camelCase to Title Case with spaces
  return docType
    // Insert a space before all uppercase letters
    .replace(/([A-Z])/g, ' $1')
    // Replace "Of" with "of" for proper title case
    .replace(/ Of/g, ' of')
    // Capitalize first letter
    .replace(/^./, str => str.toUpperCase())
    .trim();
};

/**
 * Checks if a document might be a resume/CV based on filename
 * This is a common case that should be checked for all document types
 *
 * @param {File} file - The file object to check
 * @returns {boolean} - True if the file appears to be a resume/CV
 */
export const isLikelyResume = (file) => {
  if (!file) return false;

  const fileName = file.name.toLowerCase();
  return COMMON_KEYWORDS.RESUME_KEYWORDS.some(keyword => fileName.includes(keyword));
};

// Create the exports object
const documentValidator = {
  validateDocumentType,
  isLikelyResume
};

export default documentValidator;
