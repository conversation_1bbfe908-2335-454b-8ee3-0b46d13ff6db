import CryptoJS from 'crypto-js';

const SECRET_KEY_FRONTEND = '5b9e0f2c4a8d1e7f3b6c9a0d5e8f1c7e2a4b9d0f3e6c8a1d2b5e0f7a9c3d4e6f';
const HMAC_KEY_FRONTEND = '9f7d2c1a8e0b3d6f5c4a7e9b0d1f2a3e8c7b6d5a4e3f2c1b0d9e8f7a6c5d4e3f';

export function decryptData(encryptedData, encryptionKey = SECRET_KEY_FRONTEND, hmacKey = HMAC_KEY_FRONTEND) {
    try {
        // Validate the input format for authenticated encryption
        if (typeof encryptedData !== 'object' || encryptedData === null ||
            !encryptedData.ciphertext || typeof encryptedData.ciphertext !== 'string' ||
            !encryptedData.mac || typeof encryptedData.mac !== 'string') {
            console.error("Decryption failed: Invalid encrypted data format. Expected an object like { ciphertext: string, mac: string }.");
            return null;
        }

        const { ciphertext, mac: receivedMac } = encryptedData;

        // Step 1: Verify the HMAC first to ensure data integrity and authenticity
        const expectedMac = CryptoJS.HmacSHA256(ciphertext, hmacKey).toString();

        if (expectedMac !== receivedMac) {
            console.error("Decryption failed: Data integrity check failed (HMAC mismatch). Data may have been tampered with or the wrong HMAC key was used.");
            return null; // Reject the data if HMAC doesn't match
        }

        // Step 2: If HMAC matches, proceed with decryption
        const bytes = CryptoJS.AES.decrypt(ciphertext, encryptionKey);
        const decryptedString = bytes.toString(CryptoJS.enc.Utf8);

        // If the decrypted string is empty, it means the encryption key is likely wrong or data is corrupt.
        if (!decryptedString) {
            console.error("Decryption failed: Resulted in an empty string. Please check the encryption key or the encrypted data itself.");
            return null;
        }

        // Parse the decrypted string back into a JavaScript object
        return JSON.parse(decryptedString);
    } catch (error) {
        console.error("Decryption failed:", error);
        return null; // Return null to indicate failure
    }
}

/**
 * Masks sensitive parts of strings or object values recursively.
 *
 * @param {any} data The data to mask (string, number, array, or object).
 * @param {object} [options={}] Configuration options for masking.
 * @param {string} [options.maskChar='*'] The character to use for masking.
 * @param {number} [options.revealStart=4] Number of characters to reveal at the beginning.
 * @param {number} [options.revealEnd=4] Number of characters to reveal at the end.
 * @param {string[]} [options.ignoreKeys=[]] An array of object keys whose values should not be masked.
 * @returns {any} The masked data.
 */
export function maskData(data, options = {}) {
    const defaultOptions = {
        maskChar: '*',
        revealStart: 4,
        revealEnd: 4,
        ignoreKeys: []
    };
    const config = { ...defaultOptions, ...options };

    const maskString = (str) => {
        if (typeof str !== 'string' || str.length === 0) {
            return str;
        }

        const { maskChar, revealStart, revealEnd } = config;
        const totalLength = str.length;

        // If the string is too short to reveal parts, mask the whole thing
        if (totalLength <= revealStart + revealEnd) {
            return maskChar.repeat(totalLength);
        }

        const startPart = str.substring(0, revealStart);
        const endPart = str.substring(totalLength - revealEnd, totalLength);
        const maskedPartLength = totalLength - revealStart - revealEnd;

        return startPart + maskChar.repeat(maskedPartLength) + endPart;
    };

    // Handle null or undefined data
    if (data === null || typeof data === 'undefined') {
        return data;
    }

    // Handle string data
    if (typeof data === 'string') {
        return maskString(data);
    }

    // Handle number data by converting to string first
    if (typeof data === 'number') {
        return maskString(String(data));
    }

    // Handle array data by recursively masking each item
    if (Array.isArray(data)) {
        return data.map(item => maskData(item, options));
    }

    // Handle object data by recursively masking values, respecting ignoreKeys
    if (typeof data === 'object') {
        const maskedObject = {};
        for (const key in data) {
            // Ensure it's an own property to avoid masking prototype chain properties
            if (Object.prototype.hasOwnProperty.call(data, key)) {
                if (config.ignoreKeys.includes(key)) {
                    // If key is in ignoreKeys, copy value directly
                    maskedObject[key] = data[key];
                } else {
                    // Otherwise, recursively mask the value
                    maskedObject[key] = maskData(data[key], options);
                }
            }
        }
        return maskedObject;
    }

    // Return data as is if its type is not handled (e.g., boolean, function, symbol)
    return data;
}