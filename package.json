{"name": "new_app", "version": "0.1.0", "private": true, "dependencies": {"@aws-sdk/client-textract": "^3.723.0", "@fortawesome/fontawesome-free": "^6.6.0", "@heroicons/react": "^2.2.0", "@react-oauth/google": "^0.12.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "autoprefixer": "^10.4.19", "axios": "^1.7.2", "bootstrap": "^4.5.0", "country-codes-flags-phone-codes": "^1.1.1", "crypto-js": "^4.0.0", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "heroicons": "^2.2.0", "lottie-react": "^2.4.1", "moment": "^2.26.0", "query-string": "^9.1.1", "react": "^18.3.1", "react-bootstrap": "^1.6.8", "react-dom": "^18.3.1", "react-idle-timer": "^4.3.6", "react-ipgeolocation": "^1.4.0", "react-modal": "^3.16.3", "react-number-format": "^4.4.1", "react-router-dom": "^5.2.0", "react-scripts": "^5.0.1", "react-select": "^5.8.0", "react-toastify": "^11.0.5", "react-window": "^1.8.10", "recharts": "^2.15.2", "string-similarity": "^4.0.4", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "node ./update-build.js && react-scripts build", "test": "react-scripts test --env=jsdom", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-router-dom": "^5.3.3", "tailwindcss": "^3.4.4"}, "buildDate": 1750925272584}